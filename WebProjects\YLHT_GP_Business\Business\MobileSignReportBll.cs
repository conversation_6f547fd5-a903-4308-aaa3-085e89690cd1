using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using YLHT.GP.Common;
using YLHT.GP.Models;
using YLHT_GP_Business.Interface;

namespace YLHT_GP_Business.Business
{
    public class MobileSignReportBll
    {

        /// <summary>
        /// 获取移动签名报备列表
        /// </summary>
        /// <param name="model">查询条件</param>
        /// <param name="page">当前页</param>
        /// <param name="rows">每页显示条数</param>
        /// <param name="sort">排序字段</param>
        /// <param name="order">排序方式</param>
        /// <param name="rowsCount">总记录数</param>
        /// <param name="pageCount">总页数</param>
        /// <returns></returns>
        public List<MobileSignReportModel> GetMobileSignReportList(MobileSignReportModel model, int page, int rows, string sort, string order, out int rowsCount, out int pageCount)
        {
            return BaseBLL.DataSource.GetMobileSignReportList(model, page, rows, sort, order, out rowsCount, out pageCount);
        }

        /// <summary>
        /// 根据ID获取移动签名报备
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public MobileSignReportModel GetMobileSignReportById(int id)
        {
            return BaseBLL.DataSource.GetMobileSignReportById(id);
        }

        /// <summary>
        /// 添加移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        public bool AddMobileSignReport(MobileSignReportModel model)
        {
            return BaseBLL.DataSource.AddMobileSignReport(model);
        }

        /// <summary>
        /// 更新移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        public bool UpdateMobileSignReport(MobileSignReportModel model)
        {
            return BaseBLL.DataSource.UpdateMobileSignReport(model);
        }

        /// <summary>
        /// 删除移动签名报备
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public bool DeleteMobileSignReport(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id)) return false;
                return BaseBLL.DataSource.DeleteMobileSignReport(Convert.ToInt32(id));
            }
            catch (Exception ex)
            {
                Trace.TraceError($"DeleteMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 批量删除移动签名报备
        /// </summary>
        /// <param name="model">批量删除模型</param>
        /// <returns></returns>
        public bool BatchDeleteMobileSignReport(MobileSignReportModel model)
        {
            return BaseBLL.DataSource.BatchDeleteMobileSignReport(model);
        }

        /// <summary>
        /// 查询符合删除条件的记录数量
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>符合条件的记录数</returns>
        public int CountRecordsForDelete(int channelId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                return BaseBLL.DataSource.CountRecordsForDelete(channelId, startDate, endDate);
            }
            catch (Exception ex)
            {
                Trace.TraceError(string.Format("CountRecordsForDelete方法异常，ex={0}", ex));
                return 0;
            }
        }

        /// <summary>
        /// 按条件批量删除移动签名报备
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>删除的记录数</returns>
        public int BatchDeleteByCondition(int channelId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                return BaseBLL.DataSource.BatchDeleteByCondition(channelId, startDate, endDate);
            }
            catch (Exception ex)
            {
                Trace.TraceError(string.Format("BatchDeleteByCondition方法异常，ex={0}", ex));
                return 0;
            }
        }

        /// <summary>
        /// 检查移动签名报备是否存在
        /// </summary>
        /// <param name="sign">签名</param>
        /// <param name="userId">用户ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="extNumber">扩展号</param>
        /// <param name="content">内容</param>
        /// <returns></returns>
        public bool CheckMobileSignReport(string sign, string userId, string channelId, string extNumber, string content)
        {
            return BaseBLL.DataSource.CheckMobileSignReport(sign, userId, channelId, extNumber, content);
        }

        /// <summary>
        /// 导出移动签名报备为Excel文件
        /// </summary>
        /// <param name="model">查询条件</param>
        /// <returns>Excel文件的字节数组，如果出错则返回null</returns>
        public byte[] ExportToExcel(MobileSignReportModel model)
        {
            try
            {
                // 获取所有数据
                int rowsCount, pageCount;
                List<MobileSignReportModel> list = BaseBLL.DataSource.GetMobileSignReportList(model, 1, 10000, "id", "desc", out rowsCount, out pageCount);

                if (list == null || list.Count == 0)
                {
                    return null;
                }

                // 创建Excel工作簿
                HSSFWorkbook workbook = new HSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("移动签名报备");

                // 创建表头样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.Boldweight = (short)NPOI.SS.UserModel.FontBoldWeight.Bold;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightYellow.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;
                headerStyle.BorderBottom = BorderStyle.Thin;
                headerStyle.BorderLeft = BorderStyle.Thin;
                headerStyle.BorderRight = BorderStyle.Thin;
                headerStyle.BorderTop = BorderStyle.Thin;
                headerStyle.Alignment = HorizontalAlignment.Center;
                headerStyle.VerticalAlignment = VerticalAlignment.Center;

                // 创建内容样式
                ICellStyle contentStyle = workbook.CreateCellStyle();
                contentStyle.BorderBottom = BorderStyle.Thin;
                contentStyle.BorderLeft = BorderStyle.Thin;
                contentStyle.BorderRight = BorderStyle.Thin;
                contentStyle.BorderTop = BorderStyle.Thin;

                // 创建日期样式
                ICellStyle dateStyle = workbook.CreateCellStyle();
                dateStyle.BorderBottom = BorderStyle.Thin;
                dateStyle.BorderLeft = BorderStyle.Thin;
                dateStyle.BorderRight = BorderStyle.Thin;
                dateStyle.BorderTop = BorderStyle.Thin;
                IDataFormat format = workbook.CreateDataFormat();
                dateStyle.DataFormat = format.GetFormat("yyyy-MM-dd");

                // 创建表头，与导入模板保持一致
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = new string[] {
                    "日期", "类型", "客户名称", "签名类型:企业简称/APP/商标", "签名",
                    "签名企业名称", "统一社会信用代码", "法人", "法人/经办人姓名", "法人/经办人身份证号",
                    "法人/经办人手机号（必须是本人实名验证，否则无法通过校验）", "引流链接（1个子端口对应1个链接）",
                    "引流号码1", "引流号码2",
                    "签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）",
                    "身份证照片(反正面)", "身份证材料1", "身份证材料2", "身份证材料3", "身份证材料4", "身份证材料5", "扩展号", "内容"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                    sheet.SetColumnWidth(i, 20 * 256); // 设置列宽
                }

                // 填充数据
                for (int i = 0; i < list.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1);
                    var item = list[i];

                    // 日期
                    ICell dateCell = dataRow.CreateCell(0);
                    if (item.ReportDate.HasValue)
                    {
                        dateCell.SetCellValue(item.ReportDate.Value);
                        dateCell.CellStyle = dateStyle;
                    }
                    else
                    {
                        dateCell.SetCellValue("");
                        dateCell.CellStyle = contentStyle;
                    }

                    // 按照新的列顺序填充数据
                    CreateCell(dataRow, 1, item.Type, contentStyle);
                    CreateCell(dataRow, 2, item.AccountName, contentStyle);
                    CreateCell(dataRow, 3, item.SignType, contentStyle);
                    CreateCell(dataRow, 4, item.Sign, contentStyle);
                    CreateCell(dataRow, 5, item.SignCompanyName, contentStyle);
                    CreateCell(dataRow, 6, item.UnifiedSocialCreditCode, contentStyle);
                    CreateCell(dataRow, 7, item.LegalPerson, contentStyle);
                    CreateCell(dataRow, 8, item.LegalPersonName, contentStyle);
                    CreateCell(dataRow, 9, item.LegalPersonIdCard, contentStyle);
                    CreateCell(dataRow, 10, item.LegalPersonPhone, contentStyle);
                    CreateCell(dataRow, 11, item.DrainageLink, contentStyle);
                    CreateCell(dataRow, 12, item.DrainageNumber1, contentStyle);
                    CreateCell(dataRow, 13, item.DrainageNumber2, contentStyle);

                    // 处理签名材料和身份证照片字段
                    // 对于签名材料
                    if (!string.IsNullOrEmpty(item.SignMaterials))
                    {
                        // 尝试插入图片和DISPIMG公式
                        bool success = InsertImageAndDispimgFormula(item.SignMaterials, workbook, sheet, dataRow.RowNum, 14);

                        if (!success)
                        {
                            // 如果插入失败，则只显示路径
                            CreateCell(dataRow, 14, item.SignMaterials, contentStyle);
                        }
                    }
                    else
                    {
                        CreateCell(dataRow, 14, "", contentStyle);
                    }

                    // 对于身份证照片 - 将多个身份证图片分别放到不同列
                    string[] idCardImages = new string[0];
                    if (!string.IsNullOrEmpty(item.IdCardPhotos))
                    {
                        // 按分号分割多个图片URL
                        idCardImages = item.IdCardPhotos.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                    }

                    // 身份证照片(反正面) - 第15列
                    if (idCardImages.Length > 0)
                    {
                        bool success = InsertImageAndDispimgFormula(idCardImages[0], workbook, sheet, dataRow.RowNum, 15);
                        if (!success)
                        {
                            CreateCell(dataRow, 15, idCardImages[0], contentStyle);
                        }
                    }
                    else
                    {
                        CreateCell(dataRow, 15, "", contentStyle);
                    }

                    // 身份证材料1-5 - 第16-20列
                    for (int imgIndex = 1; imgIndex <= 5; imgIndex++)
                    {
                        int colIndex = 15 + imgIndex; // 16, 17, 18, 19, 20
                        if (idCardImages.Length > imgIndex)
                        {
                            bool success = InsertImageAndDispimgFormula(idCardImages[imgIndex], workbook, sheet, dataRow.RowNum, colIndex);
                            if (!success)
                            {
                                CreateCell(dataRow, colIndex, idCardImages[imgIndex], contentStyle);
                            }
                        }
                        else
                        {
                            CreateCell(dataRow, colIndex, "", contentStyle);
                        }
                    }

                    CreateCell(dataRow, 21, item.ExtNumber, contentStyle);
                    CreateCell(dataRow, 22, item.Content, contentStyle);
                }

                // 将Excel写入内存流并返回字节数组
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError("ExportToExcel 导出失败：" + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// 创建单元格并设置值和样式
        /// </summary>
        private void CreateCell(IRow row, int columnIndex, string value, ICellStyle style)
        {
            ICell cell = row.CreateCell(columnIndex);
            cell.SetCellValue(value ?? "");
            cell.CellStyle = style;
        }







        /// <summary>
        /// 将图片嵌入到Excel单元格中，使用DISPIMG公式
        /// </summary>
        /// <param name="imagePath">图片的本地路径或URL，支持多个图片用分号分隔</param>
        /// <param name="workbook">Excel工作簿</param>
        /// <param name="sheet">Excel工作表</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="columnIndex">列索引</param>
        /// <returns>是否成功</returns>
        private bool InsertImageAndDispimgFormula(string imagePath, IWorkbook workbook, ISheet sheet, int rowIndex, int columnIndex)
        {
            try
            {
                Trace.TraceInformation($"开始处理图片插入: {imagePath}");

                if (string.IsNullOrEmpty(imagePath))
                {
                    Trace.TraceWarning("图片路径为空");
                    return false;
                }

                // 处理多个图片路径（用分号分隔），只处理第一张图片
                string[] imagePaths = imagePath.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                if (imagePaths.Length == 0)
                {
                    return false;
                }

                string firstImagePath = imagePaths[0].Trim();

                // 获取图片的物理路径
                string physicalPath = GetPhysicalImagePath(firstImagePath);
                Trace.TraceInformation($"处理图片路径: {firstImagePath} -> {physicalPath}");

                if (string.IsNullOrEmpty(physicalPath) || !System.IO.File.Exists(physicalPath))
                {
                    Trace.TraceWarning($"图片文件不存在: {firstImagePath} -> {physicalPath}");
                    return false;
                }

                // 获取或创建行
                IRow dataRow = sheet.GetRow(rowIndex);
                if (dataRow == null)
                {
                    dataRow = sheet.CreateRow(rowIndex);
                }

                // 设置行高，确保单元格有足够的显示空间
                dataRow.Height = (short)(256 * 6); // 6倍标准行高

                // 设置列宽，确保单元格有足够的显示空间
                sheet.SetColumnWidth(columnIndex, 35 * 256); // 35个字符宽度

                // 获取或创建单元格
                ICell cell = dataRow.GetCell(columnIndex);
                if (cell == null)
                {
                    cell = dataRow.CreateCell(columnIndex);
                }

                // 创建单元格样式
                ICellStyle style = workbook.CreateCellStyle();
                style.BorderBottom = BorderStyle.Thin;
                style.BorderLeft = BorderStyle.Thin;
                style.BorderRight = BorderStyle.Thin;
                style.BorderTop = BorderStyle.Thin;
                style.Alignment = HorizontalAlignment.Center;
                style.VerticalAlignment = VerticalAlignment.Center;
                cell.CellStyle = style;

                // 检查工作簿类型
                Trace.TraceInformation($"工作簿类型: {workbook.GetType().Name}");

                // 只支持XLS格式（HSSFWorkbook）
                if (!(workbook is HSSFWorkbook hssfWorkbook))
                {
                    Trace.TraceError("图片插入只支持XLS格式，当前格式不支持");
                    cell.SetCellType(CellType.String);
                    cell.SetCellValue("[格式不支持]");
                    return false;
                }

                Trace.TraceInformation("工作簿类型检查通过，开始插入图片");

                // 读取图片数据
                byte[] imageBytes;
                PictureType pictureType;
                int pictureIdx;

                try
                {
                    // 读取实际图片
                    imageBytes = System.IO.File.ReadAllBytes(physicalPath);
                    pictureType = GetPictureType(physicalPath);
                    Trace.TraceInformation($"读取图片数据: {imageBytes.Length} 字节, 类型: {pictureType}");

                    // 将图片添加到工作簿
                    pictureIdx = workbook.AddPicture(imageBytes, pictureType);
                    Trace.TraceInformation($"图片添加到工作簿，索引: {pictureIdx}");
                }
                catch (Exception imgEx)
                {
                    Trace.TraceError($"读取图片失败: {imgEx.Message}");
                    return false;
                }

                // 基于百度AI的说明，正确实现DISPIMG函数
                try
                {
                    // 关键：DISPIMG需要动态生成的图片ID，每次导入都会生成新ID
                    // 参考成功示例：=DISPIMG("In_2501A6987CD0431D95CA0A3293C91F09",1)
                    // 参考百度AI说明：=DISPIMG("ID_8BCTD5BECC7D48766A6BB679A8D9EAC49",1)

                    // 生成符合Excel标准的图片ID，使用与成功示例相似的格式
                    string pictureId = $"In_{Guid.NewGuid().ToString("N").ToUpper()}";
                    Trace.TraceInformation($"生成图片ID: {pictureId}");

                    // 步骤1：创建命名引用，将图片ID与实际图片数据关联
                    // 这是DISPIMG函数工作的关键
                    try
                    {
                        HSSFName name = hssfWorkbook.CreateName() as HSSFName;
                        if (name != null)
                        {
                            name.NameName = pictureId;

                            // 根据百度AI的说明，图片ID需要正确关联到Excel内部的图片资源
                            // 尝试多种可能的引用格式
                            string[] referenceFormats = {
                                $"#PICTURE.{pictureIdx}",            // Excel内部图片引用格式
                                $"Pictures!Picture{pictureIdx}",     // 引用Pictures工作表中的图片
                                $"Picture{pictureIdx}",              // 简单的图片引用
                                pictureIdx.ToString()                // 纯索引引用
                            };

                            bool referenceCreated = false;
                            foreach (string refFormat in referenceFormats)
                            {
                                try
                                {
                                    name.RefersToFormula = refFormat;
                                    Trace.TraceInformation($"成功创建命名引用: {pictureId} -> {refFormat}");
                                    referenceCreated = true;
                                    break;
                                }
                                catch (Exception refEx)
                                {
                                    Trace.TraceWarning($"引用格式 {refFormat} 失败: {refEx.Message}");
                                }
                            }

                            if (!referenceCreated)
                            {
                                Trace.TraceError("所有引用格式都失败，但继续尝试DISPIMG公式");
                            }
                        }
                        else
                        {
                            Trace.TraceError("无法创建命名引用对象");
                        }
                    }
                    catch (Exception nameEx)
                    {
                        Trace.TraceWarning($"创建命名引用失败: {nameEx.Message}，但继续尝试DISPIMG公式");
                    }

                    // 步骤2：设置DISPIMG公式到目标单元格
                    try
                    {
                        // 设置单元格为公式类型
                        cell.SetCellType(CellType.Formula);

                        // 根据百度AI的说明和你的成功示例，使用正确的DISPIMG格式
                        string dispimgFormula = $"DISPIMG(\"{pictureId}\",1)";
                        cell.SetCellFormula(dispimgFormula);

                        Trace.TraceInformation($"设置DISPIMG公式: ={dispimgFormula}");

                        // 步骤3：强制重新计算，让Excel识别图片ID
                        try
                        {
                            var evaluator = hssfWorkbook.GetCreationHelper().CreateFormulaEvaluator();
                            evaluator.EvaluateAll();
                            Trace.TraceInformation("强制重新计算所有公式");
                        }
                        catch (Exception evalEx)
                        {
                            Trace.TraceWarning($"公式重新计算失败: {evalEx.Message}");
                        }

                        // 步骤4：验证公式是否被正确设置
                        try
                        {
                            string actualFormula = cell.CellFormula;
                            Trace.TraceInformation($"实际设置的公式: ={actualFormula}");

                            if (actualFormula.StartsWith("@"))
                            {
                                Trace.TraceWarning("检测到@符号，这可能导致问题");
                            }
                        }
                        catch (Exception checkEx)
                        {
                            Trace.TraceWarning($"检查公式失败: {checkEx.Message}");
                        }

                        Trace.TraceInformation($"成功设置DISPIMG公式到单元格 ({rowIndex}, {columnIndex})");
                        return true;
                    }
                    catch (Exception formulaEx)
                    {
                        Trace.TraceError($"设置DISPIMG公式失败: {formulaEx.Message}");
                        return false;
                    }
                }
                catch (Exception dispimgEx)
                {
                    Trace.TraceError($"DISPIMG实现失败: {dispimgEx.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"图片插入过程失败: {ex.Message}");

                // 显示错误信息
                try
                {
                    IRow errorRow = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
                    ICell errorCell = errorRow.GetCell(columnIndex) ?? errorRow.CreateCell(columnIndex);
                    errorCell.SetCellType(CellType.String);
                    errorCell.SetCellValue($"[图片插入失败]");
                }
                catch (Exception cellEx)
                {
                    Trace.TraceError($"设置错误信息失败: {cellEx.Message}");
                }

                return false;
            }
        }

        /// <summary>
        /// 生成简单的图片ID
        /// </summary>
        private string GenerateSimpleImageId(string physicalPath, int rowIndex, int columnIndex)
        {
            try
            {
                string fileName = Path.GetFileNameWithoutExtension(physicalPath);
                // 清理文件名，只保留字母数字和下划线
                string cleanName = new string(fileName.Where(c => char.IsLetterOrDigit(c) || c == '_').ToArray());

                if (string.IsNullOrEmpty(cleanName))
                {
                    cleanName = "Image";
                }

                // 确保以字母开头
                if (char.IsDigit(cleanName[0]))
                {
                    cleanName = "Img_" + cleanName;
                }

                // 添加位置信息确保唯一性
                return $"{cleanName}_R{rowIndex}C{columnIndex}";
            }
            catch
            {
                return $"Image_R{rowIndex}C{columnIndex}";
            }
        }

        /// <summary>
        /// 直接将图片嵌入到单元格中（作为浮动对象的降级方案）
        /// </summary>
        /// <param name="workbook">Excel工作簿</param>
        /// <param name="sheet">Excel工作表</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="columnIndex">列索引</param>
        /// <param name="pictureIdx">图片索引</param>
        /// <returns>是否成功</returns>
        private bool TryEmbedImageDirectly(IWorkbook workbook, ISheet sheet, int rowIndex, int columnIndex, int pictureIdx)
        {
            try
            {
                Trace.TraceWarning("DISPIMG失败，降级到浮动图片方式");

                // 创建绘图对象
                IDrawing drawing = sheet.CreateDrawingPatriarch();

                // 创建锚点，将图片定位到单元格内
                IClientAnchor anchor = workbook.GetCreationHelper().CreateClientAnchor();
                anchor.Col1 = columnIndex;
                anchor.Row1 = rowIndex;
                anchor.Col2 = columnIndex + 1;
                anchor.Row2 = rowIndex + 1;
                anchor.AnchorType = AnchorType.MoveAndResize;

                // 创建图片
                IPicture picture = drawing.CreatePicture(anchor, pictureIdx);

                // 调整图片大小以适应单元格
                try
                {
                    picture.Resize(0.9); // 缩放到90%，留出边距
                }
                catch
                {
                    // 如果调整失败，使用默认大小
                }

                Trace.TraceInformation($"成功创建浮动图片对象 ({rowIndex}, {columnIndex})");
                return true;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"创建浮动图片对象失败: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// 获取图片的物理路径
        /// </summary>
        /// <param name="imagePath">图片路径（可能是相对路径、绝对路径或URL）</param>
        /// <returns>物理路径</returns>
        private string GetPhysicalImagePath(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                {
                    return null;
                }

                string physicalPath = imagePath.Trim();

                if (physicalPath.StartsWith("/"))
                {
                    // 将相对路径转换为物理路径
                    try
                    {
                        physicalPath = System.Web.HttpContext.Current.Server.MapPath(physicalPath);
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"转换相对路径失败: {ex.Message}");
                        return null;
                    }
                }
                else if (physicalPath.StartsWith("http://") || physicalPath.StartsWith("https://"))
                {
                    // 对于HTTP URL，尝试下载图片到临时文件
                    try
                    {
                        string tempFileName = Path.GetTempFileName() + Path.GetExtension(physicalPath);
                        using (WebClient client = new WebClient())
                        {
                            // WebClient没有直接的Timeout属性，但可以通过事件处理超时
                            client.DownloadFile(new Uri(physicalPath), tempFileName);
                        }
                        physicalPath = tempFileName;
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError($"下载远程图片失败: {ex.Message}");
                        return null;
                    }
                }

                return physicalPath;
            }
            catch (Exception ex)
            {
                Trace.TraceError($"获取物理路径失败: {ex.Message}");
                return null;
            }
        }



        /// <summary>
        /// 根据文件扩展名获取图片类型
        /// </summary>
        /// <param name="physicalPath">图片物理路径</param>
        /// <returns>图片类型</returns>
        private PictureType GetPictureType(string physicalPath)
        {
            string extension = Path.GetExtension(physicalPath).ToLower();
            switch (extension)
            {
                case ".png":
                    return PictureType.PNG;
                case ".jpg":
                case ".jpeg":
                    return PictureType.JPEG;
                case ".gif":
                    return PictureType.GIF;
                case ".bmp":
                    return PictureType.BMP;
                default:
                    return PictureType.JPEG; // 默认为JPEG
            }
        }

        /// <summary>
        /// 调整图片大小以适应单元格
        /// </summary>
        /// <param name="picture">图片对象</param>
        /// <param name="anchor">锚点</param>
        private void ResizePictureToFitCell(IPicture picture, IClientAnchor anchor)
        {
            try
            {
                // 获取图片的原始尺寸
                var imageSize = picture.GetImageDimension();

                // 计算单元格的大小（这里使用估算值）
                double cellWidth = 256 * 40; // 40个字符宽度对应的像素
                double cellHeight = 256 * 8;  // 8倍行高对应的像素

                // 计算缩放比例，保持图片比例
                double scaleX = cellWidth / imageSize.Width;
                double scaleY = cellHeight / imageSize.Height;
                double scale = Math.Min(scaleX, scaleY) * 0.9; // 留10%边距

                // 应用缩放
                if (scale < 1.0)
                {
                    picture.Resize(scale);
                }
                else
                {
                    // 如果图片比单元格小，则适当放大但不超过单元格
                    picture.Resize(Math.Min(scale, 1.0));
                }
            }
            catch (Exception ex)
            {
                Trace.TraceWarning($"调整图片大小失败: {ex.Message}");
                // 如果调整失败，使用默认大小
                try
                {
                    picture.Resize();
                }
                catch
                {
                    // 忽略调整失败
                }
            }
        }

        /// <summary>
        /// 将列索引转换为Excel列名（例如：0->A, 1->B, ..., 26->AA, 27->AB, ...）
        /// </summary>
        /// <param name="columnIndex">列索引（从0开始）</param>
        /// <returns>Excel列名</returns>
        private string GetExcelColumnName(int columnIndex)
        {
            string columnName = "";

            while (columnIndex >= 0)
            {
                int remainder = columnIndex % 26;
                columnName = (char)('A' + remainder) + columnName;
                columnIndex = (columnIndex / 26) - 1;
            }

            return columnName;
        }
    }
}