<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>图片画廊测试</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .gallery-image {
            transition: transform 0.2s ease-in-out;
            border-radius: 4px;
        }
        .gallery-image:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .thumbnail {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px;
            background-color: #fff;
            transition: box-shadow 0.2s ease-in-out;
        }
        .thumbnail:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>图片画廊功能测试</h2>
        <p>点击下面的按钮测试图片画廊功能：</p>
        <button class="btn btn-primary" onclick="testImageGallery()">测试图片画廊</button>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
    
    <script>
        // 测试图片数据
        function testImageGallery() {
            var testImages = [
                {
                    url: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=签名材料1',
                    title: '签名材料 1',
                    category: '签名材料'
                },
                {
                    url: 'https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=签名材料2',
                    title: '签名材料 2',
                    category: '签名材料'
                },
                {
                    url: 'https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=身份证正面',
                    title: '身份证正面',
                    category: '身份证照片'
                },
                {
                    url: 'https://via.placeholder.com/300x200/96CEB4/FFFFFF?text=身份证背面',
                    title: '身份证背面',
                    category: '身份证照片'
                }
            ];
            
            showImageGallery(testImages, '签名材料', 'TEST001');
        }

        // 显示图片画廊
        function showImageGallery(images, focusType, recordId) {
            var modalHtml = '<div class="modal fade" id="imageGalleryModal" tabindex="-1" role="dialog" aria-labelledby="imageGalleryModalLabel">' +
                '<div class="modal-dialog modal-lg" style="width: 90%; max-width: 1200px;" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>' +
                '<h4 class="modal-title" id="imageGalleryModalLabel">图片展示 - 记录ID: ' + recordId + '</h4>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="row">';

            // 按类别分组显示图片
            var categories = {};
            for (var i = 0; i < images.length; i++) {
                var img = images[i];
                if (!categories[img.category]) {
                    categories[img.category] = [];
                }
                categories[img.category].push(img);
            }

            // 生成每个类别的图片展示
            for (var category in categories) {
                var categoryImages = categories[category];
                
                modalHtml += '<div class="col-md-12">' +
                    '<h5><strong>' + category + '</strong> (' + categoryImages.length + '张)</h5>' +
                    '<div class="row" style="margin-bottom: 20px;">';

                for (var j = 0; j < categoryImages.length; j++) {
                    var img = categoryImages[j];
                    modalHtml += '<div class="col-md-3 col-sm-4 col-xs-6" style="margin-bottom: 15px;">' +
                        '<div class="thumbnail" style="margin-bottom: 0;">' +
                        '<img src="' + img.url + '" class="img-responsive gallery-image" ' +
                        'style="height: 150px; width: 100%; object-fit: cover; cursor: pointer;" ' +
                        'alt="' + img.title + '" title="' + img.title + '" ' +
                        'onclick="showFullImage(\'' + img.url + '\', \'' + img.title + '\')">' +
                        '<div class="caption text-center">' +
                        '<p style="margin: 5px 0; font-size: 12px;">' + img.title + '</p>' +
                        '</div>' +
                        '</div>' +
                        '</div>';
                }

                modalHtml += '</div></div>';
            }

            modalHtml += '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除之前的模态框
            $('#imageGalleryModal').remove();

            // 添加新模态框并显示
            $('body').append(modalHtml);
            $('#imageGalleryModal').modal('show');
        }

        // 显示全尺寸图片
        function showFullImage(imageUrl, title) {
            var fullImageModalHtml = '<div class="modal fade" id="fullImageModal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog modal-lg" style="width: 95%; max-width: 1400px;" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>' +
                '<h4 class="modal-title">' + title + '</h4>' +
                '</div>' +
                '<div class="modal-body text-center" style="padding: 20px;">' +
                '<img src="' + imageUrl + '" style="max-width: 100%; max-height: 80vh; border: 1px solid #ddd;">' +
                '</div>' +
                '<div class="modal-footer">' +
                '<a href="' + imageUrl + '" target="_blank" class="btn btn-primary">在新窗口中打开</a>' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除之前的全尺寸图片模态框
            $('#fullImageModal').remove();

            // 添加新模态框并显示
            $('body').append(fullImageModalHtml);
            $('#fullImageModal').modal('show');
        }
    </script>
</body>
</html>
