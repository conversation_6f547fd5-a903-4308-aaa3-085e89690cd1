# 测试身份证多列导入功能

## 功能说明
现在移动签名报备的导入功能支持读取6个身份证相关列的图片数据：
1. 身份证照片(反正面) - 第15列
2. 身份证材料1 - 第16列  
3. 身份证材料2 - 第17列
4. 身份证材料3 - 第18列
5. 身份证材料4 - 第19列
6. 身份证材料5 - 第20列

## 测试步骤

### 1. 下载导入模板
1. 访问移动签名报备页面
2. 点击"下载导入模板"
3. 确认Excel模板中包含了新增的5个身份证材料列

### 2. 准备测试数据
在Excel模板中填写测试数据，特别是身份证相关列：

**示例数据：**
```
身份证照片(反正面): =DISPIMG("ID_001",1)
身份证材料1: =DISPIMG("ID_002",1)  
身份证材料2: =DISPIMG("ID_003",1)
身份证材料3: (留空)
身份证材料4: =DISPIMG("ID_004",1)
身份证材料5: (留空)
```

### 3. 执行导入测试
1. 上传填写好的Excel文件
2. 选择对应的通道
3. 点击导入
4. 检查导入结果

### 4. 验证数据存储
导入成功后，检查数据库中的`IdCardPhotos`字段：
- 应该包含所有有效的图片URL，用分号分隔
- 例如：`/UploadFile/MobileSignReport/image1.jpg;/UploadFile/MobileSignReport/image2.jpg;/UploadFile/MobileSignReport/image3.jpg`

### 5. 验证数据显示
1. 在移动签名报备列表页面查看导入的数据
2. 身份证照片列应该显示多个图片缩略图
3. 点击图片应该能正常预览

### 6. 验证导出功能
1. 导出包含身份证图片的数据
2. 检查导出的Excel文件
3. 确认身份证图片被正确分离到6个不同的列中

## 预期结果

### 导入成功的情况：
- 系统能正确识别6个身份证相关列
- 能解析DISPIMG格式的图片引用
- 将多个图片URL合并存储到IdCardPhotos字段
- 导入成功消息显示正确的记录数

### 数据存储格式：
```sql
-- 数据库中的存储格式
IdCardPhotos = '/UploadFile/MobileSignReport/IdCardPhotos_image1.jpg;/UploadFile/MobileSignReport/IdCardMaterial1_image2.jpg;/UploadFile/MobileSignReport/IdCardMaterial2_image3.jpg'
```

### 导出结果：
- 身份证照片(反正面)列：第一个图片URL
- 身份证材料1列：第二个图片URL  
- 身份证材料2列：第三个图片URL
- 其他列：对应的图片URL或空值

## 错误处理测试

### 测试场景1：部分列为空
- 只填写身份证照片和身份证材料1
- 其他列留空
- 预期：只导入有数据的列，空列被忽略

### 测试场景2：DISPIMG格式错误
- 使用错误的DISPIMG格式
- 预期：显示相应的错误信息

### 测试场景3：图片ID不存在
- 使用不存在的图片ID
- 预期：创建占位图片或显示警告信息

## 兼容性测试

### 测试现有数据：
1. 导出现有的移动签名报备数据
2. 确认现有的单个身份证图片能正确显示在第一列
3. 其他身份证材料列应该为空

### 测试混合数据：
1. 导入包含新旧格式混合的数据
2. 确认系统能正确处理不同格式的数据

## 注意事项
1. 图片文件需要实际存在于服务器上，否则只会显示占位图片
2. DISPIMG格式必须正确：`=DISPIMG("图片ID",1)`
3. 系统会为每个图片生成唯一的文件名以避免冲突
4. 所有身份证相关图片都会合并到同一个数据库字段中
