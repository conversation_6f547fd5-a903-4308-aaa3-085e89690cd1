# IIS文件上传大小限制修复指南

## 🚨 **问题描述**

用户上传20多MB的文件时报错，这是因为IIS和ASP.NET的默认文件上传大小限制导致的。

## 🔧 **解决方案**

### **1. ASP.NET 配置 (system.web)**

在 `web.config` 的 `<system.web>` 节点中配置：

```xml
<httpRuntime maxRequestLength="102400" executionTimeout="6000" targetFramework="4.5" />
```

**参数说明：**
- `maxRequestLength="102400"` = 100MB (单位：KB)
- `executionTimeout="6000"` = 6000秒超时时间

### **2. IIS 配置 (system.webServer)**

在 `web.config` 的 `<system.webServer>` 节点中配置：

```xml
<security>
  <requestFiltering>
    <requestLimits maxAllowedContentLength="104857600" />
  </requestFiltering>
</security>
```

**参数说明：**
- `maxAllowedContentLength="104857600"` = 100MB (单位：字节)

## 📊 **配置对照表**

| 文件大小 | maxRequestLength (KB) | maxAllowedContentLength (字节) |
|----------|----------------------|-------------------------------|
| 10MB     | 10240                | 10485760                      |
| 20MB     | 20480                | 20971520                      |
| 50MB     | 51200                | 52428800                      |
| 100MB    | 102400               | 104857600                     |
| 200MB    | 204800               | 209715200                     |

## ⚙️ **当前配置**

已将文件上传限制设置为 **100MB**：

```xml
<!-- ASP.NET 配置 -->
<httpRuntime maxRequestLength="102400" executionTimeout="6000" targetFramework="4.5" />

<!-- IIS 配置 -->
<security>
  <requestFiltering>
    <requestLimits maxAllowedContentLength="104857600" />
  </requestFiltering>
</security>
```

## 🔍 **验证步骤**

### **1. 重启应用程序**
- 重新编译项目
- 重启IIS应用程序池
- 或者修改 `web.config` 后会自动重启

### **2. 测试文件上传**
1. 准备一个20-30MB的测试文件
2. 通过系统上传功能测试
3. 观察是否还有大小限制错误

### **3. 检查错误日志**
如果仍有问题，检查：
- IIS日志
- 应用程序事件日志
- 浏览器开发者工具网络面板

## 🚨 **常见问题**

### **问题1：仍然报413错误**
**原因**：可能是反向代理或负载均衡器的限制
**解决**：检查Nginx、Apache等前端服务器配置

### **问题2：上传超时**
**原因**：`executionTimeout` 设置太小
**解决**：增加超时时间
```xml
<httpRuntime maxRequestLength="102400" executionTimeout="12000" targetFramework="4.5" />
```

### **问题3：内存不足**
**原因**：大文件上传占用过多内存
**解决**：
1. 使用流式上传
2. 分块上传
3. 增加服务器内存

### **问题4：磁盘空间不足**
**原因**：临时文件夹空间不够
**解决**：
1. 清理临时文件
2. 配置临时文件路径
```xml
<httpRuntime tempDirectory="D:\Temp" />
```

## 🔧 **高级配置**

### **1. 针对特定页面配置**

在特定页面的 `web.config` 中：
```xml
<location path="Upload.aspx">
  <system.web>
    <httpRuntime maxRequestLength="204800" executionTimeout="12000" />
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="209715200" />
      </requestFiltering>
    </security>
  </system.webServer>
</location>
```

### **2. 文件类型限制**

```xml
<system.webServer>
  <security>
    <requestFiltering>
      <requestLimits maxAllowedContentLength="104857600" />
      <fileExtensions>
        <add fileExtension=".exe" allowed="false" />
        <add fileExtension=".bat" allowed="false" />
      </fileExtensions>
    </requestFiltering>
  </security>
</system.webServer>
```

## 📝 **部署清单**

- [ ] 修改 `web.config` 配置
- [ ] 重新编译项目
- [ ] 部署到服务器
- [ ] 重启IIS应用程序池
- [ ] 测试文件上传功能
- [ ] 验证不同大小文件上传
- [ ] 检查服务器性能影响
- [ ] 更新用户使用说明

## 🎯 **性能建议**

1. **合理设置大小限制**：不要设置过大，避免恶意上传
2. **使用异步上传**：避免阻塞用户界面
3. **添加进度显示**：提升用户体验
4. **文件类型验证**：只允许必要的文件类型
5. **病毒扫描**：对上传文件进行安全检查

## 🔒 **安全注意事项**

1. **文件类型白名单**：只允许安全的文件类型
2. **文件名过滤**：防止路径遍历攻击
3. **存储位置**：不要存储在Web根目录
4. **权限控制**：限制文件访问权限
5. **定期清理**：清理过期的上传文件

现在你的系统应该可以上传100MB以内的文件了！🚀
