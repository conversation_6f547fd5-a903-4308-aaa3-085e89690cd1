﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YLHT.GP.Models;

namespace YLHT_GP_Business.Interface
{
    [Description("平台数据接口（部分）")]
    public partial interface IYLHTSmsWebDataSource
    {
        bool DeleteIPWhite(string name);
        bool AddIpWhite(IPWhiteModel iPWhiteModels);
        bool UpdateIpWhite(IPWhiteModel iPWhiteModel);
        bool DeleteIpWhite(int id);
        IPWhiteModel GetIPWhiteModel(int Id);
        List<IPWhiteModel> GetIPWhiteModels(IPWhiteModel iPWhiteModel, int currpage, int pagesize, out int allsize, out int allpage);
        bool UpdateRestSendProduct(RestSendProduct restSendProduct);
        #region 补发
        bool AddRestSendProduct(RestSendProduct restSendProduct);
        RestSendProduct GetRestSendProductById(int id);
        bool DeleteRestSendProduct(int id);
        List<RestSendProduct> GetRestSendProducts(string name, int channelid, int status);

        // 补发用户配置相关方法
        List<ClientModel> GetAllClientUsers();
        List<ClientModel> GetUsersByRestSendId(int restSendId);
        bool UpdateUsersRestSendConfig(int restSendId, List<int> userIds);
        #endregion
        bool AddGoodMsisdnGroup(GoodMsisdnGroup goodMsisdnGroup);
        List<GoodMsisdnGroup> GetGoodMsisdnGroups(string name);
        GoodMsisdnGroup GetGoodMsisdnGroup(int id);
        bool UpdateGoodMsisdnGroup(GoodMsisdnGroup goodMsisdnGroup);
        bool DeleteGoodMsisdnGroup(int id);
        /// <summary>
        /// 初始化链接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        void Init(string connectionString);
        /// <summary>
        /// 查询指定通道的黑名单
        /// </summary>
        /// <param name="type">信息类型 1短信 2彩信 3语音 4闪信 0为全部</param>
        /// <param name="status">通道状态</param>
        /// <returns></returns>
        List<ChannelModel> getByChannList(int type = 0, int status = -1);

        /// <summary>
        /// 查询通道组列表
        /// </summary>
        /// <param name="type">通道组业务类型， 未知：0x0;   短信：0x1;     彩信：0x2;     闪信：0x4;</param>
        /// <returns></returns>
        List<ProductModel> GetProductList(int type=0,string name="", string username = "");
        /// <summary>
        /// 获取省列表
        /// </summary>
        /// <returns></returns>
        List<ProvinceModel> GetProvinceList();
        /// <summary>
        /// 获取运营商号段列表
        /// </summary>
        /// <returns></returns>
        List<OperMobileSectionModel> GetNdcMacs();
        /// <summary>
        /// 获取市级地区列表
        /// </summary>
        /// <param name="ProvinceId"></param>
        /// <returns></returns>
        List<CityModel> GetCityList1();
        /// <summary>
        /// 获取运营商类型列表
        /// </summary>
        /// <returns></returns>
        List<OperatorType> GetOPList();
        /// <summary>
        /// 获取市级地区列表根据省级区号
        /// </summary>
        /// <param name="ProvinceId"></param>
        /// <returns></returns>
        List<CityModel> GetCityList(string ProvinceId);
        /// <summary>
        /// 获取县级地区列表根据市级区号
        /// </summary>
        /// <param name="CityId"></param>
        /// <returns></returns>
        List<CountyModel> GetCountyList(string CityId);
        /// <summary>
        /// 获取通道名称
        /// </summary>
        /// <param name="channelInfo"></param>
        /// <returns></returns>
        Dictionary<string, int> GetChannelName(string channelInfo);
        /// <summary>
        /// 获取运营商名称
        /// </summary>
        /// <param name="operName"></param>
        /// <returns></returns>
        List<string> GetOperatorName(string operName);

        /// <summary>
        /// 获取省名称
        /// </summary>
        /// <returns></returns>
        string GetProvinceNameById(string provinceId);

        /// <summary>
        /// 获取市名称
        /// </summary>
        /// <param name="CityId"></param>
        /// <returns></returns>
        string GetCityNameById(string CityId);

        /// <summary>
        /// 获取运营商名称根据运营商id
        /// </summary>
        /// <param name="OperatorId"></param>
        /// <returns></returns>
        string GetOperatorNameById(string OperatorId);

        /// <summary>
        /// 获取通道根据id
        /// </summary>
        /// <param name="channelId"></param>
        /// <returns></returns>
        string GetChannelById(string channelId);
        
        /// <summary>
        /// 根据手机号码获取黑名单实体
        /// </summary>
        /// <param name="msisdn">手机号码</param>
        /// <returns>黑名单实体，如果不存在则返回null</returns>
        BlackMsisdnModel GetBlackMsisdnByNumber(string msisdn);
        
        /// <summary>
        /// 获取当天统计数据
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        CollectModel DayCollect(DateTime date,string userid="",string notuserid="",string notchannelid="", string adminid="");
        /// <summary>
        /// 地区占比查询
        /// </summary>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="channelid"></param>
        /// <param name="userid"></param>
        /// <param name="adminid"></param>
        /// <returns></returns>
        Dictionary<int,int> DiQuCollect(DateTime StartTime, DateTime EndTime, int channelid, string userid = "", string adminid = "");
        IEnumerable<UserSendStatModel> GetUserSendCount(DateTime startTime, DateTime endTime, string userId = "", string notUserId = "", string notChannelId = "");
        IEnumerable<UserSendRankModel> GetUserSendRank(DateTime startTime, DateTime endTime, string userId = "", string notUserId = "", string notChannelId = "", int top = 10);
        /// <summary>
        /// 获取通道状态统计列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>

        List<ChannelStatusCollectModel> GetMsgStatusStatisticalChannel(QuerySendStatistics query, out ChannelStatusCollectModel model, int PageSize, int currpage,int AdminId, out int myRows, out int myPageCount);
        List<ChannelStatusCollectModel> GetMsgStatusStatisticalChannelNoRows(QuerySendStatistics query, out ChannelStatusCollectModel model, int PageSize, int currpage, int AdminId);
        List<ChannelStatusCollectModel> GetMsgStatusStatisticalUser(QuerySendStatistics query, out ChannelStatusCollectModel model, int PageSize, int currpage,int AdminId, out int myRows, out int myPageCount);
        List<ChannelStatusCollectModel> GetMsgStatusStatisticalUserNoRows(QuerySendStatistics query, out ChannelStatusCollectModel model, int PageSize, int currpage,int AdminId);
        /// <summary>
        /// 获取通道发送统计列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>

        List<ChannelCollectModel> GetMsgStatisticalChannel(QuerySendStatistics query, out ChannelCollectModel model, int PageSize, int currpage,int AdminId, out int myRows, out int myPageCount);
        List<ChannelCollectModel> GetMsgStatisticalChannelDay(QuerySendStatistics query, out ChannelCollectModel model, int PageSize, int currpage,int AdminId, out int myRows, out int myPageCount);
        
        /// <summary>
        /// 获取用户发送统计列表
        /// </summary>
        /// <param name="query"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        List<UserCollectModel> GetStatisticalUser(QuerySendStatistics query, out UserCollectModel model, int PageSize, int currpage, int AdminId, out int myRows, out int myPageCount);

        /// <summary>
        /// 导出文件
        /// </summary>
        /// <param name="FileUrl">文件下载路径</param>
        /// <param name="FileName">文件名</param>
        /// <param name="FileDir">Oracle文件目录名称</param>
        /// <param name="FileShowName">前端展示文件名称</param>
        /// <param name="IsClient">是否客户端，1:客户端，2：管理端</param>
        /// <param name="UserId">用户ID</param>
        /// <param name="QuerySql">查询sql</param>
        /// <param name="QueryCountSql">查询总数sql</param>
        void ExportFile(string FileUrl,string FileName,string FileDir,string FileShowName,int IsClient,int UserId,string QuerySql,string QueryCountSql);

        void ExportChannelFileOptimize(MsgDetails msg, string sqlFormat, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        void ExportMsgReportDetailOptimize(MsgDetails msg, string sqlFormat, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        void ExportUserFileOptimize(MsgDetails msg, string sqlFormat, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        void ExportUserClientFileOptimize(MsgDetails msg, string sqlFormat, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        void ExportVoiceChannelFile(VoiceDetails msg, string sqlFormat, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        void ExportMmsTemplateViewOld( string QuerySql, string QueryCountSql, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        List<MmsTemplateModel> ExportMmsTemplateView(MmsTemplateModel mmsTemplateModel);
        /// <summary>
        /// 获取文件任务列表
        /// </summary>
        /// <param name="UserId">用户id</param>
        /// <param name="IsClient">是否是客户端，1:客户端，2：管理端</param>
        /// <returns></returns>
        List<ExportFile> GetExportFileList(string UserId, string IsClient,int PageSize,int currpage,out int AllPage,out int AllSize);
        /// <summary>
        /// 下载文件，更新状态
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        ExportFile DownFile(string id);
        /// <summary>
        /// 通道发送统计不查总条数（用于导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        List<ChannelCollectModel> GetMsgStatisticalChannelNoRows(QuerySendStatistics query,/* out ChannelCollectModel model,*/ int PageSize, int currpage,int AdminId/*, out int myRows, out int myPageCount*/);
        /// <summary>
        /// 用户发送统计不查总条数（用户导出）
        /// </summary>
        /// <param name="query"></param>
        /// <param name="PageSize"></param>
        /// <param name="currpage"></param>
        /// <returns></returns>
        List<UserCollectModel> GetMsgStatisticalUserNoRows(QuerySendStatistics query, int PageSize, int currpage,int Adminid);
        /// <summary>
        /// 用户发送统计不查总条数（用户导出账单）
        /// </summary>
        /// <param name="query"></param>
        /// <param name="PageSize"></param>
        /// <param name="currpage"></param>
        /// <param name="AdminId"></param>
        /// <returns></returns>
        List<UserCollectModel> GetMsgStatisticalUserNoRowsPrice(QuerySendStatistics query, int PageSize, int currpage, int AdminId);
        /// <summary>
        /// 导出文件完成更新状态
        /// </summary>
        /// <param name="fileid">文件id</param>
        /// <param name="path">文件路径</param>
        /// <param name="info">文件生成描述</param>
        /// <param name="rows">生成完成的总条数</param>
        void UpdateFileStatus(string fileid, string path, string info, string rows);
        /// <summary>
        /// 添加导出文件记录，并返回当前ID
        /// </summary>
        /// <param name="ef"></param>
        /// <returns></returns>
        string GetAddFileCurrId(ExportFile ef);
        /// <summary>
        /// 获取彩信对象集合
        /// </summary>
        /// <param name="TaskId"></param>
        /// <returns></returns>
        List<MsgData> GetMsgDatas(string TaskId);
        /// <summary>
        /// 发送统计同步，type：1：用户，2：通道
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        bool Renewal(int type = 1);

        /// <summary>
        /// 修改备注
        /// </summary>
        /// <param name="remarkModel"></param>
        /// <returns></returns>
        bool UpdateRemark(RemarkModel remarkModel);
        /// <summary>
        /// 查询备注
        /// </summary>
        /// <param name="remarkModel"></param>
        /// <returns></returns>
        RemarkModel GetRemark(RemarkModel remarkModel);
        /// <summary>
        /// 获取日志
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="text"></param>
        /// <param name="channelid"></param>
        /// <returns></returns>
        List<LogModel> GetLogModel(int adminid, int userid, DateTime startTime, DateTime endTime, string text = "", int channelid = 0, int pagesize = 100);

        #region 小组
        List<GroupModel> GetGroupModels();
        bool UpdateGroup(int id, string gname, List<int> SaleMans);
        bool AddGroup(string gname, List<int> SaleMans);
        /// <summary>
        /// 添加小组业务员关联
        /// </summary>
        /// <param name="gid"></param>
        /// <param name="smids"></param>
        /// <returns></returns>
        bool AddGroupSaleMan(int gid, List<int> smids);
        GroupModel GetGroupModelById(int groupId);
        List<SalesManModel> GetGroupSaleManListByGroupId(int groupId);
        bool DeleteGroupS(int id);
        #endregion

        #region 账单
        IEnumerable<UserPriceDynamic> GetUserPriceDynamics(int userId, DateTime etartTime, DateTime endTime);
        #endregion

        #region 通道监控表
        List<ChannelMonitorModel> GetChannelMonitors();
        ChannelMonitorModel GetChannelMonitorById(int id);
        bool UpdateChannelMonitor(ChannelMonitorModel channelMonitorModel);
        bool AddChannelMonitor(ChannelMonitorModel channelMonitorModel);
        bool DeleteChannelMonitor(int id);
        ChannelMonitorModel GetChannelMonitorByChannelId(int channelId);
        #endregion



        #region 视频彩信线上报备签名
        bool AddSignReport(SignReport signReport);
        bool AddBatchSignReport(List<SignReport> signReport);
        bool UpdateSignReport(SignReport signReport, string sign, int channelid);
        SignReport GetSignReport(int channelid, string sign);
        bool DeleteSignReport(int channelid, string sign);
        bool DeleteSignReport(int channelid, List<string> sign);
        List<SignReport> QuerySignReport(string sign = "",int channelid = 0, int status = -1);
        List<SignReport> QuerySignReport(SignReport page, string sign = "", int channelid = 0, int status = -1);
        #endregion


        #region 号码拦截
        bool DeleteMsisdnBack(string msisdn);
        bool AddMsisdnBack(string msisdn, int adminid, string remark);


        bool AddMsisdnBack(MsisdnBackModel n, out List<string> arr1);

        bool AddMsisdnBack(string[] msisdn, int adminid, string remark);
        List<MsisdnBackModel> MsisdnBackModel(MsisdnBackModel page);
        #endregion
        #region 携号转网
        bool AddMsisdnConvert(MsisdnConvertModel msisdnConvertModel);
        bool DeleteMsisdnConvert(string msisdn);
        List<MsisdnConvertModel> GetMsisdnConvert(MsisdnConvertModel msisdnConverModel);
        #endregion

        #region 拦截模板

        List<TextTemplatePinciModel> GetTextTemplatePinciList(TextTemplatePinciModel model, int CurrPage, int PageSize, string orderField, string orderFlag, out int rows, out int pageCount);

        bool AddTextTemplatePinci(TextTemplatePinciModel model);
        bool DeleteTextTemplatePinci(string[] ids);
        bool UpdateTextTemplatePinci(TextTemplatePinciModel model);
        TextTemplatePinciModel GetTextTemplatePinciById(string id);
        bool UpdateTextTemplatePinciStatus(string status, string[] id);
        #endregion


        #region 内容模板签名报备

        void ExportTextSignExtNumberFile(TextSignExtNumberModel model, string FileUrl, string FileName, string FileDir, string FileShowName, int IsClient, int UserId);
        List<TextSignExtNumberModel> GetTextSignExtNumberList(TextSignExtNumberModel model, int currPage, int PageSize, string strOrderField, string strOrderFlag, out int nmyRows, out int nmyPageCount);
        bool DeleteTextSignExtNumber(int id);
        bool AddTextSignExtNumber<T>(T model);
        bool DeleteBatchTextSignExtNumber(List<TextSignExtNumberModel> models);
        TextSignExtNumberModel GetTextSignExtNumberById(int id);
        bool UpdateTextSignExtNumber(TextSignExtNumberModel model);
        bool CheckTextSignExtNumber(string sign, string userId, string channelId, string extNumber, string text);
        bool BatchDeleteTextSignExtNumber(TextSignExtNumberModel model);
        bool BatchDeleteTextSignExtNumberOptimized(List<TextSignExtNumberModel> models, int channelId);
        #endregion

        #region 移动签名报备
        List<MobileSignReportModel> GetMobileSignReportList(MobileSignReportModel model, int currPage, int PageSize, string strOrderField, string strOrderFlag, out int nmyRows, out int nmyPageCount);
        bool DeleteMobileSignReport(int id);
        bool AddMobileSignReport<T>(T model);
        bool UpdateMobileSignReport(MobileSignReportModel model);
        MobileSignReportModel GetMobileSignReportById(int id);
        bool CheckMobileSignReport(string sign, string userId, string channelId, string extNumber, string content);
        int CountRecordsForDelete(int channelId, DateTime? startDate, DateTime? endDate);
        int BatchDeleteByCondition(int channelId, DateTime? startDate, DateTime? endDate);
        bool BatchDeleteMobileSignReport(MobileSignReportModel model);
        #endregion
    }

}
