# 移动签名报备导入功能修改说明

## 修改概述
根据需求，在移动签名报备的导入功能中，在身份证照片列后面增加了5个身份证材料列，用于存储更多的身份证相关图片。所有身份证相关列的图片都会合并到身份证字段中。

## 修改内容

### 1. 表格结构修改
**文件：** `WebProjects/YLHT_GP_Business/Business/MobileSignReportBll.cs`

**修改前的列结构：**
```
"身份证照片(反正面)", "扩展号", "内容"
```

**修改后的列结构：**
```
"身份证照片(反正面)", "身份证材料1", "身份证材料2", "身份证材料3", "身份证材料4", "身份证材料5", "扩展号", "内容"
```

### 2. 导入功能修改
**文件：** `WebProjects/YLHT.GP.Manager/Controllers/MobileSignReportController.cs`

#### 2.1 图片字段配置
增加了5个身份证材料列的配置：
```csharp
Dictionary<string, int> imageFields = new Dictionary<string, int>
{
    { "签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）", -1 },
    { "身份证照片(反正面)", -1 },
    { "身份证材料1", -1 },
    { "身份证材料2", -1 },
    { "身份证材料3", -1 },
    { "身份证材料4", -1 },
    { "身份证材料5", -1 }
};
```

#### 2.2 图片处理逻辑
- **精确位置匹配**：新增`ExtractImageFromSpecificCell`方法，根据具体的行列位置提取图片
- **支持多种Excel格式**：兼容HSSF(.xls)和XSSF(.xlsx)格式的图片提取
- **图片锚点定位**：通过图片的锚点信息精确匹配到对应的单元格
- **多图片合并**：将所有身份证相关的图片URL用分号(;)连接，存储到`IdCardPhotos`字段中
- **DISPIMG格式支持**：解析DISPIMG公式中的图片ID，并根据位置提取对应图片

### 3. 导出功能修改
**文件：** `WebProjects/YLHT_GP_Business/Business/MobileSignReportBll.cs`

#### 3.1 列索引调整
- 由于增加了5列，调整了后续列的索引
- 扩展号列从第16列调整到第21列
- 内容列从第17列调整到第22列

#### 3.2 身份证图片导出
- 修改了身份证图片的导出逻辑
- 将存储的多个图片URL按分号分割，分别放到不同的列中
- 身份证照片(反正面)放在第15列
- 身份证材料1-5分别放在第16-20列

### 4. 用户界面修改
**文件：** `WebProjects/YLHT.GP.Manager/Views/MobileSignReport/ImportMobileSignReport.cshtml`

更新了导入说明，增加了关于身份证材料多列支持的说明：
- 说明身份证材料支持6个列（身份证照片+身份证材料1-5）
- 提醒用户这些列都可以存放身份证相关图片

## 功能特点

### 1. 多图片支持
- 身份证照片(反正面)：主要的身份证图片
- 身份证材料1-5：额外的身份证相关材料图片
- 总共支持6个身份证相关图片

### 2. 图片合并存储
- 所有身份证相关列的图片都会合并到数据库的`IdCardPhotos`字段中
- 多个图片URL用分号(;)分隔
- 导出时会自动分离到对应的列中

### 3. 向后兼容
- 保持了原有的数据结构和字段
- 现有的身份证照片数据不受影响
- 支持原有的单图片和新的多图片格式

## 使用说明

### 导入Excel格式
1. 身份证照片(反正面)列：主要身份证图片
2. 身份证材料1-5列：额外的身份证材料图片
3. 所有图片都使用DISPIMG公式格式：`=DISPIMG("图片ID",1)`
4. 系统会自动将所有身份证相关图片合并存储

### 数据存储格式
数据库中的`IdCardPhotos`字段存储格式：
```
/UploadFile/MobileSignReport/image1.jpg;/UploadFile/MobileSignReport/image2.jpg;/UploadFile/MobileSignReport/image3.jpg
```

## 技术改进

### 图片提取逻辑优化
**问题**：原来的图片提取逻辑使用`workbook.GetAllPictures()`获取所有图片，然后简单地取第一张，无法区分不同列的图片。

**解决方案**：
1. **新增`ExtractImageFromSpecificCell`方法**：根据行列位置精确提取图片
2. **锚点匹配**：通过图片的锚点信息(`anchor.Col1`, `anchor.Row1`)匹配到具体单元格
3. **格式兼容**：分别处理HSSF和XSSF两种Excel格式的图片提取
4. **位置容错**：允许图片位置有±1行的偏差，提高匹配成功率

### 核心代码逻辑
```csharp
// 检查图片是否与当前单元格关联
if (anchor != null && anchor.Col1 == colIndex &&
   (anchor.Row1 == rowIndex || Math.Abs(anchor.Row1 - rowIndex) <= 1))
{
    // 提取并保存图片
}
```

## 测试建议
1. **精确位置测试**：在不同列放置不同图片，验证是否能正确提取对应列的图片
2. **多图片测试**：测试6个身份证列都有图片的情况
3. **部分图片测试**：测试只有部分列有图片的情况
4. **格式兼容测试**：分别测试.xls和.xlsx格式的文件
5. **位置偏差测试**：测试图片位置略有偏差时的处理
6. **空列处理测试**：测试某些列为空的情况
7. **向后兼容测试**：测试现有数据的导出功能
