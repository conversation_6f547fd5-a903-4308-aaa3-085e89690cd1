# 移动签名报备列表查询功能修复指南

## 🔍 **问题分析**

用户反馈列表页的查询功能不工作，可能的原因：
1. 前端参数传递问题
2. 后端模型绑定问题
3. 日期格式转换问题
4. 数据类型不匹配问题

## 🔧 **修复内容**

### **1. 控制器参数修复**

**问题**：原来使用复杂模型绑定，可能导致参数传递失败
**解决**：改为单独参数接收，手动构建查询模型

```csharp
// 修复前
public JsonResult GetMobileSignReportList(MobileSignReportModel model, int page = 1, int rows = 10, string sort = "id", string order = "desc")

// 修复后
public JsonResult GetMobileSignReportList(string ReportDate = "", string Type = "", string AccountName = "", string SignType = "", string Sign = "", string SignCompanyName = "", string UnifiedSocialCreditCode = "", string LegalPersonName = "", string ChannelId = "", int page = 1, int rows = 10, string sort = "id", string order = "desc")
```

### **2. 日期处理优化**

```csharp
// 处理日期字符串转换
if (!string.IsNullOrEmpty(ReportDate))
{
    if (DateTime.TryParse(ReportDate, out DateTime reportDate))
    {
        model.ReportDate = reportDate;
    }
}
```

### **3. 通道ID处理**

```csharp
// 处理通道ID字符串转整数
if (!string.IsNullOrEmpty(ChannelId) && int.TryParse(ChannelId, out int channelId))
{
    model.ChannelId = channelId;
}
```

### **4. 前端调试信息**

```javascript
function queryParams(params) {
    var temp = {
        rows: params.limit,
        page: Math.floor(params.offset / params.limit) + 1,
        sort: params.sort,
        order: params.order,
        ReportDate: $("#txt_reportDate").val(),
        Type: $("#txt_type").val(),
        AccountName: $("#txt_accountName").val(),
        SignType: $("#txt_signType").val(),
        Sign: $("#txt_sign").val(),
        SignCompanyName: $("#txt_signCompanyName").val(),
        UnifiedSocialCreditCode: $("#txt_unifiedSocialCreditCode").val(),
        LegalPersonName: $("#txt_legalPersonName").val(),
        ChannelId: $("#txt_channelId").val()
    };
    console.log("查询参数：", temp); // 添加调试信息
    return temp;
}
```

### **5. 重置功能优化**

```javascript
$("#btn_reset").click(function () {
    // 清空所有查询字段
    $("#txt_reportDate").val("");
    $("#txt_type").val("");
    $("#txt_accountName").val("");
    $("#txt_signType").val("");
    $("#txt_sign").val("");
    $("#txt_signCompanyName").val("");
    $("#txt_unifiedSocialCreditCode").val("");
    $("#txt_legalPersonName").val("");
    $("#txt_channelId").val("");
    // 重置后刷新表格
    $("#tb_mobileSignReport").bootstrapTable('refresh');
});
```

## 🧪 **验证步骤**

### **1. 基础功能测试**

1. **页面加载测试**：
   - 打开移动签名报备列表页面
   - 确认数据正常加载
   - 按F12查看控制台，确认无JavaScript错误

2. **查询参数调试**：
   - 在查询条件中输入测试数据
   - 点击"查询"按钮
   - 查看浏览器控制台输出的"查询参数"信息
   - 确认参数值正确传递

### **2. 单个条件查询测试**

1. **日期查询**：
   - 选择一个具体日期（如：2024-01-01）
   - 点击查询，验证结果是否正确

2. **类型查询**：
   - 输入类型关键字（如：移动）
   - 点击查询，验证模糊匹配是否工作

3. **客户名称查询**：
   - 输入客户名称关键字
   - 点击查询，验证模糊匹配是否工作

4. **签名类型查询**：
   - 选择下拉框中的选项（企业简称/APP/商标）
   - 点击查询，验证精确匹配是否工作

5. **签名查询**：
   - 输入签名关键字
   - 点击查询，验证模糊匹配是否工作

6. **通道查询**：
   - 选择通道下拉框中的选项
   - 点击查询，验证是否按通道过滤

### **3. 组合条件查询测试**

1. **多条件组合**：
   - 同时设置日期 + 类型 + 客户名称
   - 点击查询，验证AND条件是否正确工作

2. **边界条件测试**：
   - 输入不存在的数据
   - 验证是否返回空结果
   - 输入特殊字符，验证是否正常处理

### **4. 重置功能测试**

1. **重置测试**：
   - 设置多个查询条件
   - 点击"重置"按钮
   - 验证所有字段是否清空
   - 验证表格是否刷新显示全部数据

## 🔍 **调试信息检查**

### **浏览器控制台**

查询时应该看到：
```
查询参数： {rows: 10, page: 1, sort: "id", order: "desc", ReportDate: "2024-01-01", Type: "移动", ...}
```

### **服务器日志**

在服务器日志中应该看到：
```
GetMobileSignReportList查询参数: page=1, rows=10, sort=id, order=desc
查询条件: ReportDate=2024-01-01, Type=移动, AccountName=测试, ...
```

## 🚨 **故障排除**

### **如果查询仍然不工作**

1. **检查网络请求**：
   - 打开浏览器开发者工具 → Network面板
   - 点击查询按钮
   - 查看是否有AJAX请求发出
   - 检查请求参数是否正确

2. **检查服务器响应**：
   - 查看AJAX请求的响应内容
   - 确认返回的数据格式是否正确
   - 检查是否有服务器错误

3. **检查数据库连接**：
   - 确认数据库连接正常
   - 检查SQL查询是否正确执行
   - 查看数据库日志

### **常见错误及解决方案**

1. **日期格式错误**：
   ```
   错误：无法解析日期字符串
   解决：确保日期格式为 yyyy-mm-dd
   ```

2. **参数传递失败**：
   ```
   错误：控制器接收不到参数
   解决：检查前端参数名称与后端参数名称是否一致
   ```

3. **数据类型转换错误**：
   ```
   错误：ChannelId转换失败
   解决：确保传递的是有效的数字字符串
   ```

## 🎯 **预期结果**

修复完成后，用户应该能够：

1. ✅ 使用任意单个条件进行查询
2. ✅ 使用多个条件组合查询
3. ✅ 正确处理日期查询
4. ✅ 正确处理下拉框查询
5. ✅ 正确处理文本模糊查询
6. ✅ 使用重置功能清空条件
7. ✅ 查看查询结果的分页
8. ✅ 对查询结果进行排序

## 📝 **测试清单**

- [ ] 页面正常加载，无JavaScript错误
- [ ] 日期查询功能正常
- [ ] 类型模糊查询功能正常
- [ ] 客户名称模糊查询功能正常
- [ ] 签名类型精确查询功能正常
- [ ] 签名模糊查询功能正常
- [ ] 企业名称模糊查询功能正常
- [ ] 统一社会信用代码查询功能正常
- [ ] 法人姓名查询功能正常
- [ ] 通道查询功能正常
- [ ] 多条件组合查询功能正常
- [ ] 重置功能正常
- [ ] 查询结果分页正常
- [ ] 查询结果排序正常

## 🚀 **部署建议**

1. **备份数据**：修改前备份相关文件
2. **测试环境验证**：先在测试环境完整测试
3. **生产环境部署**：确认无误后部署
4. **用户培训**：告知用户查询功能的使用方法
