<!DOCTYPE html>
<html>
<head>
    <title>查询功能测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>移动签名报备查询功能测试</h2>
    
    <div>
        <label>日期:</label>
        <input type="text" id="txt_reportDate" value="2025-06-04" />
    </div>
    
    <div>
        <label>类型:</label>
        <input type="text" id="txt_type" value="山东大唐移动科技" />
    </div>
    
    <div>
        <label>客户名称:</label>
        <input type="text" id="txt_accountName" value="" />
    </div>
    
    <div>
        <label>签名类型:</label>
        <select id="txt_signType">
            <option value="">全部</option>
            <option value="企业简称">企业简称</option>
            <option value="APP">APP</option>
            <option value="商标">商标</option>
        </select>
    </div>
    
    <div>
        <label>签名:</label>
        <input type="text" id="txt_sign" value="" />
    </div>
    
    <div>
        <label>签名企业名称:</label>
        <input type="text" id="txt_signCompanyName" value="" />
    </div>
    
    <div>
        <label>统一社会信用代码:</label>
        <input type="text" id="txt_unifiedSocialCreditCode" value="" />
    </div>
    
    <div>
        <label>法人姓名:</label>
        <input type="text" id="txt_legalPersonName" value="" />
    </div>
    
    <div>
        <label>通道:</label>
        <select id="txt_channelId">
            <option value="">全部</option>
            <option value="1">通道1</option>
            <option value="2">通道2</option>
        </select>
    </div>
    
    <br/>
    <button id="btn_test">测试查询参数</button>
    <button id="btn_query">发送查询请求</button>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;">
        <h3>结果:</h3>
        <pre id="output"></pre>
    </div>

    <script>
        // 模拟queryParams函数
        function queryParams(params) {
            var temp = {
                rows: params ? params.limit : 10,
                page: params ? Math.floor(params.offset / params.limit) + 1 : 1,
                sort: params ? params.sort : "id",
                order: params ? params.order : "desc",
                ReportDate: $("#txt_reportDate").val(),
                Type: $("#txt_type").val(),
                AccountName: $("#txt_accountName").val(),
                SignType: $("#txt_signType").val(),
                Sign: $("#txt_sign").val(),
                SignCompanyName: $("#txt_signCompanyName").val(),
                UnifiedSocialCreditCode: $("#txt_unifiedSocialCreditCode").val(),
                LegalPersonName: $("#txt_legalPersonName").val(),
                ChannelId: $("#txt_channelId").val()
            };
            console.log("查询参数：", temp);
            return temp;
        }
        
        // 测试参数构建
        $("#btn_test").click(function() {
            var params = {
                limit: 10,
                offset: 0,
                sort: "id",
                order: "desc"
            };
            var queryData = queryParams(params);
            $("#output").text(JSON.stringify(queryData, null, 2));
        });
        
        // 发送实际查询请求
        $("#btn_query").click(function() {
            var params = {
                limit: 10,
                offset: 0,
                sort: "id",
                order: "desc"
            };
            var queryData = queryParams(params);
            
            $.ajax({
                url: '/MobileSignReport/GetMobileSignReportList',
                type: 'GET',
                data: queryData,
                success: function(response) {
                    $("#output").text("成功响应:\n" + JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    $("#output").text("错误响应:\n" + 
                        "状态: " + status + "\n" + 
                        "错误: " + error + "\n" + 
                        "响应: " + xhr.responseText);
                }
            });
        });
    </script>
</body>
</html>
