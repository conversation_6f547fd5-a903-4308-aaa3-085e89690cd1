@model YLHT.GP.Models.MsgAuditModel
@{
    Layout = "~/Views/Shared/_Layout2.cshtml";
}

@{ ViewBag.Title = "短信审核";}
<style>
    #leftsead {
        width: 28px;
        height: 143px;
        position: fixed;
        top: 100px;
        right: 0px;
        display: none;
    }

    *html #leftsead {
        margin-top: 258px;
        position: absolute;
        top: expression(eval(document.documentElement.scrollTop));
    }

        #leftsead li {
            width: 28px;
            height: 60px;
        }

            #leftsead li img {
                float: right;
            }

            #leftsead li a {
                height: 30px;
                float: right;
                display: block;
                min-width: 28px;
                max-width: 131px;
                border-radius: 5px;
                overflow: hidden;
                border: 1px solid gray;
            }

                #leftsead li a .shows {
                    display: block;
                }

                #leftsead li a .hides {
                    margin-right: -143px;
                    cursor: pointer;
                    cursor: hand;
                }

    /* 筛选结果样式 */
    .filter-result-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: #fff;
    }

    .filter-result-header {
        margin-bottom: 8px;
        font-size: 13px;
    }

    .phone-tag {
        background: #ffebee;
        color: #c62828;
        padding: 2px 6px;
        margin: 0 3px;
        border-radius: 3px;
        cursor: pointer;
        display: inline-block;
        font-size: 12px;
        border: 1px solid #ffcdd2;
    }

    .phone-tag:hover {
        background: #ffcdd2;
    }

    .link-tag {
        background: #e3f2fd;
        color: #1565c0;
        padding: 2px 6px;
        margin: 0 3px;
        border-radius: 3px;
        cursor: pointer;
        display: inline-block;
        font-size: 12px;
        word-break: break-all;
        border: 1px solid #bbdefb;
    }

    .link-tag:hover {
        background: #bbdefb;
    }

    .sign-tag {
        background: #f3e5f5;
        color: #7b1fa2;
        padding: 2px 6px;
        margin: 0 3px;
        border-radius: 3px;
        cursor: pointer;
        display: inline-block;
        font-size: 12px;
        border: 1px solid #e1bee7;
    }

    .sign-tag:hover {
        background: #e1bee7;
    }

    .template-add-btn {
        background: #4caf50;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        margin-left: 10px;
    }

    .template-add-btn:hover {
        background: #45a049;
    }

    .template-input {
        font-family: 'Courier New', monospace;
        line-height: 1.4;
    }

    .template-checkbox {
        transform: scale(1.2);
        margin-right: 8px;
    }

    .filter-result-item {
        position: relative;
    }

    .filter-result-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .template-tabs {
        display: flex;
        margin-bottom: 10px;
        border-bottom: 1px solid #ddd;
    }

    .template-tab-btn {
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-bottom: none;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 12px;
        margin-right: 2px;
        border-radius: 4px 4px 0 0;
        transition: all 0.3s;
    }

    .template-tab-btn:hover {
        background: #e9e9e9;
    }

    .template-tab-btn.active {
        background: #fff;
        border-bottom: 1px solid #fff;
        color: #4caf50;
        font-weight: bold;
    }

    .template-content {
        border: 1px solid #ddd;
        border-top: none;
        padding: 10px;
        border-radius: 0 0 4px 4px;
        background: #fff;
    }

    .content-box {
        margin-top: 8px;
        padding: 8px;
        background: #f9f9f9;
        border-radius: 3px;
    }

    .content-text {
        margin-top: 5px;
        padding: 8px;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 3px;
        cursor: pointer;
        line-height: 1.4;
        font-size: 13px;
    }

    .content-text:hover {
        background: #f5f5f5;
        border-color: #ddd;
    }
</style>
<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> <a href="/Home/Index">首页</a> <span class="c-gray en">&gt;</span> 短信管理 <span class="c-gray en">&gt;</span> 短信审核 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>

<div class="page-container">
    @using (Html.BeginForm("Index", "MsgAudit", FormMethod.Post, new { id = "form1" }))
    {
        <div>
            开始时间：
            <input type="text" autocomplete="off" value="@string.Format("{0:yyyy-MM-dd HH:mm:ss}",Model.StartTime)" onfocus="WdatePicker({ maxDate:'#F{$dp.$D(\'datemax\')||\'%y-%M-%d\'}',dateFmt: 'yyyy-MM-dd HH:mm:ss' })" id="datemin" name="StartTime" class="input-text Wdate" style="width:170px;">
            客户账号：<select style="width:200px" id="UserName" name="UserName" class="select-box select">
                <option value="" selected>全部</option>
                @if (Model.UserNames != null)
                {
                    foreach (var item in Model.UserNames)
                    {
                        string username = item.Split(',')[0];
                        string count = item.Split(',')[1];
                        <option value="@username" @(Model.UserName == username ? "selected" : "")>@username (@count)</option>
                        @*<option value="@item" @(Model.UserName==item ? "selected" : "")>@item</option>*@
                    }
                }
            </select>
            <button type="button" onclick="shuaxinusercount1()" id="shuaxinusercount" class="btn btn-success radius">刷新客户</button>
            @*<input type="text" class="input-text" value="@(Model.UserName==null ? "" : Model.UserName)" style="width:200px" placeholder="用户账号" id="UserName" autocomplete="off" name="UserName">*@
            客户名称：<input type="text" class="input-text" value="@(Model.CustomerName==null ? "" : Model.CustomerName)" style="width:220px" placeholder="用户账号" id="" name="CustomerName">
            业务类型：<select name="ServiceType" style="width:120px" class="select-box select">
                <option value="0" @(Model.ServiceType == 0 ? "selected" : "")>全部</option>
                <option value="1" @(Model.ServiceType == 1 ? "selected" : "")>短信</option>
                <option value="4" @(Model.ServiceType == 4 ? "selected" : "")>闪信</option>
            </select>
            审核类型：<select name="AuditType" style="width:120px" class="select-box select">
    <option value="-1" @(Model.AuditType == -1 ? "selected" : "")>全部</option>
    <option value="1" @(Model.AuditType == 1 ? "selected" : "")>账户设置</option>
    <option value="2" @(Model.AuditType == 2 ? "selected" : "")>缺少通道</option>
    <option value="3" @(Model.AuditType == 3 ? "selected" : "")>敏感词</option>
    <option value="4" @(Model.AuditType == 4 ? "selected" : "")>一客一签未匹配</option>
    <option value="5" @(Model.AuditType == 5 ? "selected" : "")>通道价格大于用户价格</option>
    <option value="6" @(Model.AuditType == 6 ? "selected" : "")>余额不足</option>
    <option value="7" @(Model.AuditType == 7 ? "selected" : "")>内容日限</option>
</select>
            排序：<select name="PaiXu" style="width:120px" class="select-box select">
                <option value="1" @(Model.PaiXu == 1 ? "selected" : "")>倒序</option>
                <option value="3" @(Model.PaiXu == 3 ? "selected" : "")>正序</option>
            </select>
            <input type="text" class="input-text" value="0" id="showanumber" style="width:50px" /><button type="button" onclick="auditgengxin()" class="btn btn-success radius">更新</button>
            @*审核状态：@Html.DropDownList("AuditType", (List<SelectListItem>)ViewBag.typeList)*@
            <br />
            <br />
            结束时间：
            <input type="text" autocomplete="off" value="@string.Format("{0:yyyy-MM-dd HH:mm:ss}",Model.EndTime)" onfocus="WdatePicker({ minDate:'#F{$dp.$D(\'datemin\')}',maxDate:'%y-%M-%d',dateFmt: 'yyyy-MM-dd HH:mm:ss' })" id="datemax" name="EndTime" class="input-text Wdate" style="width:170px;">
            短信内容：<input type="text" class="input-text" value="@Model.Text" style="width:200px" placeholder="短信内容" id="" name="Text">
            任务批次：<input type="text" class="input-text" value="@Model.TaskId" style="width:220px" placeholder="任务批次" id="" name="TaskId">
            手机号码：<input type="text" class="input-text" value="@Model.Msisdns" style="width:220px" placeholder="手机号码" id="" name="Msisdns">
            页面数量：<input type="number" class="number input-text" style="width:110px" name="PageSize" id="PageSize" value="@(ViewBag.PageSize==null ? 50 : ViewBag.PageSize)" />
            @*<select id="pagecount"><option value="50">50</option><option value="100">100</option><option value="200">200</option></select>*@
            @*@if (Model.IsPlan == 0)
                {
                    <input type="checkbox" style="display:none" name="IsPlan">@:定时
                }
                else if (Model.IsPlan == 1)
                {
                    <input type="checkbox" style="display:none" checked name="IsPlan">@:定时
                }*@

            <input type="hidden" value="@(ViewBag.curpage==null ? 1 : ViewBag.curpage)" id="hdcurpage" name="currpage" /><!--当前页-->
            <input type="hidden" value="false" id="Export" name="Export" />
            @*<input type="hidden" value="@(ViewBag.PageSize==null ? 50 : ViewBag.PageSize)" id="PageSize" name="PageSize" />*@<!--页面数量-->
            <button type="submit" class="btn btn-success radius" onclick="submit1()" id="sub" name=""><i class="Hui-iconfont">&#xe665;</i> 查询</button>
            <button type="submit" class="btn btn-success radius" onclick="Export()" id="" name=""><i class="Hui-iconfont">&#xe665;</i> 导出</button>
        </div>
    }

    <div class="cl pd-5 bg-1 bk-gray mt-20">
        <span class="l top">
            <button onclick="UpdateChannel('更换通道','/MsgAudit/UpdateChannel/','800','500')" class="btn btn-success radius">更换通道</button>&nbsp;
            <button onclick="update('修改内容','/MsgAudit/UpDateContent/','800','500')" class="btn btn-success radius">修改内容</button>&nbsp;
            <button onclick="AuditBack()" class="btn btn-success radius">退回审核</button>&nbsp;
            @*<button onclick="AuditShou()" class="btn btn-success radius">手动完成</button>&nbsp;*@
            @*<button class="btn btn-success radius">带鉴定</button>&nbsp;*@
            <button onclick="content_add('新增免审内容','/ContentPreparation/AddContent?Text=','800','500')" class="btn btn-success radius">添加免审</button>&nbsp;
            <button onclick="AuditGo()" class="btn btn-success radius">审核通过</button>&nbsp;
            <button onclick="showDayLimitPhoneAndLink()" class="btn btn-warning radius">筛选日限频手机号链接</button>
        </span>

        <span class="r">共有数据：<strong id="tiaoshu">@(ViewBag.allsize != null ? ViewBag.allsize : 0)</strong> 条 </span>
        <button  id="shuaxincount" onclick="shuaxincount()" class="btn btn-success radius r ">刷新条数</button>
        <div id="demo7" class="text-r"></div>
        <div id="messagebox" style="text-align:right"><span>分页正在加载中.....</span></div>
    </div>
    <div class="mt-20">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
            <thead>
                <tr class="text-c">
                    <th width="25"><input type="checkbox" name="" value=""></th>
                    <th width="90">任务批次</th>
                    <th width="80">账户</th>
                    @*<th width="80">客户名称</th>*@
                    <th width="90">通道（条数）</th>
                    <th width="60" title="条数">号码/计费</th>
                    <th width="70">运营商(数量)</th>
                    <th width="80">描述</th>
                    @*<th width="60">优先级</th>*@
                    <th width="50">提交方式</th>
                    <th width="80">提交时间</th>
                    <th width="80">操作</th>
                </tr>
            </thead>
            <tbody>
                @if (ViewBag.mlist != null)
                {
                    foreach (var item in (List<YLHT.GP.Models.MsgAuditModel>)ViewBag.mlist)
                    {
                        var miaoshu = Html.GetAuditData(item.AuditData, item.AuditType);
                        var tongdaoxinxi = Html.GetChannelNames(item.ChannelInfo);
                        var OperatorName = Html.GetOperatorName(item.GroupInfo);
                        var NewchannelInfos = Html.GetNewChannelNames(item.NewchannelInfo);
                        <tr class="text-c">
                            <td><input type="checkbox" value="@item.Id：@item.NewchannelInfo：@item.AuditType" name="checkbox"></td>
                            <td>@item.TaskId</td>
                            <td><u style="cursor:pointer;text-decoration:none" class="text-primary" onclick="customer_show('@item.UserName','/Client/UpdateCustomer/@item.UserId/1','','','600')">@item.UserName</u></td>
                            @*<td>@item.UserName</td>*@
                            @*<td>@item.CustomerName</td>*@
                            @if (item.NewchannelInfo == null)
                            {
                                <td title="@tongdaoxinxi" onclick="tip_show(this,'@tongdaoxinxi')" @*onclick="showCh(@item.Id)"*@>@YLHT.GP.Common.SqlStringHelper.Trim(tongdaoxinxi.ToString(), 12)</td>
                            }
                            else
                            {
                                <td title="@NewchannelInfos" onclick="tip_show(this,'@NewchannelInfos')">@YLHT.GP.Common.SqlStringHelper.Trim(NewchannelInfos.ToString(), 13)</td>
                            }
                            <td title="@item.BillingCount"><a href="javascript:void(0);" onclick="GetMsisdns(@item.Id)">@item.MsisdnCount</a> / @(Convert.ToInt32(item.BillingCount) * Convert.ToInt32(item.MsisdnCount))</td>
                            <td title="@OperatorName.ToString().Replace('|',' ')" onclick="tip_show(this,'@OperatorName.ToString().Replace('|',' ')')" @*onclick="showOp(@item.Id)"*@>@OperatorName.ToString().Split('|')[0] @*@YLHT.GP.Common.SqlStringHelper.Trim(OperatorName.ToString(), 8)*@</td>
                            @if (item.NewchannelInfo != null)
                            {
                                <td title="@miaoshu.ToString()" onclick="tip_show(this,'@miaoshu')">@miaoshu.ToString().Split('，')[0] (已分配通道) </td>
                            }
                            else
                            {

                                if (item.AuditType == 5 || item.AuditType == 4 || item.AuditType == 6 || item.AuditType == 7)
                                {
                                    <td title="@miaoshu" onclick="tip_show(this,'@miaoshu')" style='color:red'>@(miaoshu.ToString().Length < 13 ? miaoshu.ToString() : YLHT.GP.Common.SqlStringHelper.Trim(miaoshu.ToString(), 13))</td>
                                }
                                else
                                {
                                    if (item.AuditType == 3)
                                    {
                                        <td title="@miaoshu" onclick="ContentMatching('@item.AuditData.Split(':')[0]')" style="color:red">@YLHT.GP.Common.SqlStringHelper.Trim(miaoshu.ToString().Split('，')[0] + "，" + miaoshu.ToString().Split('，')[1], 13)</td>
                                    }
                                    else
                                    {
                                        <td title="@miaoshu" onclick="tip_show(this,'@miaoshu')" style='color:@(miaoshu.ToString().Contains("缺少通道") || miaoshu.ToString().Contains("内容验证") ? "red" : "")'>@YLHT.GP.Common.SqlStringHelper.Trim(miaoshu.ToString(), 13)</td>
                                    }
                                }
                            }
                            @*<td>@Html.GetPriority(item.Priority)</td>*@
                            <td>@Html.GetAccessOrigin(item.AccessOrigin)</td>
                            <td onclick="tip_show(this,'@item.SubmitTimes')">@item.SubmitTimes</td>
                            <td><a onclick="pishen('@item.Id')" role="button">批审</a>&nbsp;<a onclick="layer_show('批换','/MsgAudit/BatchUpText?TaskId=@item.Id','600','550')" role="button">批换</a>&nbsp;<a onclick="layer_show('变量批换','/MsgAudit/BatchUpdateTextDynamic?TaskId=@item.Id','700','760')" role="button">变量批换</a></td>
                        </tr>
                        <tr>
                            <td scope="col" colspan="11" class="text-r"><label id="<EMAIL>">@item.Text</label><label class="c-red">(@(item.ServiceType == 1 ? "短信" : item.ServiceType == 4 ? "闪信" : "未知"))(共<span>@item.Text.Length</span>字)</label></td>
                        </tr>
                        @*<tr>
                                <td class="text-r" scope="col" colspan="11"><label id="<EMAIL>">@item.Text</label> &nbsp;&nbsp;&nbsp;&nbsp;<label class="c-red">(共 @item.Text.Length 字)</label></td>
                            </tr>
                            <tr class="text-c" id="<EMAIL>" style="display:none">
                                <td colspan="11" scope="col"><textarea class="textarea" autocomplete="off" placeholder="手机号码" onKeyUp="$.Huitextarealength(this,100)" name="" id="" readonly="readonly">@item.Msisdns</textarea></td>
                            </tr>
                            <tr class="text-c" id="<EMAIL>" style="display:none">
                                <td colspan="11" scope="col">通道信息：@Html.GetChannelNames(item.ChannelInfo)</td>
                            </tr>*@
                        <tr class="text-c" id="<EMAIL>" style="display:none">
                            <td colspan="11" title="@item.GroupInfo" scope="col">运营商信息：@Html.GetOperatorName(item.GroupInfo)</td>
                        </tr>
                    }
                    if (ViewBag.mlist.Count != 0)
                    {
                        if (ViewBag.mlist.Count > 20)
                        {
                            <tr>

                                <td scope="col" colspan="11">
                                    <span class="r">
                                        共有数据：<strong>@(ViewBag.allsize != null ? ViewBag.allsize : 0)</strong> 条
                                        <button onclick="UpdateChannel('更换通道','/MsgAudit/UpdateChannel/','800','500')" class="btn btn-success radius">更换通道</button>&nbsp;
                                        <button onclick="update('修改内容','/MsgAudit/UpDateContent/','800','500')" class="btn btn-success radius">修改内容</button>&nbsp;
                                        <button onclick="AuditBack()" class="btn btn-success radius">退回审核</button>&nbsp;
                                        @*<button onclick="AuditShou()" class="btn btn-success radius">手动完成</button>&nbsp;*@
                                        <button onclick="content_add('新增免审内容','/ContentPreparation/AddContent?Text=','800','500')" class="btn btn-success radius">添加免审</button>&nbsp;
                                        <button onclick="AuditGo()" class="btn btn-success radius">审核通过</button>&nbsp;
                                        <button onclick="showDayLimitPhoneAndLink()" class="btn btn-warning radius">筛选日限频手机号链接</button>
                                    </span>
                                </td>
                            </tr>
                        }
                    }
                }
                else
                {
                    <tr>
                        <td scope="col" colspan="11" style="text-align:center">暂无数据</td>
                    </tr>
                }
            </tbody>
            @if (ViewBag.allsize > ViewBag.PageSize)
            {
                <tfoot>
                    <tr>
                        <td colspan="11" class="text-r"><div id="page1" class="page text-r"></div></td>
                    </tr>
                </tfoot>
            }
        </table>
        <input value="@ViewBag.allpage" id="allpage" type="hidden" />
        <input value="@ViewBag.curpage" id="curpage" type="hidden" />

        @*@Html.Partial("_FooterPager")*@
    </div>

</div>
<!--侧边悬浮
<div id="leftsead">
    <ul>
        <li><a href="javascript:void(0);" onclick="AuditGo()"><img src="~/images/foot03/审核通过.jpg" width="28" height="30" title="审核通过" class="shows" /></a></li>
        <li><a href="javascript:void(0);" onclick="UpdateChannel('更换通道','/MsgAudit/UpdateChannel/','800','500')"><img src="~/images/foot03/更换通道.jpg" width="28" title="更换通道" height="30" class="shows" /></a></li>

        <li><a href="javascript:void(0);" onclick="AuditShou()" title="手动完成"><img title="手动完成" src="~/images/foot03/手动完成.jpg" width="28" height="30" class="shows" /></a></li>
        <li><a href="javascript:void(0);" title="添加免审" onclick="content_add('新增免审内容','/ContentPreparation/AddContent?Text=','800','500')"><img title="添加免审" src="~/images/foot03/添加免审.jpg" width="28" height="30" class="shows" /></a></li>
        <li><a href="javascript:void(0);" title="退回审核" onclick="AuditBack()"><img title="退回审核" src="~/images/foot03/退回审核.jpg" width="28" height="30" class="shows" /></a></li>
        <li><a id="top_btn" title="返回顶部"><img src="~/images/foot03/top.jpg" title="返回顶部" width="28" height="30" class="shows" /></a></li>
    </ul>
</div>
    -->
<!--请在下方写此页面业务相关的脚本-->
<link href="~/Content/layui/css/layui.css" rel="stylesheet" />
<script src="~/Content/layui/layui.js"></script>
<script src="~/Content/layui/layui.all.js"></script>
<script src="~/Scripts/MsgAudit/Index.js"></script>
<script>
        $(function () {
            $("#messagebox").hide();
        })
        function shuaxincount() {
            querycount2();
        }
        function shuaxinusercount1() {
            $("#UserName").empty();
            $('#shuaxinusercount').attr('disabled', true);
            $.ajax({
                url: '/MsgAudit/ShuaXinUserCount',
                type: 'post',
                data: $("#form1").serialize(),
                dataType: 'json',
                success: function (data) {
                    $('#shuaxinusercount').attr('disabled', false);
                    console.log(data);
                    $("#UserName").append('<option value="" selected>全部</option>');
                    data.forEach(item => {

                        var username= item.split(',')[0];
                        var count = item.split(',')[1];
                        var sstr = "";

                        var option = '<option value="' + username + '" ' + sstr + '>' + username + '（' + count + '）</option>';
                        $("#UserName").append(option);
                    });

                }, error: function (data) {
                    $('#shuaxinusercount').attr('disabled', false);
                    console.log(data);
                }, timeout: function () {
                    $('#shuaxinusercount').attr('disabled', false);
                    console.log(data);
                }
            });




        }
        function querycount2() {
        var curpage =@ViewBag.curpage;
        if (true) {
            $("#shuaxincount").attr('disabled', true);
                $.ajax({
                    url: '/MsgAudit/ShuaXinCount',
                    type: 'post',
                    data: $("#form1").serialize(),
                    dataType: 'json',
                    success: function (data) {
                        $("#shuaxincount").attr('disabled', false);
                        console.log(data);
                        if (data.PageCount > 0) {
                            layui.use(['laypage'], function () {
                                var laypage = layui.laypage;
                                laypage.render({
                                    elem: 'demo7'
                                    , count: data.Rows,
                                    curr: curpage,
                                    limit: 50
                                    , layout: ['count', 'prev', 'page', 'next', 'refresh', 'skip']
                                    , jump: function (obj, first) {
                                        if (!first) {
                                            console.log(obj)
                                            $("#hdcurpage").val(obj.curr);
                                            loadshow();
                                            $("#form1").submit();
                                        }
                                    }
                                });
                            });
                        } else {
                            $("#shuaxincount").attr('disabled', false);
                        }
                    }, error: function (data) {
                        $("#shuaxincount").attr('disabled', false);
                        $("#messagebox").empty().text("获取分页异常");
                        $("#messagebox").show();
                        console.log(data);
                    }, timeout: function () {
                        $("#shuaxincount").attr('disabled', false);
                        $("#messagebox").empty().text("获取分页超时！");
                        $("#messagebox").show();
                        console.log(data);
                    }
                });
            //}
        }
}

        function auditgengxin() {
            var shownum = $("#showanumber").val();
            $.ajax({
                url: '/MsgAudit/UpdateShowNumber',
                type: 'post',
                data: { id: shownum },
                dataType: 'json',
                success: function (data) {
                        layer.msg("操作成功！", { icon: 1 });
                },
                error: function (data) {
                    layer.alert("后台异常！", { icon: 2 });
                    console.log(data);
                }
            });
        }

        function pishen(taskid) {
            var starttime = $("#datemin").val();
            var endtime = $("#datemax").val();
            var a = "/MsgAudit/BatchAudit?TaskId=" + taskid + "&startTime=" + starttime + "&endTime=" + endtime;
            layer_show('批审', a, '600', '650');
            return;
        }

        function ContentMatching(id) {
            layer.open({
                type: 2,
                title: '内容验证',
                content: '/MmsAudit/LookContentMatch?Id=' + id,
                area: ['1100px', '180px']
            });
        }
        /*用户-查看*/
        function customer_show(title, url, id, w, h) {
            layer_show(title, url, w, h);
        }

        /*筛选日限频条件下包含手机号和链接的内容*/
        function showDayLimitPhoneAndLink() {
            // 显示使用说明和查询模式选择
            var content = '<div style="margin-bottom: 15px;">';
            content += '<strong>📋 查询模式选择：</strong><br>';
            content += '<label style="margin-right: 20px; cursor: pointer;"><input type="radio" name="queryMode" value="excludeExisting" checked style="margin-right: 5px;">🔍 智能模式（推荐）</label>';
            content += '<label style="cursor: pointer;"><input type="radio" name="queryMode" value="includeAll" style="margin-right: 5px;">📊 全量模式</label>';
            content += '<div style="margin-top: 8px; padding: 8px; background: #f0f8ff; border-radius: 3px; font-size: 12px; color: #666;">';
            content += '<strong>🔍 智能模式：</strong>自动排除已添加的模板，只显示新内容，避免重复添加<br>';
            content += '<strong>📊 全量模式：</strong>显示所有符合条件的内容，包括已添加的模板';
            content += '</div>';
            content += '</div>';

            content += '<div style="border-top: 1px solid #eee; padding-top: 15px;">';
            content += '<strong>功能说明：</strong><br>';
            content += '• 自动识别11位手机号码<br>';
            content += '• 自动识别网址链接<br>';
            content += '• 自动识别【】格式签名<br>';
            content += '• 智能生成模板内容<br>';
            content += '• 自动去重相同短信内容<br>';
            content += '• 支持一键复制和批量添加<br>';
            content += '• 最多显示100条记录<br><br>';
            content += '<strong>智能模板规则：</strong><br>';
            content += '• <span style="color: #4caf50;">📋 严格模式</span>：保留手机号、链接、签名，其他内容智能选择变量类型（带长度限制）<br>';
            content += '• <span style="color: #ff9800;">🔓 宽松模式</span>：保留手机号、链接、签名，其他变量统一使用{Any}（无长度限制）<br>';
            content += '• <span style="color: #2196f3;">🌐 宽松模式（主域名）</span>：保留手机号、主域名、签名，其他变量统一使用{Any}（无长度限制）<br>';
            content += '• 支持手动编辑和批量操作';
            content += '</div>';

            layer.confirm(content, {
                btn: ['开始筛选', '取消'],
                icon: 3,
                title: '筛选日限频手机号链接内容',
                area: ['600px', 'auto']
            }, function(index) {
                // 获取选择的查询模式
                var queryMode = $('input[name="queryMode"]:checked').val();
                var excludeExisting = queryMode === 'excludeExisting';

                layer.close(index);

                // 获取当前查询条件
                var formData = $("#form1").serialize();
                formData += '&excludeExisting=' + excludeExisting;

                layer.load(1, { shade: [0.3, '#000'] });

            $.ajax({
                url: '/MsgAudit/GetDayLimitContentWithPhoneAndLink',
                type: 'post',
                data: formData,
                dataType: 'json',
                success: function (data) {
                    layer.closeAll('loading');

                    if (data.error) {
                        layer.alert(data.error, { icon: 2 });
                        return;
                    }

                    if (!data.success || !data.data || data.data.length === 0) {
                        var message = data.message || '没有找到符合条件的数据';
                        layer.alert(message + '<br><br><small style="color:#999;">提示：请确保查询时间范围内有日限频类型的审核数据，且内容包含手机号或链接</small>', { icon: 5 });
                        return;
                    }

                    // 构建显示内容
                    var content = '<div style="max-height: 600px; overflow-y: auto;">';
                    content += '<div style="margin-bottom: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px;">';

                    // 显示查询模式
                    var modeText = data.excludeExisting ? '🔍 智能模式（已排除现有模板）' : '📊 全量模式（包含所有内容）';
                    content += '<div style="margin-bottom: 8px;"><strong>查询模式：</strong>' + modeText + '</div>';

                    content += '<strong>📊 筛选结果：</strong>共找到 <span style="color: red;">' + data.total + '</span> 条去重后的记录';
                    if (data.originalCount && data.originalCount > data.total) {
                        content += ' <small style="color: #666;">(原始 ' + data.originalCount + ' 条，去重移除 ' + (data.originalCount - data.total) + ' 条)</small>';
                    }
                    if (data.total > 100) {
                        content += '<br><small style="color: #999;">显示前100条记录</small>';
                    }
                    content += '<div style="margin-top: 10px;">';
                    content += '<button class="btn btn-success btn-sm" onclick="batchAddAllTemplates(\'strict\')" style="margin-right: 5px;">📋 批量添加严格模板</button>';
                    content += '<button class="btn btn-success btn-sm" onclick="batchAddAllTemplates(\'loose\')" style="margin-right: 5px;">🔓 批量添加宽松模板</button>';
                    content += '<button class="btn btn-success btn-sm" onclick="batchAddAllTemplates(\'looseDomain\')" style="margin-right: 10px;">🌐 批量添加宽松模板（主域名）</button>';
                    content += '<button class="btn btn-info btn-sm" onclick="selectAllTemplates()" style="margin-right: 10px;">☑️ 全选模板</button>';
                    content += '<button class="btn btn-warning btn-sm" onclick="clearAllSelections()">❌ 清空选择</button>';
                    content += '</div>';
                    content += '</div>';

                    data.data.forEach(function(item, index) {
                        content += '<div class="filter-result-item" data-index="' + index + '">';
                        content += '<div class="filter-result-header">';
                        content += '<input type="checkbox" class="template-checkbox" data-index="' + index + '" style="margin-right: 8px;">';
                        content += '<strong>任务批次：</strong>' + item.TaskId + ' | ';
                        content += '<strong>用户：</strong>' + item.UserName + ' (' + (item.CustomerName || '') + ') | ';
                        content += '<strong>提交时间：</strong>' + (item.SubmitTimes || '未知');

                        // 显示合并信息
                        if (item.IsGrouped && item.GroupCount > 1) {
                            content += ' | <span style="color: #ff6f00; font-weight: bold;">🔗 已合并 ' + item.GroupCount + ' 条相似内容</span>';
                        }

                        content += '</div>';

                        if (item.HasPhone && item.Phones.length > 0) {
                            content += '<div style="margin-bottom: 8px;">';
                            content += '<strong style="color: #c62828;">📱 包含手机号：</strong><br>';
                            item.Phones.forEach(function(phone) {
                                content += '<span class="phone-tag" onclick="copyToClipboard(\'' + phone + '\')" title="点击复制">' + phone + '</span>';
                            });
                            content += '</div>';
                        }

                        if (item.HasLink && item.Links.length > 0) {
                            content += '<div style="margin-bottom: 8px;">';
                            if (item.IsGrouped && item.GroupCount > 1) {
                                content += '<strong style="color: #1565c0;">🔗 包含链接（共 ' + item.Links.length + ' 个，来自 ' + item.GroupCount + ' 条相似内容）：</strong><br>';
                            } else {
                                content += '<strong style="color: #1565c0;">🔗 包含链接：</strong><br>';
                            }
                            item.Links.forEach(function(link) {
                                var displayLink = link.length > 50 ? link.substring(0, 50) + '...' : link;
                                content += '<span class="link-tag" onclick="copyToClipboard(\'' + link.replace(/'/g, "\\'") + '\')" title="' + link + '&#10;点击复制">' + displayLink + '</span>';
                            });
                            content += '</div>';
                        }

                        if (item.HasSign && item.Signs.length > 0) {
                            content += '<div style="margin-bottom: 8px;">';
                            content += '<strong style="color: #7b1fa2;">✍️ 包含签名：</strong><br>';
                            item.Signs.forEach(function(sign) {
                                content += '<span class="sign-tag" onclick="copyToClipboard(\'' + sign.replace(/'/g, "\\'") + '\')" title="点击复制">' + sign + '</span>';
                            });
                            content += '</div>';
                        }

                        content += '<div class="content-box">';
                        content += '<strong>📄 短信内容：</strong>';
                        content += '<div class="content-text" onclick="copyToClipboard(\'' + item.Text.replace(/'/g, "\\'").replace(/\n/g, '\\n') + '\')" title="点击复制完整内容">';
                        content += item.Text.replace(/\n/g, '<br>');
                        content += '</div>';

                        // 显示合并详情
                        if (item.IsGrouped && item.GroupCount > 1) {
                            content += '<div style="margin-top: 8px;">';
                            content += '<button class="btn btn-sm btn-outline-info" onclick="toggleGroupDetails(' + index + ')" id="groupToggle' + index + '">';
                            content += '📋 查看合并详情 (' + item.GroupCount + ' 条)';
                            content += '</button>';
                            content += '<div id="groupDetails' + index + '" style="display: none; margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 3px; border: 1px solid #dee2e6;">';
                            content += '<strong>🔍 合并的相似内容：</strong><br>';
                            content += '<small style="color: #666;">基础模式：' + (item.BaseText || '未知') + '</small><br>';
                            if (item.MainDomain) {
                                content += '<small style="color: #666;">主域名：' + item.MainDomain + '</small><br>';
                            }
                            if (item.MainSign) {
                                content += '<small style="color: #666;">主签名：' + item.MainSign + '</small><br>';
                            }
                            content += '<div style="margin-top: 5px;">';
                            if (item.GroupItems && item.GroupItems.length > 0) {
                                item.GroupItems.forEach(function(groupItem, groupIndex) {
                                    content += '<div style="margin: 3px 0; padding: 3px 6px; background: white; border-radius: 2px; font-size: 12px;">';
                                    content += '<strong>条目 ' + (groupIndex + 1) + '：</strong>';
                                    content += '<span style="color: #666;">任务ID: ' + groupItem.TaskId + ' | 时间: ' + groupItem.SubmitTimes + '</span><br>';
                                    // 显示完整内容，但可以折叠长内容
                                    var displayText = groupItem.Text;
                                    var isLongText = displayText.length > 150;
                                    var shortText = isLongText ? displayText.substring(0, 150) + '...' : displayText;

                                    content += '<div style="font-family: monospace;">';
                                    content += '<span id="shortText' + index + '_' + groupIndex + '" style="' + (isLongText ? '' : 'display:none;') + '">' + shortText + '</span>';
                                    content += '<span id="fullText' + index + '_' + groupIndex + '" style="' + (isLongText ? 'display:none;' : '') + '">' + displayText.replace(/\n/g, '<br>') + '</span>';

                                    if (isLongText) {
                                        content += '<br><button class="btn btn-xs btn-link" onclick="toggleFullText(' + index + ', ' + groupIndex + ')" id="toggleBtn' + index + '_' + groupIndex + '" style="padding:2px 5px; font-size:11px;">显示完整内容</button>';
                                    }
                                    content += '</div>';
                                    content += '</div>';
                                });
                            }
                            content += '</div>';
                            content += '</div>';
                            content += '</div>';
                        }

                        // 显示智能生成的模板内容
                        if (item.StrictTemplate || item.LooseTemplate || item.LooseDomainTemplate) {
                            content += '<div style="margin-top: 10px; padding: 8px; background: #e8f5e8; border-radius: 3px; border-left: 4px solid #4caf50;">';
                            content += '<strong>🎯 智能模板：</strong>';

                            // 模板选择标签页
                            content += '<div style="margin-top: 8px;">';
                            content += '<div class="template-tabs" data-index="' + index + '">';
                            content += '<button class="template-tab-btn active" onclick="switchTemplateTab(' + index + ', \'strict\')" data-tab="strict">📋 严格模式</button>';
                            content += '<button class="template-tab-btn" onclick="switchTemplateTab(' + index + ', \'loose\')" data-tab="loose">🔓 宽松模式</button>';
                            content += '<button class="template-tab-btn" onclick="switchTemplateTab(' + index + ', \'looseDomain\')" data-tab="looseDomain">🌐 宽松模式（主域名）</button>';
                            content += '</div>';
                            content += '</div>';

                            // 严格模式模板
                            if (item.StrictTemplate) {
                                content += '<div class="template-content" data-index="' + index + '" data-mode="strict" style="display: block;">';
                                content += '<div style="margin-top: 5px;">';
                                content += '<textarea class="template-input strict-template" data-index="' + index + '" data-mode="strict" style="width: 100%; min-height: 60px; padding: 5px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px; resize: vertical;" placeholder="编辑严格模板内容...">' + item.StrictTemplate + '</textarea>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px;">';
                                content += '<button class="template-add-btn" onclick="addSingleTemplate(' + index + ', \'strict\')" title="添加严格模板" style="margin-right: 5px;">➕ 添加严格模板</button>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px; padding: 6px; background: #f0f8ff; border-radius: 3px; font-size: 11px; color: #666;">';
                                content += '<strong>📋 严格模式：</strong>手机号、链接、签名保持原样，其他内容智能选择变量类型';
                                content += '</div>';
                                content += '</div>';
                            }

                            // 宽松模式模板
                            if (item.LooseTemplate) {
                                content += '<div class="template-content" data-index="' + index + '" data-mode="loose" style="display: none;">';
                                content += '<div style="margin-top: 5px;">';
                                content += '<textarea class="template-input loose-template" data-index="' + index + '" data-mode="loose" style="width: 100%; min-height: 60px; padding: 5px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px; resize: vertical;" placeholder="编辑宽松模板内容...">' + item.LooseTemplate + '</textarea>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px;">';
                                content += '<button class="template-add-btn" onclick="addSingleTemplate(' + index + ', \'loose\')" title="添加宽松模板" style="margin-right: 5px;">➕ 添加宽松模板</button>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px; padding: 6px; background: #fff3e0; border-radius: 3px; font-size: 11px; color: #666;">';
                                content += '<strong>🔓 宽松模式：</strong>保留手机号、链接、签名，其他变量统一使用{Any}类型（无长度限制）';
                                content += '</div>';
                                content += '</div>';
                            }

                            // 宽松模式（主域名）模板
                            if (item.LooseDomainTemplate) {
                                content += '<div class="template-content" data-index="' + index + '" data-mode="looseDomain" style="display: none;">';
                                content += '<div style="margin-top: 5px;">';
                                content += '<textarea class="template-input loose-domain-template" data-index="' + index + '" data-mode="looseDomain" style="width: 100%; min-height: 60px; padding: 5px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px; resize: vertical;" placeholder="编辑宽松模式（主域名）模板内容...">' + item.LooseDomainTemplate + '</textarea>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px;">';
                                content += '<button class="template-add-btn" onclick="addSingleTemplate(' + index + ', \'looseDomain\')" title="添加宽松模式（主域名）模板" style="margin-right: 5px;">➕ 添加宽松模式（主域名）模板</button>';
                                content += '</div>';
                                content += '<div style="margin-top: 5px; padding: 6px; background: #e3f2fd; border-radius: 3px; font-size: 11px; color: #666;">';
                                content += '<strong>🌐 宽松模式（主域名）：</strong>保留手机号、主域名、签名，其他变量统一使用{Any}类型（无长度限制）';
                                content += '</div>';
                                content += '</div>';
                            }

                            content += '<div style="margin-top: 8px; padding: 6px; background: #f5f5f5; border-radius: 3px; font-size: 11px; color: #666;">';
                            content += '<strong>💡 使用提示：</strong><br>';
                            content += '• 严格模式：智能选择变量类型，匹配更精确<br>';
                            content += '• 宽松模式：统一使用{Any}类型，匹配更灵活<br>';
                            content += '• 宽松模式（主域名）：只保留主域名，不保留完整URL<br>';
                            content += '• 所有模式都保留手机号、签名不变';
                            content += '</div>';
                            content += '</div>';
                        }

                        content += '<small style="color: #999; margin-top: 5px; display: block;">💡 点击内容、手机号、链接或签名可复制</small>';
                        content += '</div>';
                        content += '</div>';
                    });

                    content += '</div>';

                    var layerIndex = layer.open({
                        type: 1,
                        title: '日限频条件下包含手机号和链接的内容',
                        content: content,
                        area: ['1000px', '700px'],
                        btn: ['重新查询', '关闭'],
                        btn1: function(index) {
                            layer.close(index);
                            showDayLimitPhoneAndLink(); // 重新打开查询对话框
                        },
                        yes: function(index) {
                            layer.close(index);
                            showDayLimitPhoneAndLink(); // 重新打开查询对话框
                        },
                        btn2: function(index) {
                            layer.close(index);
                        }
                    });

                    // 保存数据到全局变量，供其他函数使用
                    window.filterResultData = data.data;
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.alert('查询失败，请重试', { icon: 2 });
                }
            });
            });
        }

        /*复制到剪贴板*/
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    layer.msg('已复制到剪贴板', { icon: 1, time: 1000 });
                }).catch(function() {
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        /*兼容性复制方法*/
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    layer.msg('已复制到剪贴板', { icon: 1, time: 1000 });
                } else {
                    layer.msg('复制失败，请手动复制', { icon: 2, time: 2000 });
                }
            } catch (err) {
                layer.msg('复制失败，请手动复制', { icon: 2, time: 2000 });
            }

            document.body.removeChild(textArea);
        }

        /*添加到内容链接号码模板*/
        function addToContentTemplate(text) {
            if (!text || text.trim() === '') {
                layer.msg('内容不能为空', { icon: 5, time: 1000 });
                return;
            }

            layer.confirm('确定要将此内容添加到内容链接号码模板吗？<br><br>' +
                '<div style="max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0;">' +
                '<strong>内容预览：</strong><br>' + text.replace(/\n/g, '<br>') + '</div>', {
                btn: ['确定添加', '取消'],
                icon: 3,
                title: '添加到内容模板',
                area: ['500px', 'auto']
            }, function(index) {
                layer.close(index);

                // 显示加载状态
                var loadIndex = layer.load(1, { shade: [0.3, '#000'] });

                $.ajax({
                    url: '/MsgAudit/AddToContentTemplate',
                    type: 'post',
                    data: {
                        text: text,
                        userId: 0 // 0表示全部用户
                    },
                    dataType: 'json',
                    success: function (data) {
                        layer.close(loadIndex);

                        if (data.success) {
                            layer.msg(data.message || '添加成功', { icon: 1, time: 2000 });
                        } else {
                            layer.alert(data.error || '添加失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadIndex);
                        layer.alert('添加失败，请重试', { icon: 2 });
                    }
                });
            });
        }

        /*切换模板标签页*/
        function switchTemplateTab(index, mode) {
            // 更新标签按钮状态
            $('.template-tabs[data-index="' + index + '"] .template-tab-btn').removeClass('active');
            $('.template-tabs[data-index="' + index + '"] .template-tab-btn[data-tab="' + mode + '"]').addClass('active');

            // 切换内容显示
            $('.template-content[data-index="' + index + '"]').hide();
            $('.template-content[data-index="' + index + '"][data-mode="' + mode + '"]').show();
        }

        /*添加单个模板*/
        function addSingleTemplate(index, mode) {
            mode = mode || 'strict'; // 默认严格模式
            var templateInput = $('.template-input[data-index="' + index + '"][data-mode="' + mode + '"]');
            var templateContent = templateInput.val().trim();

            if (!templateContent) {
                layer.msg('模板内容不能为空', { icon: 5, time: 1000 });
                return;
            }

            var modeText = mode === 'strict' ? '严格模式' : (mode === 'loose' ? '宽松模式' : '宽松模式（主域名）');
            var modeIcon = mode === 'strict' ? '📋' : (mode === 'loose' ? '🔓' : '🌐');

            layer.confirm('确定要添加此' + modeText + '模板到内容链接号码模板吗？<br><br>' +
                '<div style="max-height: 150px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0;">' +
                '<strong>' + modeIcon + ' ' + modeText + '模板内容：</strong><br>' + templateContent.replace(/\n/g, '<br>') + '</div>', {
                btn: ['确定添加', '取消'],
                icon: 3,
                title: '添加' + modeText + '模板',
                area: ['500px', 'auto']
            }, function(layerIndex) {
                layer.close(layerIndex);

                var loadIndex = layer.load(1, { shade: [0.3, '#000'] });

                $.ajax({
                    url: '/MsgAudit/AddToContentTemplate',
                    type: 'post',
                    data: {
                        text: templateContent,
                        userId: 0
                    },
                    dataType: 'json',
                    success: function (data) {
                        layer.close(loadIndex);

                        if (data.success) {
                            layer.msg(data.message || '添加成功', { icon: 1, time: 2000 });
                        } else {
                            layer.alert(data.error || '添加失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadIndex);
                        layer.alert('添加失败，请重试', { icon: 2 });
                    }
                });
            });
        }

        /*全选模板*/
        function selectAllTemplates() {
            $('.template-checkbox').prop('checked', true);
            layer.msg('已全选所有模板', { icon: 1, time: 1000 });
        }

        /*清空选择*/
        function clearAllSelections() {
            $('.template-checkbox').prop('checked', false);
            layer.msg('已清空所有选择', { icon: 1, time: 1000 });
        }

        /*一键批量添加全部模板*/
        function batchAddAllTemplates(mode) {
            mode = mode || 'strict'; // 默认严格模式
            var templates = [];
            var selector = mode === 'strict' ? '.strict-template' : (mode === 'loose' ? '.loose-template' : '.loose-domain-template');

            $(selector).each(function() {
                var content = $(this).val().trim();
                if (content) {
                    templates.push(content);
                }
            });

            if (templates.length === 0) {
                var modeText = mode === 'strict' ? '严格模式' : (mode === 'loose' ? '宽松模式' : '宽松模式（主域名）');
                layer.msg('没有可添加的' + modeText + '模板内容', { icon: 5, time: 1000 });
                return;
            }

            var modeText = mode === 'strict' ? '严格模式' : (mode === 'loose' ? '宽松模式' : '宽松模式（主域名）');
            batchAddSelectedTemplates(templates, '全部' + modeText);
        }

        /*批量添加选中的模板*/
        function batchAddSelectedTemplates(templates, type) {
            if (!templates || templates.length === 0) {
                // 如果没有传入模板，则获取选中的模板
                templates = [];
                $('.template-checkbox:checked').each(function() {
                    var index = $(this).data('index');
                    var content = $('.template-input[data-index="' + index + '"]').val().trim();
                    if (content) {
                        templates.push(content);
                    }
                });
                type = '选中';
            }

            if (templates.length === 0) {
                layer.msg('没有选中的模板内容', { icon: 5, time: 1000 });
                return;
            }

            // 创建模板预览内容，支持预览和编辑两种模式
            var previewContent = '<div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">';
            previewContent += '<strong>共 ' + templates.length + ' 个模板：</strong>';
            previewContent += '<div>';
            previewContent += '<button type="button" id="previewModeBtn" onclick="switchBatchMode(\'preview\')" style="margin-right: 5px; padding: 4px 12px; background: #2196f3; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">📋 预览模式</button>';
            previewContent += '<button type="button" id="editModeBtn" onclick="switchBatchMode(\'edit\')" style="padding: 4px 12px; background: #f5f5f5; color: #666; border: 1px solid #ddd; border-radius: 3px; font-size: 12px; cursor: pointer;">✏️ 编辑模式</button>';
            previewContent += '</div>';
            previewContent += '</div>';

            // 预览模式容器
            previewContent += '<div id="previewModeContainer" style="background: #f5f5f5; border-radius: 3px; margin: 10px 0;">';
            previewContent += '<div style="padding: 8px 10px; border-bottom: 1px solid #e0e0e0; background: #fafafa;">';
            previewContent += '<div style="margin-bottom: 8px; font-size: 12px; color: #666;">';
            previewContent += '📋 预览模式 - 总计 ' + templates.length + ' 个模板，总字符数: ' + templates.reduce(function(sum, t) { return sum + t.length; }, 0);
            previewContent += '</div>';
            previewContent += '<input type="text" id="templateSearchInput" placeholder="🔍 搜索模板内容..." style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;" oninput="searchBatchTemplates(this.value)">';
            previewContent += '</div>';
            previewContent += '<div style="max-height: 350px; overflow-y: auto; padding: 10px;">';
            templates.forEach(function(template, index) {
                var displayText = template.length > 80 ? template.substring(0, 80) + '...' : template;
                previewContent += '<div style="margin-bottom: 5px; padding: 6px 8px 6px 8px; background: white; border-radius: 3px; border: 1px solid #ddd; font-size: 12px; font-family: monospace; cursor: pointer; transition: all 0.2s; position: relative; min-height: 32px; padding-right: 120px;" onclick="editSingleTemplate(' + index + ')" title="点击编辑此模板" onmouseover="this.style.backgroundColor=\'#f0f8ff\'; this.style.borderColor=\'#2196f3\';" onmouseout="this.style.backgroundColor=\'white\'; this.style.borderColor=\'#ddd\';">';
                previewContent += '<span style="color: #666; font-weight: bold; margin-right: 8px; min-width: 30px; display: inline-block;">' + (index + 1) + '.</span>';
                previewContent += '<span class="template-preview-text" data-index="' + index + '" style="word-break: break-all; line-height: 1.4;">' + displayText + '</span>';
                previewContent += '<div style="position: absolute; right: 8px; top: 4px; text-align: right; line-height: 1.2;">';
                previewContent += '<div style="color: #999; font-size: 11px; margin-bottom: 2px;">(' + template.length + '字符)</div>';
                previewContent += '<div style="color: #2196f3; font-size: 10px; opacity: 0.7;">✏️ 点击编辑</div>';
                previewContent += '</div>';
                previewContent += '</div>';
            });
            previewContent += '</div>'; // 关闭模板列表容器
            previewContent += '</div>'; // 关闭预览模式容器

            // 编辑模式容器
            previewContent += '<div id="editModeContainer" style="background: #f5f5f5; border-radius: 3px; margin: 10px 0; display: none;">';
            previewContent += '<div style="padding: 8px 10px; border-bottom: 1px solid #e0e0e0; background: #fafafa; font-size: 12px; color: #666;">';
            previewContent += '📝 编辑模式 - 总计 ' + templates.length + ' 个模板，总字符数: ' + templates.reduce(function(sum, t) { return sum + t.length; }, 0);
            previewContent += '</div>';
            previewContent += '<div style="max-height: 350px; overflow-y: auto; padding: 10px;">';
            templates.forEach(function(template, index) {
                previewContent += '<div style="margin-bottom: 10px; padding: 8px; background: white; border-radius: 3px; border: 1px solid #ddd; position: relative;">';
                previewContent += '<div style="margin-bottom: 5px; font-weight: bold; color: #666; display: flex; justify-content: space-between; align-items: center;">';
                previewContent += '<span>模板 ' + (index + 1) + ':</span>';
                previewContent += '<span style="font-size: 11px; color: #999;">字符数: <span class="char-count" data-index="' + index + '">' + template.length + '</span></span>';
                previewContent += '</div>';
                previewContent += '<textarea class="batch-template-input" data-index="' + index + '" style="width: 100%; min-height: 60px; padding: 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; resize: vertical; font-family: monospace;" oninput="updateCharCount(' + index + ')">' + template + '</textarea>';
                previewContent += '</div>';
            });
            previewContent += '</div>'; // 关闭模板列表容器
            previewContent += '</div>'; // 关闭编辑模式容器

            // 添加操作按钮（只在编辑模式显示）
            previewContent += '<div id="batchOperations" style="margin: 10px 0; padding: 8px; background: #f0f8ff; border-radius: 3px; border: 1px solid #e1f5fe; display: none;">';
            previewContent += '<div style="margin-bottom: 8px;"><strong>🛠️ 批量操作：</strong></div>';
            previewContent += '<button type="button" onclick="selectAllBatchTemplates()" style="margin-right: 5px; padding: 4px 8px; background: #4caf50; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">全选文本</button>';
            previewContent += '<button type="button" onclick="clearAllBatchTemplates()" style="margin-right: 5px; padding: 4px 8px; background: #f44336; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">清空全部</button>';
            previewContent += '<button type="button" onclick="removeEmptyBatchTemplates()" style="margin-right: 5px; padding: 4px 8px; background: #ff9800; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">移除空模板</button>';
            previewContent += '</div>';

            previewContent += '<div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 3px; font-size: 12px; color: #666;">';
            previewContent += '<strong>💡 提示：</strong><span id="modeHint">预览模式下点击任意模板可快速编辑，或切换到编辑模式进行批量操作。</span>';
            previewContent += '</div>';

            layer.open({
                type: 1,
                title: '批量添加模板 - 预览和编辑',
                area: ['800px', '600px'],
                content: previewContent,
                btn: ['确定批量添加', '取消'],
                yes: function(layerIndex) {
                    // 获取编辑后的模板内容
                    var editedTemplates = [];
                    $('.batch-template-input').each(function() {
                        var content = $(this).val().trim();
                        if (content) {
                            editedTemplates.push(content);
                        }
                    });

                    if (editedTemplates.length === 0) {
                        layer.msg('没有有效的模板内容', { icon: 5, time: 1000 });
                        return;
                    }

                    layer.close(layerIndex);
                    var loadIndex = layer.load(1, { shade: [0.3, '#000'] });

                $.ajax({
                    url: '/MsgAudit/BatchAddToContentTemplate',
                    type: 'post',
                    data: JSON.stringify(editedTemplates),
                    contentType: 'application/json',
                    dataType: 'json',
                    success: function (data) {
                        layer.close(loadIndex);

                        if (data.success) {
                            var message = data.message || '批量添加完成';
                            if (data.failCount > 0) {
                                message += '<br><br><strong>详细结果：</strong><br>';
                                message += '成功：' + data.successCount + ' 条<br>';
                                message += '失败：' + data.failCount + ' 条';
                                if (data.errors && data.errors.length > 0) {
                                    message += '<br><br><strong>失败原因：</strong><br>';
                                    message += data.errors.slice(0, 3).join('<br>');
                                    if (data.errors.length > 3) {
                                        message += '<br>... 还有 ' + (data.errors.length - 3) + ' 个错误';
                                    }
                                }
                            }
                            layer.alert(message, { icon: 1, area: ['500px', 'auto'] });
                        } else {
                            layer.alert(data.error || '批量添加失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadIndex);
                        layer.alert('批量添加失败，请重试', { icon: 2 });
                    }
                });
                },
                btn2: function(layerIndex) {
                    // 取消按钮
                    layer.close(layerIndex);
                }
            });
        }

        /*更新字符计数*/
        function updateCharCount(index) {
            var textarea = $('.batch-template-input[data-index="' + index + '"]');
            var charCount = $('.char-count[data-index="' + index + '"]');
            if (textarea.length && charCount.length) {
                charCount.text(textarea.val().length);
            }
        }

        /*全选所有批量模板文本*/
        function selectAllBatchTemplates() {
            $('.batch-template-input').each(function() {
                this.select();
            });
            layer.msg('已选中所有模板文本', { icon: 1, time: 1000 });
        }

        /*清空所有批量模板*/
        function clearAllBatchTemplates() {
            layer.confirm('确定要清空所有模板内容吗？', { icon: 3 }, function(index) {
                $('.batch-template-input').val('');
                $('.char-count').text('0');
                layer.close(index);
                layer.msg('已清空所有模板', { icon: 1, time: 1000 });
            });
        }

        /*移除空的批量模板*/
        function removeEmptyBatchTemplates() {
            var removedCount = 0;
            $('.batch-template-input').each(function() {
                if ($(this).val().trim() === '') {
                    $(this).closest('div[style*="margin-bottom: 10px"]').remove();
                    removedCount++;
                }
            });
            if (removedCount > 0) {
                layer.msg('已移除 ' + removedCount + ' 个空模板', { icon: 1, time: 1000 });
            } else {
                layer.msg('没有找到空模板', { icon: 0, time: 1000 });
            }
        }

        /*切换批量模板模式*/
        function switchBatchMode(mode) {
            if (mode === 'preview') {
                // 切换到预览模式
                $('#previewModeContainer').show();
                $('#editModeContainer').hide();
                $('#batchOperations').hide();

                // 更新按钮样式
                $('#previewModeBtn').css({
                    'background': '#2196f3',
                    'color': 'white',
                    'border': 'none'
                });
                $('#editModeBtn').css({
                    'background': '#f5f5f5',
                    'color': '#666',
                    'border': '1px solid #ddd'
                });

                // 更新提示文本
                $('#modeHint').text('预览模式下点击任意模板可快速编辑，或切换到编辑模式进行批量操作。');
            } else if (mode === 'edit') {
                // 切换到编辑模式
                $('#previewModeContainer').hide();
                $('#editModeContainer').show();
                $('#batchOperations').show();

                // 更新按钮样式
                $('#editModeBtn').css({
                    'background': '#2196f3',
                    'color': 'white',
                    'border': 'none'
                });
                $('#previewModeBtn').css({
                    'background': '#f5f5f5',
                    'color': '#666',
                    'border': '1px solid #ddd'
                });

                // 更新提示文本
                $('#modeHint').text('编辑模式下可以修改每个模板内容，使用批量操作功能，修改后点击"确定批量添加"即可。');
            }
        }

        /*编辑单个模板*/
        function editSingleTemplate(index) {
            var currentText = $('.batch-template-input[data-index="' + index + '"]').val();

            layer.prompt({
                title: '编辑模板 ' + (index + 1),
                formType: 2, // 多行文本
                value: currentText,
                area: ['600px', '200px'],
                maxlength: 1000
            }, function(value, layerIndex) {
                if (value !== null && value !== undefined) {
                    // 更新文本框的值
                    $('.batch-template-input[data-index="' + index + '"]').val(value);

                    // 更新预览显示
                    var displayText = value.length > 80 ? value.substring(0, 80) + '...' : value;
                    $('.template-preview-text[data-index="' + index + '"]').text(displayText);

                    // 更新字符计数
                    $('.char-count[data-index="' + index + '"]').text(value.length);

                    // 更新预览模式中的字符数显示
                    $('.template-preview-text[data-index="' + index + '"]').parent().find('div:last div:first').text('(' + value.length + '字符)');

                    layer.close(layerIndex);
                    layer.msg('模板已更新', { icon: 1, time: 1000 });
                }
            });
        }

        /*搜索批量模板*/
        function searchBatchTemplates(searchText) {
            var searchLower = searchText.toLowerCase();
            var visibleCount = 0;

            $('#previewModeContainer .template-preview-text').each(function() {
                var templateDiv = $(this).closest('div[onclick]');
                var templateText = $('.batch-template-input[data-index="' + $(this).data('index') + '"]').val().toLowerCase();

                if (searchText === '' || templateText.indexOf(searchLower) !== -1) {
                    templateDiv.show();
                    visibleCount++;
                } else {
                    templateDiv.hide();
                }
            });

            // 显示搜索结果统计
            if (searchText !== '') {
                if (visibleCount === 0) {
                    if ($('#searchResultHint').length === 0) {
                        $('#previewModeContainer').append('<div id="searchResultHint" style="text-align: center; padding: 20px; color: #999; font-size: 14px;">未找到匹配的模板</div>');
                    }
                } else {
                    $('#searchResultHint').remove();
                }
            } else {
                $('#searchResultHint').remove();
            }
        }

        /*切换合并详情显示*/
        function toggleGroupDetails(index) {
            var detailsDiv = $('#groupDetails' + index);
            var toggleBtn = $('#groupToggle' + index);

            if (detailsDiv.is(':visible')) {
                detailsDiv.slideUp(200);
                toggleBtn.html('📋 查看合并详情 (' + toggleBtn.text().match(/\d+/)[0] + ' 条)');
            } else {
                detailsDiv.slideDown(200);
                toggleBtn.html('📋 收起合并详情 (' + toggleBtn.text().match(/\d+/)[0] + ' 条)');
            }
        }

        /*切换单个条目的完整内容显示*/
        function toggleFullText(groupIndex, itemIndex) {
            var shortTextSpan = $('#shortText' + groupIndex + '_' + itemIndex);
            var fullTextSpan = $('#fullText' + groupIndex + '_' + itemIndex);
            var toggleBtn = $('#toggleBtn' + groupIndex + '_' + itemIndex);

            if (fullTextSpan.is(':visible')) {
                // 当前显示完整内容，切换到简短内容
                fullTextSpan.hide();
                shortTextSpan.show();
                toggleBtn.text('显示完整内容');
            } else {
                // 当前显示简短内容，切换到完整内容
                shortTextSpan.hide();
                fullTextSpan.show();
                toggleBtn.text('收起内容');
            }
        }
</script>

<script type="text/javascript" src="~/Scripts/My97DatePicker/4.8/WdatePicker.js"></script>