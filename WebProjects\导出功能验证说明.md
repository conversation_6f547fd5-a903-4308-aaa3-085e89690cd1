# 移动签名报备导出功能验证说明

## 导出功能现状检查

### ✅ 表头结构正确
导出的Excel模板包含正确的列结构：
```
列15: "身份证照片(反正面)"
列16: "身份证材料1" 
列17: "身份证材料2"
列18: "身份证材料3"
列19: "身份证材料4"
列20: "身份证材料5"
列21: "扩展号"
列22: "内容"
```

### ✅ 数据分离逻辑正确
导出时会将数据库中合并的身份证图片数据正确分离：

1. **数据读取**：从`item.IdCardPhotos`字段读取合并的图片URL（用分号分隔）
2. **数据分割**：`idCardImages = item.IdCardPhotos.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries)`
3. **分列放置**：
   - 第1张图片 → 身份证照片(反正面)列（第15列）
   - 第2张图片 → 身份证材料1列（第16列）
   - 第3张图片 → 身份证材料2列（第17列）
   - 第4张图片 → 身份证材料3列（第18列）
   - 第5张图片 → 身份证材料4列（第19列）
   - 第6张图片 → 身份证材料5列（第20列）

### ✅ 图片处理逻辑
对于每个图片URL：
1. **优先使用DISPIMG公式**：调用`InsertImageAndDispimgFormula`方法
2. **备用文本显示**：如果DISPIMG插入失败，则显示图片URL文本

### ✅ 空值处理
- 如果某个位置没有图片，对应的列会显示为空
- 不会因为缺少图片而导致列错位

## 验证测试步骤

### 1. 准备测试数据
创建包含多个身份证图片的测试记录：
```sql
-- 示例：包含3张身份证图片的记录
UPDATE T_MOBILE_SIGN_REPORT 
SET ID_CARD_PHOTOS = '/UploadFile/MobileSignReport/img1.jpg;/UploadFile/MobileSignReport/img2.jpg;/UploadFile/MobileSignReport/img3.jpg'
WHERE ID = [测试记录ID];
```

### 2. 执行导出测试
1. 访问移动签名报备页面
2. 选择包含多图片数据的记录
3. 点击"导出Excel"
4. 下载并打开导出的Excel文件

### 3. 验证导出结果
检查导出的Excel文件：

#### 表头验证：
- [ ] 第15列：身份证照片(反正面)
- [ ] 第16列：身份证材料1
- [ ] 第17列：身份证材料2  
- [ ] 第18列：身份证材料3
- [ ] 第19列：身份证材料4
- [ ] 第20列：身份证材料5
- [ ] 第21列：扩展号
- [ ] 第22列：内容

#### 数据验证：
- [ ] 第1张图片正确显示在"身份证照片(反正面)"列
- [ ] 第2张图片正确显示在"身份证材料1"列
- [ ] 第3张图片正确显示在"身份证材料2"列
- [ ] 后续列为空（如果只有3张图片）
- [ ] 图片以DISPIMG公式格式显示（如：`=DISPIMG("图片ID",1)`）
- [ ] 扩展号和内容列位置正确

### 4. 边界情况测试

#### 测试场景1：单张图片
```
数据库存储：/UploadFile/MobileSignReport/single.jpg
预期结果：
- 身份证照片(反正面)列：显示图片
- 身份证材料1-5列：全部为空
```

#### 测试场景2：满6张图片
```
数据库存储：img1.jpg;img2.jpg;img3.jpg;img4.jpg;img5.jpg;img6.jpg
预期结果：
- 所有6个身份证相关列都有图片显示
```

#### 测试场景3：超过6张图片
```
数据库存储：img1.jpg;img2.jpg;img3.jpg;img4.jpg;img5.jpg;img6.jpg;img7.jpg
预期结果：
- 只显示前6张图片
- 第7张图片被忽略
```

#### 测试场景4：空数据
```
数据库存储：NULL 或 空字符串
预期结果：
- 所有6个身份证相关列都为空
```

## 可能的问题和解决方案

### 问题1：DISPIMG公式插入失败
**现象**：图片列显示URL文本而不是图片
**原因**：`InsertImageAndDispimgFormula`方法失败
**解决**：检查图片文件是否存在，图片ID生成是否正确

### 问题2：列位置错乱
**现象**：扩展号、内容等列的数据显示在错误位置
**原因**：列索引计算错误
**解决**：验证列索引：扩展号=21，内容=22

### 问题3：图片分离错误
**现象**：图片显示在错误的列中
**原因**：分号分割或数组索引问题
**解决**：检查`Split`方法和数组访问逻辑

## 总结

根据代码检查，导出功能已经正确实现了：
- ✅ 表头包含6个身份证相关列
- ✅ 数据分离逻辑正确
- ✅ 列索引计算正确
- ✅ 空值处理完善

建议进行实际的导出测试来验证功能是否完全符合预期。
