<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>文件上传问题诊断指南</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .diagnosis-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-example {
            background-color: #f8f8f8;
            padding: 10px;
            border-left: 4px solid #337ab7;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .fix-item {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #5cb85c;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 文件上传问题诊断指南</h1>
        
        <div class="diagnosis-section">
            <h3>🎯 问题描述</h3>
            <div class="alert alert-warning">
                <strong>现象：</strong>同一个Excel文件第一次上传成功，第二次上传时报错
            </div>
        </div>

        <div class="diagnosis-section">
            <h3>✅ 已实施的修复</h3>
            
            <div class="fix-item">
                <h4>1. 服务器端文件流处理优化</h4>
                <ul>
                    <li>强制重置文件流位置到0</li>
                    <li>将文件内容完整读取到字节数组</li>
                    <li>使用独立的内存流创建工作簿</li>
                    <li>添加详细的文件读取日志</li>
                </ul>
            </div>
            
            <div class="fix-item">
                <h4>2. 前端缓存清除机制</h4>
                <ul>
                    <li>添加时间戳避免请求缓存</li>
                    <li>禁用Ajax缓存</li>
                    <li>导入完成后清除文件输入</li>
                    <li>添加前端导入ID跟踪</li>
                </ul>
            </div>
            
            <div class="fix-item">
                <h4>3. 资源管理改进</h4>
                <ul>
                    <li>添加finally块确保workbook释放</li>
                    <li>增强异常处理和错误日志</li>
                    <li>防止重复点击导入按钮</li>
                </ul>
            </div>
            
            <div class="fix-item">
                <h4>4. UserId设置修复</h4>
                <ul>
                    <li>确保导入时正确设置UserId</li>
                    <li>避免重复检查误判</li>
                </ul>
            </div>
        </div>

        <div class="diagnosis-section">
            <h3>📊 预期的日志输出</h3>
            <p>修复后，你应该在服务器日志中看到：</p>
            <div class="log-example">[导入a1b2c3d4] 开始导入，通道ID：1
[导入a1b2c3d4] 文件上传诊断：
  - 文件名：test.xlsx
  - 内容长度：12345字节
  - 内容类型：application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
  - 输入流是否可读：True
  - 输入流是否可寻址：True
  - 输入流当前位置：0
  - 输入流长度：12345
[导入a1b2c3d4] 开始读取Excel文件，文件扩展名：.xlsx
[导入a1b2c3d4] 流位置已重置为0
[导入a1b2c3d4] 文件读取完成，字节数：12345
[导入a1b2c3d4] Excel工作簿创建成功，工作表数量：1
[导入a1b2c3d4] 第2行重复检查：签名=测试签名, 用户ID=123, 通道ID=1, 扩展号=NULL, 内容长度=0
[导入a1b2c3d4] 导入完成：成功1条，总行数1
[导入a1b2c3d4] 返回成功结果：成功导入1条记录
[导入a1b2c3d4] 资源清理完成</div>
            
            <p>在浏览器控制台中看到：</p>
            <div class="log-example">[前端import_1234567890_abc123def] 开始导入，通道ID：1，文件：test.xlsx
[前端import_1234567890_abc123def] 导入完成，结果：{code: 0, msg: "成功导入1条记录"}
[前端import_1234567890_abc123def] 文件输入已清除</div>
        </div>

        <div class="diagnosis-section">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>清除浏览器缓存</strong>：Ctrl+Shift+Delete 清除缓存</li>
                <li><strong>打开开发者工具</strong>：F12，查看Console和Network标签</li>
                <li><strong>第一次上传</strong>：
                    <ul>
                        <li>选择通道1</li>
                        <li>选择Excel文件</li>
                        <li>点击导入</li>
                        <li>观察日志输出</li>
                    </ul>
                </li>
                <li><strong>第二次上传</strong>：
                    <ul>
                        <li>选择通道2（不同通道）</li>
                        <li>重新选择相同Excel文件</li>
                        <li>点击导入</li>
                        <li>观察是否成功</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="diagnosis-section">
            <h3>🚨 如果问题仍然存在</h3>
            <div class="alert alert-danger">
                <h4>可能的其他原因：</h4>
                <ol>
                    <li><strong>IIS/应用程序池问题</strong>：重启应用程序池</li>
                    <li><strong>文件锁定</strong>：确保Excel文件没有被其他程序打开</li>
                    <li><strong>权限问题</strong>：检查上传目录权限</li>
                    <li><strong>内存不足</strong>：检查服务器内存使用情况</li>
                    <li><strong>数据库连接</strong>：检查数据库连接池状态</li>
                </ol>
            </div>
        </div>

        <div class="diagnosis-section">
            <h3>📝 需要收集的信息</h3>
            <div class="panel panel-default">
                <div class="panel-body">
                    <h4>如果问题仍然存在，请提供：</h4>
                    <ol>
                        <li><strong>完整的错误消息</strong>（前端alert和后端异常）</li>
                        <li><strong>服务器日志</strong>（包含[导入xxxxxxxx]的完整日志）</li>
                        <li><strong>浏览器控制台日志</strong>（Console和Network标签的内容）</li>
                        <li><strong>Excel文件信息</strong>：
                            <ul>
                                <li>文件大小</li>
                                <li>文件格式（.xls或.xlsx）</li>
                                <li>数据行数</li>
                                <li>是否包含图片</li>
                            </ul>
                        </li>
                        <li><strong>环境信息</strong>：
                            <ul>
                                <li>浏览器类型和版本</li>
                                <li>服务器操作系统</li>
                                <li>.NET Framework版本</li>
                                <li>IIS版本</li>
                            </ul>
                        </li>
                        <li><strong>操作步骤</strong>：
                            <ul>
                                <li>第一次导入的通道ID</li>
                                <li>第二次导入的通道ID</li>
                                <li>两次操作的时间间隔</li>
                                <li>是否刷新了页面</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="diagnosis-section">
            <h3>🔍 高级诊断</h3>
            <div class="alert alert-info">
                <h4>如果需要更深入的诊断：</h4>
                <ol>
                    <li><strong>启用详细日志</strong>：在web.config中设置日志级别为Debug</li>
                    <li><strong>使用Fiddler</strong>：抓取HTTP请求详情</li>
                    <li><strong>检查临时文件</strong>：查看ASP.NET临时文件目录</li>
                    <li><strong>内存分析</strong>：使用性能监视器检查内存使用</li>
                    <li><strong>数据库监控</strong>：检查数据库连接和锁定情况</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
