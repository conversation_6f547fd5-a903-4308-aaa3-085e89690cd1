using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using YLHT.GP.Common;
using YLHT.GP.Common.Data;
using YLHT.GP.Models;

namespace YLHT.GP.DataSource
{
    /// <summary>
    /// 移动签名报备数据访问类
    /// </summary>
    public partial class YLHTSmsWebDataSource
    {
        /// <summary>
        /// 获取移动签名报备列表
        /// </summary>
        /// <param name="model">查询条件</param>
        /// <param name="page">当前页</param>
        /// <param name="rows">每页显示条数</param>
        /// <param name="sort">排序字段</param>
        /// <param name="order">排序方式</param>
        /// <param name="rowsCount">总记录数</param>
        /// <param name="pageCount">总页数</param>
        /// <returns></returns>
        public List<MobileSignReportModel> GetMobileSignReportList(MobileSignReportModel model, int page, int rows, string sort, string order, out int rowsCount, out int pageCount)
        {
            try
            {
                string sql = @"
                    SELECT
                        a.ID,
                        a.REPORT_DATE AS ReportDate,
                        a.TYPE,
                        a.ACCOUNT_NAME AS AccountName,
                        a.SIGN_TYPE AS SignType,
                        a.SIGN,
                        a.SIGN_COMPANY_NAME AS SignCompanyName,
                        a.UNIFIED_SOCIAL_CREDIT_CODE AS UnifiedSocialCreditCode,
                        a.LEGAL_PERSON AS LegalPerson,
                        a.LEGAL_PERSON_NAME AS LegalPersonName,
                        a.LEGAL_PERSON_ID_CARD AS LegalPersonIdCard,
                        a.LEGAL_PERSON_PHONE AS LegalPersonPhone,
                        a.DRAINAGE_LINK AS DrainageLink,
                        a.DRAINAGE_NUMBER1 AS DrainageNumber1,
                        a.DRAINAGE_NUMBER2 AS DrainageNumber2,
                        a.SIGN_MATERIALS AS SignMaterials,
                        a.ID_CARD_PHOTOS AS IdCardPhotos,
                        a.EXT_NUMBER AS ExtNumber,
                        a.CONTENT,
                        a.CHANNEL_ID AS ChannelId,
                        b.CHANNELNAME AS ChannelName,
                        a.USER_ID AS UserId,
                        c.USERNAME AS UserName,
                        a.STATUS,
                        a.ADD_TIME AS AddTime,
                        a.ADMIN_ID AS AdminId,
                        d.ADMINNAME AS AdminName,
                        a.SIGN_STATUS AS SignStatus
                    FROM
                        T_MOBILE_SIGN_REPORT a
                        LEFT JOIN base_channel b ON a.CHANNEL_ID = b.CHANNELid
                        LEFT JOIN base_user c ON a.USER_ID = c.USERid
                        LEFT JOIN base_admin d ON a.ADMIN_ID = d.ADMINID
                    WHERE
                        1=1 ";

                DynamicParameters parameters = new DynamicParameters();

                if (model != null)
                {
                    if (model.ReportDate.HasValue)
                    {
                        sql += " AND TRUNC(a.REPORT_DATE) = TRUNC(:ReportDate) ";
                        parameters.Add("ReportDate", model.ReportDate);
                    }

                    if (!string.IsNullOrEmpty(model.Type))
                    {
                        sql += " AND a.TYPE LIKE '%' || :Type || '%' ";
                        parameters.Add("Type", model.Type);
                    }

                    if (!string.IsNullOrEmpty(model.AccountName))
                    {
                        sql += " AND a.ACCOUNT_NAME LIKE '%' || :AccountName || '%' ";
                        parameters.Add("AccountName", model.AccountName);
                    }

                    if (!string.IsNullOrEmpty(model.SignType))
                    {
                        sql += " AND a.SIGN_TYPE LIKE '%' || :SignType || '%' ";
                        parameters.Add("SignType", model.SignType);
                    }

                    if (!string.IsNullOrEmpty(model.Sign))
                    {
                        sql += " AND a.SIGN LIKE '%' || :Sign || '%' ";
                        parameters.Add("Sign", model.Sign);
                    }

                    if (!string.IsNullOrEmpty(model.SignCompanyName))
                    {
                        sql += " AND a.SIGN_COMPANY_NAME LIKE '%' || :SignCompanyName || '%' ";
                        parameters.Add("SignCompanyName", model.SignCompanyName);
                    }

                    if (!string.IsNullOrEmpty(model.UnifiedSocialCreditCode))
                    {
                        sql += " AND a.UNIFIED_SOCIAL_CREDIT_CODE LIKE '%' || :UnifiedSocialCreditCode || '%' ";
                        parameters.Add("UnifiedSocialCreditCode", model.UnifiedSocialCreditCode);
                    }

                    if (!string.IsNullOrEmpty(model.LegalPersonName))
                    {
                        sql += " AND a.LEGAL_PERSON_NAME LIKE '%' || :LegalPersonName || '%' ";
                        parameters.Add("LegalPersonName", model.LegalPersonName);
                    }

                    if ( model.ChannelId > 0)
                    {
                        sql += " AND a.CHANNEL_ID = :ChannelId ";
                        parameters.Add("ChannelId", model.ChannelId);
                    }

                    if ( model.UserId > 0)
                    {
                        sql += " AND a.USER_ID = :UserId ";
                        parameters.Add("UserId", model.UserId);
                    }

                    if (model.Status>0)
                    {
                        sql += " AND a.STATUS = :Status ";
                        parameters.Add("Status", model.Status);
                    }
                }

                // 先执行计数查询
                string countSql = string.Format("SELECT COUNT(1) FROM ({0}) t", sql);
                rowsCount = (int)this.QueryRecord<decimal>(countSql, parameters);
                pageCount = (int)Math.Ceiling(rowsCount * 1.0 / rows);

                // 构建排序和分页查询
                string orderBy = string.IsNullOrEmpty(sort) ? "a.ID" : string.Format("a.{0}", sort.ToUpper());
                string orderDirection = order.ToUpper() == "ASC" ? "ASC" : "DESC";
                sql += string.Format(" ORDER BY {0} {1}", orderBy, orderDirection);

                // 分页查询
                sql = string.Format(@"
                    SELECT * FROM (
                        SELECT t.*, ROWNUM AS rn FROM ({0}) t WHERE ROWNUM <= :endIndex
                    ) WHERE rn > :startIndex", sql);

                // 为分页查询创建新的参数集合，包含原有参数和分页参数
                DynamicParameters pageParameters = new DynamicParameters();

                // 复制原有参数
                if (model != null)
                {
                    if (model.ReportDate.HasValue)
                    {
                        pageParameters.Add("ReportDate", model.ReportDate);
                    }
                    if (!string.IsNullOrEmpty(model.Type))
                    {
                        pageParameters.Add("Type", model.Type);
                    }
                    if (!string.IsNullOrEmpty(model.AccountName))
                    {
                        pageParameters.Add("AccountName", model.AccountName);
                    }
                    if (!string.IsNullOrEmpty(model.SignType))
                    {
                        pageParameters.Add("SignType", model.SignType);
                    }
                    if (!string.IsNullOrEmpty(model.Sign))
                    {
                        pageParameters.Add("Sign", model.Sign);
                    }
                    if (!string.IsNullOrEmpty(model.SignCompanyName))
                    {
                        pageParameters.Add("SignCompanyName", model.SignCompanyName);
                    }
                    if (!string.IsNullOrEmpty(model.UnifiedSocialCreditCode))
                    {
                        pageParameters.Add("UnifiedSocialCreditCode", model.UnifiedSocialCreditCode);
                    }
                    if (!string.IsNullOrEmpty(model.LegalPersonName))
                    {
                        pageParameters.Add("LegalPersonName", model.LegalPersonName);
                    }
                    if (model.ChannelId > 0)
                    {
                        pageParameters.Add("ChannelId", model.ChannelId);
                    }
                    if (model.UserId > 0)
                    {
                        pageParameters.Add("UserId", model.UserId);
                    }
                    if (model.Status > 0)
                    {
                        pageParameters.Add("Status", model.Status);
                    }
                }

                // 添加分页参数
                pageParameters.Add("startIndex", (page - 1) * rows);
                pageParameters.Add("endIndex", page * rows);

                Trace.TraceInformation(string.Format("sql={0},parameters={1}", sql, string.Join(",", pageParameters.ParameterNames)));
                return this.Query<MobileSignReportModel>(sql, pageParameters).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(string.Format("GetMobileSignReportList方法异常，ex={0}", ex));
                rowsCount = 0;
                pageCount = 0;
                return new List<MobileSignReportModel>();
            }
        }

        /// <summary>
        /// 根据ID获取移动签名报备
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public MobileSignReportModel GetMobileSignReportById(int id)
        {
            try
            {
                string sql = @"
                    SELECT 
                        a.ID, 
                        a.REPORT_DATE AS ReportDate, 
                        a.TYPE, 
                        a.ACCOUNT_NAME AS AccountName, 
                        a.SIGN_TYPE AS SignType, 
                        a.SIGN, 
                        a.SIGN_COMPANY_NAME AS SignCompanyName, 
                        a.UNIFIED_SOCIAL_CREDIT_CODE AS UnifiedSocialCreditCode, 
                        a.LEGAL_PERSON AS LegalPerson, 
                        a.LEGAL_PERSON_NAME AS LegalPersonName, 
                        a.LEGAL_PERSON_ID_CARD AS LegalPersonIdCard, 
                        a.LEGAL_PERSON_PHONE AS LegalPersonPhone, 
                        a.DRAINAGE_LINK AS DrainageLink, 
                        a.DRAINAGE_NUMBER1 AS DrainageNumber1, 
                        a.DRAINAGE_NUMBER2 AS DrainageNumber2, 
                        a.SIGN_MATERIALS AS SignMaterials, 
                        a.ID_CARD_PHOTOS AS IdCardPhotos, 
                        a.EXT_NUMBER AS ExtNumber, 
                        a.CONTENT, 
                        a.CHANNEL_ID AS ChannelId, 
                        b.CHANNELNAME AS ChannelName, 
                        a.USER_ID AS UserId, 
                        c.USERNAME AS UserName, 
                        a.STATUS, 
                        a.ADD_TIME AS AddTime, 
                        a.ADMIN_ID AS AdminId, 
                        a.SIGN_STATUS AS SignStatus 
                    FROM 
                        T_MOBILE_SIGN_REPORT a 
                        LEFT JOIN base_channel b ON a.CHANNEL_ID = b.CHANNELID 
                        LEFT JOIN base_user c ON a.USER_ID = c.USERID 
                    WHERE 
                        a.ID = :Id";

                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("Id", id);

                return this.QueryRecord<MobileSignReportModel>(sql, parameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"GetMobileSignReportById方法异常，ex={ex}");
                return null;
            }
        }

        /// <summary>
        /// 添加移动签名报备（泛型方法，实现接口）
        /// </summary>
        /// <typeparam name="T">模型类型</typeparam>
        /// <param name="model">添加的模型</param>
        /// <returns></returns>
        public bool AddMobileSignReport<T>(T model)
        {
            try
            {
                if (model is MobileSignReportModel)
                {
                    return AddMobileSignReport(model as MobileSignReportModel);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"AddMobileSignReport<T>方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 添加移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        public bool AddMobileSignReport(MobileSignReportModel model)
        {
            try
            {
                string sql = @"
                    INSERT INTO T_MOBILE_SIGN_REPORT (
                        REPORT_DATE, 
                        TYPE, 
                        ACCOUNT_NAME, 
                        SIGN_TYPE, 
                        SIGN, 
                        SIGN_COMPANY_NAME, 
                        UNIFIED_SOCIAL_CREDIT_CODE, 
                        LEGAL_PERSON, 
                        LEGAL_PERSON_NAME, 
                        LEGAL_PERSON_ID_CARD, 
                        LEGAL_PERSON_PHONE, 
                        DRAINAGE_LINK, 
                        DRAINAGE_NUMBER1, 
                        DRAINAGE_NUMBER2, 
                        SIGN_MATERIALS, 
                        ID_CARD_PHOTOS, 
                        EXT_NUMBER, 
                        CONTENT, 
                        CHANNEL_ID, 
                        USER_ID, 
                        STATUS, 
                        ADD_TIME, 
                        ADMIN_ID, 
                        SIGN_STATUS
                    ) VALUES (
                        :ReportDate, 
                        :Type, 
                        :AccountName, 
                        :SignType, 
                        :Sign, 
                        :SignCompanyName, 
                        :UnifiedSocialCreditCode, 
                        :LegalPerson, 
                        :LegalPersonName, 
                        :LegalPersonIdCard, 
                        :LegalPersonPhone, 
                        :DrainageLink, 
                        :DrainageNumber1, 
                        :DrainageNumber2, 
                        :SignMaterials, 
                        :IdCardPhotos, 
                        :ExtNumber, 
                        :Content, 
                        :ChannelId, 
                        :UserId, 
                        :Status, 
                        SYSDATE, 
                        :AdminId, 
                        :SignStatus
                    )";

                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("ReportDate", model.ReportDate);
                parameters.Add("Type", model.Type);
                parameters.Add("AccountName", model.AccountName);
                parameters.Add("SignType", model.SignType);
                parameters.Add("Sign", model.Sign);
                parameters.Add("SignCompanyName", model.SignCompanyName);
                parameters.Add("UnifiedSocialCreditCode", model.UnifiedSocialCreditCode);
                parameters.Add("LegalPerson", model.LegalPerson);
                parameters.Add("LegalPersonName", model.LegalPersonName);
                parameters.Add("LegalPersonIdCard", model.LegalPersonIdCard);
                parameters.Add("LegalPersonPhone", model.LegalPersonPhone);
                parameters.Add("DrainageLink", model.DrainageLink);
                parameters.Add("DrainageNumber1", model.DrainageNumber1);
                parameters.Add("DrainageNumber2", model.DrainageNumber2);
                parameters.Add("SignMaterials", model.SignMaterials);
                parameters.Add("IdCardPhotos", model.IdCardPhotos);
                parameters.Add("ExtNumber", model.ExtNumber);
                parameters.Add("Content", model.Content);
                parameters.Add("ChannelId", model.ChannelId);
                parameters.Add("UserId", model.UserId);
                parameters.Add("Status", model.Status);
                parameters.Add("AdminId", model.AdminId);
                parameters.Add("SignStatus", model.SignStatus);

                return this.Execute(sql, parameters) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"AddMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 更新移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        public bool UpdateMobileSignReport(MobileSignReportModel model)
        {
            try
            {
                string sql = @"
                    UPDATE T_MOBILE_SIGN_REPORT SET 
                        REPORT_DATE = :ReportDate, 
                        TYPE = :Type, 
                        ACCOUNT_NAME = :AccountName, 
                        SIGN_TYPE = :SignType, 
                        SIGN = :Sign, 
                        SIGN_COMPANY_NAME = :SignCompanyName, 
                        UNIFIED_SOCIAL_CREDIT_CODE = :UnifiedSocialCreditCode, 
                        LEGAL_PERSON = :LegalPerson, 
                        LEGAL_PERSON_NAME = :LegalPersonName, 
                        LEGAL_PERSON_ID_CARD = :LegalPersonIdCard, 
                        LEGAL_PERSON_PHONE = :LegalPersonPhone, 
                        DRAINAGE_LINK = :DrainageLink, 
                        DRAINAGE_NUMBER1 = :DrainageNumber1, 
                        DRAINAGE_NUMBER2 = :DrainageNumber2, 
                        SIGN_MATERIALS = :SignMaterials, 
                        ID_CARD_PHOTOS = :IdCardPhotos, 
                        EXT_NUMBER = :ExtNumber, 
                        CONTENT = :Content, 
                        CHANNEL_ID = :ChannelId, 
                        USER_ID = :UserId, 
                        STATUS = :Status, 
                        ADMIN_ID = :AdminId, 
                        SIGN_STATUS = :SignStatus 
                    WHERE 
                        ID = :Id";

                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("Id", model.Id);
                parameters.Add("ReportDate", model.ReportDate);
                parameters.Add("Type", model.Type);
                parameters.Add("AccountName", model.AccountName);
                parameters.Add("SignType", model.SignType);
                parameters.Add("Sign", model.Sign);
                parameters.Add("SignCompanyName", model.SignCompanyName);
                parameters.Add("UnifiedSocialCreditCode", model.UnifiedSocialCreditCode);
                parameters.Add("LegalPerson", model.LegalPerson);
                parameters.Add("LegalPersonName", model.LegalPersonName);
                parameters.Add("LegalPersonIdCard", model.LegalPersonIdCard);
                parameters.Add("LegalPersonPhone", model.LegalPersonPhone);
                parameters.Add("DrainageLink", model.DrainageLink);
                parameters.Add("DrainageNumber1", model.DrainageNumber1);
                parameters.Add("DrainageNumber2", model.DrainageNumber2);
                parameters.Add("SignMaterials", model.SignMaterials);
                parameters.Add("IdCardPhotos", model.IdCardPhotos);
                parameters.Add("ExtNumber", model.ExtNumber);
                parameters.Add("Content", model.Content);
                parameters.Add("ChannelId", model.ChannelId);
                parameters.Add("UserId", model.UserId);
                parameters.Add("Status", model.Status);
                parameters.Add("AdminId", model.AdminId);
                parameters.Add("SignStatus", model.SignStatus);

                return this.Execute(sql, parameters) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"UpdateMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 删除移动签名报备
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public bool DeleteMobileSignReport(int id)
        {
            try
            {
                string sql = "DELETE FROM T_MOBILE_SIGN_REPORT WHERE ID = :Id";
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("Id", id);
                return this.Execute(sql, parameters) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"DeleteMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 批量删除移动签名报备
        /// </summary>
        /// <param name="model">批量删除模型</param>
        /// <returns></returns>
        public bool BatchDeleteMobileSignReport(MobileSignReportModel model)
        {
            try
            {
                if (model.MobileSignReportModels == null || model.MobileSignReportModels.Count == 0)
                {
                    return false;
                }

                StringBuilder ids = new StringBuilder();
                foreach (var item in model.MobileSignReportModels)
                {
                    ids.Append(item.Id).Append(",");
                }
                ids.Remove(ids.Length - 1, 1);

                string sql = $"DELETE FROM T_MOBILE_SIGN_REPORT WHERE ID IN ({ids})";
                return this.Execute(sql) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"BatchDeleteMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }

        /// <summary>
        /// 创建删除条件的参数集合
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>参数集合</returns>
        private DynamicParameters CreateDeleteParameters(int channelId, DateTime? startDate, DateTime? endDate)
        {
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("channelId", channelId);

            if (startDate.HasValue && endDate.HasValue)
            {
                parameters.Add("startDate", startDate.Value);
                parameters.Add("endDate", endDate.Value);
            }
            else if (startDate.HasValue)
            {
                parameters.Add("startDate", startDate.Value);
            }
            else if (endDate.HasValue)
            {
                parameters.Add("endDate", endDate.Value);
            }

            return parameters;
        }

        /// <summary>
        /// 查询符合删除条件的记录数量（用于调试）
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>符合条件的记录数</returns>
        public int CountRecordsForDelete(int channelId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                StringBuilder sql = new StringBuilder();
                sql.Append("SELECT COUNT(*) FROM T_MOBILE_SIGN_REPORT WHERE CHANNEL_ID = :channelId");

                // 添加日期条件
                if (startDate.HasValue && endDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE >= :startDate AND REPORT_DATE <= :endDate");
                }
                else if (startDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE >= :startDate");
                }
                else if (endDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE <= :endDate");
                }

                System.Diagnostics.Trace.TraceInformation(string.Format("查询删除条件记录数SQL：{0}", sql));
                System.Diagnostics.Trace.TraceInformation(string.Format("查询参数：channelId={0}, startDate={1}, endDate={2}", channelId, startDate, endDate));

                DynamicParameters parameters = CreateDeleteParameters(channelId, startDate, endDate);
                int count = (int)this.QueryRecord<decimal>(sql.ToString(), parameters);
                System.Diagnostics.Trace.TraceInformation(string.Format("符合删除条件的记录数：{0}", count));

                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(string.Format("CountRecordsForDelete方法异常，ex={0}", ex));
                return 0;
            }
        }

        /// <summary>
        /// 按条件批量删除移动签名报备
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>删除的记录数</returns>
        public int BatchDeleteByCondition(int channelId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                StringBuilder sql = new StringBuilder();
                sql.Append("DELETE FROM T_MOBILE_SIGN_REPORT WHERE CHANNEL_ID = :channelId");

                // 添加日期条件
                if (startDate.HasValue && endDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE >= :startDate AND REPORT_DATE <= :endDate");
                }
                else if (startDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE >= :startDate");
                }
                else if (endDate.HasValue)
                {
                    sql.Append(" AND REPORT_DATE <= :endDate");
                }

                System.Diagnostics.Trace.TraceInformation(string.Format("批量删除SQL：{0}", sql));
                System.Diagnostics.Trace.TraceInformation(string.Format("参数：channelId={0}, startDate={1}, endDate={2}", channelId, startDate, endDate));

                // 先再次确认要删除的记录数
                string countSql = sql.ToString().Replace("DELETE FROM", "SELECT COUNT(*) FROM");
                DynamicParameters queryParameters = CreateDeleteParameters(channelId, startDate, endDate);
                int preDeleteCount = (int)this.QueryRecord<decimal>(countSql, queryParameters);
                System.Diagnostics.Trace.TraceInformation(string.Format("删除前再次确认记录数：{0}", preDeleteCount));

                if (preDeleteCount == 0)
                {
                    System.Diagnostics.Trace.TraceWarning("没有找到符合删除条件的记录");
                    return 0;
                }

                // 执行删除
                DynamicParameters deleteParameters = CreateDeleteParameters(channelId, startDate, endDate);
                int deletedCount = this.Execute(sql.ToString(), param: deleteParameters);
                System.Diagnostics.Trace.TraceInformation(string.Format("批量删除完成，删除记录数：{0}", deletedCount));

                // 删除后再次查询确认
                DynamicParameters postQueryParameters = CreateDeleteParameters(channelId, startDate, endDate);
                int postDeleteCount = (int)this.QueryRecord<decimal>(countSql, postQueryParameters);
                System.Diagnostics.Trace.TraceInformation(string.Format("删除后剩余记录数：{0}", postDeleteCount));

                return deletedCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(string.Format("BatchDeleteByCondition方法异常，ex={0}", ex));
                return 0;
            }
        }

        /// <summary>
        /// 检查移动签名报备是否存在
        /// </summary>
        /// <param name="sign">签名</param>
        /// <param name="userId">用户ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="extNumber">扩展号</param>
        /// <param name="content">内容</param>
        /// <returns></returns>
        public bool CheckMobileSignReport(string sign, string userId, string channelId, string extNumber, string content)
        {
            try
            {
                System.Diagnostics.Trace.TraceInformation($"CheckMobileSignReport调用参数：");
                System.Diagnostics.Trace.TraceInformation($"  Sign: '{sign}'");
                System.Diagnostics.Trace.TraceInformation($"  UserId: '{userId}'");
                System.Diagnostics.Trace.TraceInformation($"  ChannelId: '{channelId}'");
                System.Diagnostics.Trace.TraceInformation($"  ExtNumber: '{extNumber ?? "NULL"}'");
                System.Diagnostics.Trace.TraceInformation($"  Content: '{content ?? "NULL"}' (长度: {content?.Length ?? 0})");

                string sql = "SELECT F_CHECK_MOBILE_SIGN_REPORT(:Sign, :UserId, :ChannelId, :ExtNumber, :Content) FROM DUAL";
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("Sign", sign);
                parameters.Add("UserId", userId);
                parameters.Add("ChannelId", channelId);
                parameters.Add("ExtNumber", extNumber);
                parameters.Add("Content", content);
                int count = this.QueryRecord<int>(sql, parameters);

                System.Diagnostics.Trace.TraceInformation($"CheckMobileSignReport结果：找到 {count} 条重复记录");
                return count > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"CheckMobileSignReport方法异常，ex={ex}");
                return false;
            }
        }
    }
} 