<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>文件缓存问题测试</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn-test {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 文件缓存问题测试页面</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="alert alert-info">
                <p>这个页面用于测试文件上传缓存问题。请按照以下步骤操作：</p>
                <ol>
                    <li>选择一个Excel文件</li>
                    <li>点击"测试上传1"按钮</li>
                    <li>不要重新选择文件，直接点击"测试上传2"按钮</li>
                    <li>观察两次上传的结果和日志</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>📁 文件选择</h3>
            <div class="form-group">
                <label for="test_file">选择Excel文件：</label>
                <input type="file" id="test_file" class="form-control" accept=".xls,.xlsx">
            </div>
            
            <div class="form-group">
                <label for="channel_select">选择通道：</label>
                <select id="channel_select" class="form-control">
                    <option value="1">通道1</option>
                    <option value="2">通道2</option>
                    <option value="3">通道3</option>
                </select>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试按钮</h3>
            <button id="btn_test1" class="btn btn-primary btn-test">测试上传1</button>
            <button id="btn_test2" class="btn btn-warning btn-test">测试上传2（不重新选择文件）</button>
            <button id="btn_test3" class="btn btn-success btn-test">测试上传3（不重新选择文件）</button>
            <button id="btn_clear" class="btn btn-danger btn-test">清除文件选择</button>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <div id="log_output" class="log-output">等待测试...\n</div>
            <button id="btn_clear_log" class="btn btn-default btn-sm">清除日志</button>
        </div>
    </div>

    <script>
        var testCount = 0;
        
        function addLog(message) {
            var timestamp = new Date().toLocaleTimeString();
            $('#log_output').append('[' + timestamp + '] ' + message + '\n');
            $('#log_output').scrollTop($('#log_output')[0].scrollHeight);
        }
        
        function testUpload(testName) {
            testCount++;
            var fileInput = document.getElementById('test_file');
            var channelId = $('#channel_select').val();
            
            addLog('=== ' + testName + ' 开始 ===');
            
            if (fileInput.files.length === 0) {
                addLog('错误：没有选择文件');
                return;
            }
            
            var file = fileInput.files[0];
            addLog('文件信息：');
            addLog('  名称：' + file.name);
            addLog('  大小：' + file.size + ' 字节');
            addLog('  类型：' + file.type);
            addLog('  最后修改：' + new Date(file.lastModified).toLocaleString());
            
            // 创建FormData
            var formData = new FormData();
            formData.append('file', file);
            formData.append('channelId', channelId);
            formData.append('testName', testName);
            formData.append('testCount', testCount);
            formData.append('timestamp', Date.now());
            
            addLog('开始上传到通道 ' + channelId + '...');
            
            $.ajax({
                url: '/MobileSignReport/ImportMobileSignReport',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                cache: false,
                success: function (data) {
                    addLog('上传结果：');
                    addLog('  代码：' + data.code);
                    addLog('  消息：' + data.msg);
                    if (data.code === 0) {
                        addLog('✅ 上传成功');
                    } else {
                        addLog('❌ 上传失败');
                    }
                },
                error: function (xhr, status, error) {
                    addLog('❌ 上传出错：' + status + ' - ' + error);
                    if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            addLog('  详细错误：' + (response.msg || response.Message));
                        } catch (e) {
                            addLog('  响应内容：' + xhr.responseText.substring(0, 200));
                        }
                    }
                }
            });
            
            addLog('=== ' + testName + ' 结束 ===\n');
        }
        
        $('#btn_test1').click(function() {
            testUpload('测试上传1');
        });
        
        $('#btn_test2').click(function() {
            testUpload('测试上传2');
        });
        
        $('#btn_test3').click(function() {
            testUpload('测试上传3');
        });
        
        $('#btn_clear').click(function() {
            var fileInput = document.getElementById('test_file');
            fileInput.value = '';
            addLog('🗑️ 文件选择已清除');
        });
        
        $('#btn_clear_log').click(function() {
            $('#log_output').text('日志已清除...\n');
        });
        
        // 监听文件选择变化
        $('#test_file').change(function() {
            if (this.files.length > 0) {
                addLog('📁 选择了新文件：' + this.files[0].name);
            }
        });
    </script>
</body>
</html>
