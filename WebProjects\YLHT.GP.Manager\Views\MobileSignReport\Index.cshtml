@{
    ViewBag.Title = "移动签名报备";
    Layout = null;
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>移动签名报备</title>

    <!-- 基础CSS库 -->
    <link href="~/Scripts/MobileSign/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/Scripts/MobileSign/css/font-awesome.min.css" rel="stylesheet">

    <!-- 插件CSS -->
    <link href="~/Scripts/MobileSign/css/bootstrap-table.min.css" rel="stylesheet">
    <link href="~/Scripts/MobileSign/css/bootstrap-datepicker.min.css" rel="stylesheet">

    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }

        .panel {
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .panel-title {
            font-weight: bold;
        }

        .btn {
            margin-right: 5px;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .fixed-table-container {
            min-height: 400px;
        }

        /* 图片画廊样式 */
        .gallery-image {
            transition: transform 0.2s ease-in-out;
            border-radius: 4px;
        }

        .gallery-image:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .thumbnail {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px;
            background-color: #fff;
            transition: box-shadow 0.2s ease-in-out;
        }

        .thumbnail:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        .modal-lg {
            max-width: 1200px;
        }

        .image-category-title {
            color: #337ab7;
            border-bottom: 2px solid #337ab7;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }

        .fixed-table-body {
            overflow-x: auto;
            overflow-y: auto;
        }

        /* 批量删除按钮样式 */
        #btn_batch_delete {
            background-color: #d9534f;
            border-color: #d43f3a;
            color: white;
        }

        #btn_batch_delete:hover {
            background-color: #c9302c;
            border-color: #ac2925;
        }

        #btn_batch_delete:disabled {
            background-color: #d9534f;
            border-color: #d43f3a;
            opacity: 0.6;
        }

        /* 批量删除按钮加载状态 */
        #btn_batch_delete:disabled .glyphicon-hourglass {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    @Html.AntiForgeryToken()
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">移动签名报备</h3>
                    </div>
                    <div class="panel-body">
                        <div id="toolbar" class="btn-group">
                            <button id="btn_add" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> 添加
                            </button>
                            <button id="btn_edit" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span> 修改
                            </button>
                            <button id="btn_delete" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> 删除
                            </button>
                            <button id="btn_batch_delete" type="button" class="btn btn-danger">
                                <span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 批量删除
                            </button>
                            <button id="btn_export" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-export" aria-hidden="true"></span> 导出
                            </button>
                            <button id="btn_import" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-import" aria-hidden="true"></span> 导入
                            </button>
                        </div>
                        <div id="searchbar" style="margin-top:10px;">
                            <form id="formSearch" class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_reportDate">日期</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control datepicker" id="txt_reportDate" placeholder="日期">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_type">类型</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_type" placeholder="类型">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_accountName">客户名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_accountName" placeholder="客户名称">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_signType">签名类型</label>
                                            <div class="col-sm-8">
                                                <select class="form-control" id="txt_signType">
                                                    <option value="">全部</option>
                                                    <option value="企业简称">企业简称</option>
                                                    <option value="简称">简称</option>
                                                    <option value="APP">APP</option>
                                                    <option value="商标">商标</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_sign">签名</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_sign" placeholder="签名">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_signCompanyName">签名企业名称</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_signCompanyName" placeholder="签名企业名称">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_unifiedSocialCreditCode">统一社会信用代码</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_unifiedSocialCreditCode" placeholder="统一社会信用代码">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_legalPersonName">法人/经办人姓名</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" id="txt_legalPersonName" placeholder="法人/经办人姓名">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label col-sm-4" for="txt_channelId">通道</label>
                                            <div class="col-sm-8">
                                                @Html.DropDownList("txt_channelId", ViewBag.Channels as List<SelectListItem>, new { @class = "form-control" })
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12 text-center">
                                        <button type="button" id="btn_query" class="btn btn-primary">查询</button>
                                        <button type="button" id="btn_reset" class="btn btn-default">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="table-responsive" style="margin-top:20px;">
                            <table id="tb_mobileSignReport"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 基础JS库 -->
    <script src="~/Scripts/MobileSign/js/bootstrap.min.js"></script>
    <script src="~/Scripts/MobileSign/js/jquery.min.js"></script>

    <!-- 插件JS -->
    <script src="~/Scripts/MobileSign/js/bootstrap-table.min.js"></script>
    <script src="~/Scripts/MobileSign/js/bootstrap-table-zh-CN.min.js"></script>
    <script src="~/Scripts/MobileSign/js/bootstrap-datepicker.min.js"></script>
    <script src="~/Scripts/MobileSign/js/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Scripts/MobileSign/js/moment.min.js"></script>
    <script src="~/Scripts/MobileSign/js/sweetalert.min.js"></script>
    <link href="~/CSS/select2.css" rel="stylesheet" />
    <script src="~/Scripts/select2.js"></script>
    <script>
        $("#txt_channelId").select2();
        $(function () {

            // 初始化日期选择器
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                language: 'zh-CN',
                autoclose: true,
                todayHighlight: true
            });

            // 初始化表格
            InitTable();

            // 按钮事件
            $("#btn_add").click(function () {
                window.location.href = "/MobileSignReport/AddMobileSignReport";
            });

            $("#btn_edit").click(function () {
                var selections = $("#tb_mobileSignReport").bootstrapTable('getSelections');
                console.log("修改按钮点击，已选择记录：", selections);
                if (selections.length != 1) {
                    alert("请选择一条记录进行修改");
                    return;
                }

                // 检查ID字段
                var selectedRow = selections[0];
                console.log("选中的行数据：", selectedRow);

                var id = selectedRow.Id || selectedRow.id || selectedRow.ID;
                console.log("获取的ID值：", id, "类型：", typeof id);

                if (!id) {
                    console.error("无法获取ID值，所有可能的ID字段：",
                        selectedRow.Id, selectedRow.id, selectedRow.ID);
                    alert("无法获取记录ID，请刷新页面后重试");
                    return;
                }

                var url = "/MobileSignReport/UpdateMobileSignReport/" + id;
                console.log("即将跳转到URL：", url);
                window.location.href = url;
            });

            $("#btn_delete").click(function () {
                var selections = $("#tb_mobileSignReport").bootstrapTable('getSelections');
                if (selections.length != 1) {
                    alert("请选择一条记录进行删除");
                    return;
                }

                if (confirm("确定删除所选记录吗?")) {
                    $.ajax({
                        url: "/MobileSignReport/DeleteMobileSignReport",
                        type: "post",
                        data: { id: selections[0].Id },
                        success: function (data) {
                            if (data.code == 0) {
                                alert("删除成功!");
                                $("#tb_mobileSignReport").bootstrapTable('refresh');
                            } else {
                                alert("删除失败! " + data.msg);
                            }
                        }
                    });
                }
            });

            // 批量删除按钮事件
            $("#btn_batch_delete").click(function () {
                var selections = $("#tb_mobileSignReport").bootstrapTable('getSelections');
                if (selections.length == 0) {
                    alert("请至少选择一条记录进行批量删除");
                    return;
                }

                var confirmMsg = "确定要删除选中的 " + selections.length + " 条记录吗？\n\n此操作不可恢复，请谨慎操作！";
                if (confirm(confirmMsg)) {
                    // 构建批量删除的数据
                    var deleteData = {
                        MobileSignReportModels: [],
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    };

                    for (var i = 0; i < selections.length; i++) {
                        deleteData.MobileSignReportModels.push({
                            Id: selections[i].Id
                        });
                    }

                    // 显示加载状态
                    $("#btn_batch_delete").prop('disabled', true).html('<span class="glyphicon glyphicon-hourglass"></span> 删除中...');

                    $.ajax({
                        url: "/MobileSignReport/BatchDeleteMobileSignReport",
                        type: "post",
                        data: deleteData,
                        success: function (data) {
                            $("#btn_batch_delete").prop('disabled', false).html('<span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 批量删除');

                            if (data.code == 0) {
                                alert("批量删除成功！共删除 " + selections.length + " 条记录");
                                $("#tb_mobileSignReport").bootstrapTable('refresh');
                            } else {
                                alert("批量删除失败! " + data.msg);
                            }
                        },
                        error: function (xhr, status, error) {
                            $("#btn_batch_delete").prop('disabled', false).html('<span class="glyphicon glyphicon-trash" aria-hidden="true"></span> 批量删除');
                            console.error('批量删除请求失败:', status, error);
                            alert('批量删除请求失败，请检查网络连接或联系管理员');
                        }
                    });
                }
            });

            $("#btn_export").click(function (e) {
                e.preventDefault(); // 阻止默认行为

                // 获取搜索参数
                var params = {
                    ReportDate: $("#txt_reportDate").val(),
                    Type: $("#txt_type").val(),
                    AccountName: $("#txt_accountName").val(),
                    SignType: $("#txt_signType").val(),
                    Sign: $("#txt_sign").val(),
                    SignCompanyName: $("#txt_signCompanyName").val(),
                    UnifiedSocialCreditCode: $("#txt_unifiedSocialCreditCode").val(),
                    LegalPersonName: $("#txt_legalPersonName").val(),
                    ChannelId: $("#txt_channelId").val()
                };

                // 输出调试信息
                console.log("导出参数:", params);

                // 创建表单并提交
                var $form = $("<form>")
                    .attr("method", "POST")
                    .attr("action", "/MobileSignReport/ExportMobileSignReport")
                    .attr("target", "_blank"); // 在新窗口中打开

                // 添加参数到表单
                $.each(params, function (key, value) {
                    if (value) {
                        $("<input>")
                            .attr("type", "hidden")
                            .attr("name", key)
                            .attr("value", value)
                            .appendTo($form);
                    }
                });

                // 添加表单到文档并提交
                $("body").append($form);
                console.log("表单准备提交:", $form);
                $form.submit();

                // 移除表单
                setTimeout(function () {
                    $form.remove();
                }, 1000);
            });

            $("#btn_import").click(function () {
                window.location.href = "/MobileSignReport/ImportMobileSignReport";
            });

            $("#btn_query").click(function () {
                console.log("点击查询按钮");
                // 先输出当前输入框的值
                console.log("当前输入框值:");
                console.log("日期:", $("#txt_reportDate").val());
                console.log("类型:", $("#txt_type").val());
                console.log("客户名称:", $("#txt_accountName").val());
                console.log("签名类型:", $("#txt_signType").val());
                console.log("签名:", $("#txt_sign").val());
                console.log("签名企业名称:", $("#txt_signCompanyName").val());
                console.log("统一社会信用代码:", $("#txt_unifiedSocialCreditCode").val());
                console.log("法人姓名:", $("#txt_legalPersonName").val());
                console.log("通道:", $("#txt_channelId").val());

                $("#tb_mobileSignReport").bootstrapTable('refresh');
            });

            $("#btn_reset").click(function () {
                $("#txt_reportDate").val("");
                $("#txt_type").val("");
                $("#txt_accountName").val("");
                $("#txt_signType").val("");
                $("#txt_sign").val("");
                $("#txt_signCompanyName").val("");
                $("#txt_unifiedSocialCreditCode").val("");
                $("#txt_legalPersonName").val("");
                $("#txt_channelId").val("");
                // 重置后刷新表格
                $("#tb_mobileSignReport").bootstrapTable('refresh');
            });
        });

        // 初始化表格
        function InitTable() {
            $('#tb_mobileSignReport').bootstrapTable('destroy'); // 先销毁可能存在的表格
            $('#tb_mobileSignReport').bootstrapTable({
                url: '/MobileSignReport/GetMobileSignReportList',
                method: 'get',
                toolbar: '#toolbar',
                striped: true,
                cache: false,
                pagination: true,
                sortable: true,
                sortOrder: "desc",
                queryParams: queryParams,
                sidePagination: "server",
                pageNumber: 1,
                pageSize: 10,
                pageList: [10, 25, 50, 100],
                search: false,
                strictSearch: true,
                showColumns: true,
                showRefresh: true,
                minimumCountColumns: 2,
                clickToSelect: true,
                singleSelect: false,
                maintainSelected: true,
                debug: true,
                uniqueId: "Id",
                responseHandler: function (res) {
                    console.log("获取的数据：", res);
                    if (res && res.rows && res.rows.length > 0) {
                        console.log("第一条数据ID：", res.rows[0].Id);
                    }
                    return res;
                },
                onLoadSuccess: function(data) {
                    console.log("表格数据加载成功，数据量：", data.rows ? data.rows.length : 0);
                    // 确保复选框事件正常工作
                    setTimeout(function() {
                        var checkboxes = $('#tb_mobileSignReport').find('input[type="checkbox"]');
                        console.log("复选框数量：", checkboxes.length);
                    }, 100);
                },
                onClickRow: function (row, $element, field) {
                    console.log("点击行：", row);
                    console.log("行ID：", row.Id);
                    // 如果点击的不是复选框列，则切换选中状态
                    if (field !== 'state') {
                        var index = $element.attr('data-index');
                        $('#tb_mobileSignReport').bootstrapTable('check', index);
                    }
                },
                columns: [{
                    checkbox: true,
                    align: 'center',
                    valign: 'middle'
                }, {
                    field: 'ReportDate',
                    title: '日期',
                    align: 'center',
                    valign: 'middle',
                    sortable: true,
                    formatter: function (value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        }
                        return '';
                    }
                }, {
                    field: 'Type',
                    title: '类型',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'AccountName',
                    title: '客户名称',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'SignType',
                    title: '签名类型',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'Sign',
                    title: '签名',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'SignCompanyName',
                    title: '签名企业名称',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'UnifiedSocialCreditCode',
                    title: '统一社会信用代码',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'LegalPerson',
                    title: '法人',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'LegalPersonName',
                    title: '法人/经办人姓名',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'LegalPersonIdCard',
                    title: '法人/经办人身份证号',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'LegalPersonPhone',
                    title: '法人/经办人手机号',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'DrainageLink',
                    title: '引流链接',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'DrainageNumber1',
                    title: '引流号码1',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'DrainageNumber2',
                    title: '引流号码2',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'SignMaterials',
                    title: '签名材料',
                    align: 'center',
                    valign: 'middle',
                    sortable: true,
                    formatter: signMaterialsFormatter
                }, {
                    field: 'IdCardPhotos',
                    title: '身份证照片',
                    align: 'center',
                    valign: 'middle',
                    sortable: true,
                    formatter: idCardPhotosFormatter
                }, {
                    field: 'ExtNumber',
                    title: '扩展号',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'Content',
                    title: '内容',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'ChannelName',
                    title: '通道',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'AdminName',
                    title: '管理员名称',
                    align: 'center',
                    valign: 'middle',
                    sortable: true
                }, {
                    field: 'Status',
                    title: '状态',
                    align: 'center',
                    valign: 'middle',
                    sortable: true,
                    formatter: function (value, row, index) {
                        if (value == 1) {
                            return '<span class="label label-success">正常</span>';
                        } else {
                            return '<span class="label label-danger">禁用</span>';
                        }
                    }
                }, {
                    field: 'AddTime',
                    title: '添加时间',
                    align: 'center',
                    valign: 'middle',
                    sortable: true,
                    formatter: function (value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD HH:mm:ss');
                        }
                        return '';
                    }
                }]
            });

            // 调试：检查表格初始化后的状态
            setTimeout(function() {
                console.log("表格初始化完成，检查复选框...");
                var checkboxes = $('#tb_mobileSignReport').find('input[type="checkbox"]');
                console.log("找到的复选框数量：", checkboxes.length);
                if (checkboxes.length === 0) {
                    console.warn("未找到复选框，可能是配置问题");
                }
            }, 2000);
        }

        // 签名材料格式化
        function signMaterialsFormatter(value, row, index) {
            if (!value) return '';

            // 如果是URL路径，可能是多张图片以分号分隔
            if (value.indexOf(';') > -1) {
                var urls = value.split(';');
                var html = '';
                for (var i = 0; i < urls.length; i++) {
                    var url = urls[i].trim();
                    if (url) {
                        html += '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'签名材料\')" style="margin-right:5px;"><img src="' + url + '" style="max-width:50px; max-height:30px;" alt="查看材料" /></a>';
                    }
                }
                return html;
            }

            // 单张图片URL
            if (value.startsWith('/') || value.startsWith('http://') || value.startsWith('https://')) {
                return '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'签名材料\')"><img src="' + value + '" style="max-width:50px; max-height:30px;" alt="查看材料" /></a>';
            }

            // 如果含有DISPIMG格式，提取ID并生成图片URL
            if (value.indexOf('DISPIMG') > -1) {
                try {
                    var matches = value.match(/DISPIMG\(\"([^\"]+)\"/);
                    if (matches && matches.length > 1) {
                        var imageId = matches[1];
                        var imageUrl = '/MobileSignReport/GetImage?id=' + encodeURIComponent(imageId);
                        return '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'签名材料\')"><img src="' + imageUrl + '" style="max-width:50px; max-height:30px;" alt="查看材料" /></a>';
                    }
                } catch (e) {
                    console.error("解析DISPIMG出错:", e);
                }
            }

            // 如果不是图片URL或DISPIMG格式，则直接显示文本
            return value;
        }

        // 身份证照片格式化
        function idCardPhotosFormatter(value, row, index) {
            if (!value) return '';

            // 如果是URL路径，可能是多张图片以分号分隔
            if (value.indexOf(';') > -1) {
                var urls = value.split(';');
                var html = '';
                for (var i = 0; i < urls.length; i++) {
                    var url = urls[i].trim();
                    if (url) {
                        html += '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'身份证照片\')" style="margin-right:5px;"><img src="' + url + '" style="max-width:50px; max-height:30px;" alt="查看照片" /></a>';
                    }
                }
                return html;
            }

            // 单张图片URL
            if (value.startsWith('/') || value.startsWith('http://') || value.startsWith('https://')) {
                return '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'身份证照片\')"><img src="' + value + '" style="max-width:50px; max-height:30px;" alt="查看照片" /></a>';
            }

            // 如果含有DISPIMG格式，提取ID并生成图片URL
            if (value.indexOf('DISPIMG') > -1) {
                try {
                    var matches = value.match(/DISPIMG\(\"([^\"]+)\"/);
                    if (matches && matches.length > 1) {
                        var imageId = matches[1];
                        var imageUrl = '/MobileSignReport/GetImage?id=' + encodeURIComponent(imageId);
                        return '<a href="javascript:void(0);" onclick="showAllImages(\'' + row.Id + '\', \'身份证照片\')"><img src="' + imageUrl + '" style="max-width:50px; max-height:30px;" alt="查看照片" /></a>';
                    }
                } catch (e) {
                    console.error("解析DISPIMG出错:", e);
                }
            }

            // 如果不是图片URL或DISPIMG格式，则直接显示文本
            return value;
        }

        // 显示所有相关图片
        function showAllImages(recordId, imageType) {
            console.log("显示所有图片:", recordId, imageType);

            // 获取当前行的数据
            var tableData = $('#tb_mobileSignReport').bootstrapTable('getData');
            var currentRow = null;

            for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].Id == recordId) {
                    currentRow = tableData[i];
                    break;
                }
            }

            if (!currentRow) {
                alert("未找到对应的记录数据");
                return;
            }

            var images = [];

            // 收集签名材料图片
            if (currentRow.SignMaterials) {
                var signImages = parseImages(currentRow.SignMaterials, "签名材料");
                images = images.concat(signImages);
            }

            // 收集身份证照片
            if (currentRow.IdCardPhotos) {
                var idCardImages = parseImages(currentRow.IdCardPhotos, "身份证照片");
                images = images.concat(idCardImages);
            }

            if (images.length === 0) {
                alert("没有找到相关图片");
                return;
            }

            // 创建图片展示模态框
            showImageGallery(images, imageType, recordId);
        }

        // 解析图片数据
        function parseImages(imageData, category) {
            var images = [];

            if (!imageData) return images;

            // 处理URL格式的图片（分号分隔）
            if (imageData.indexOf(';') > -1) {
                var urls = imageData.split(';');
                for (var i = 0; i < urls.length; i++) {
                    var url = urls[i].trim();
                    if (url) {
                        images.push({
                            url: url,
                            title: category + " " + (i + 1),
                            category: category
                        });
                    }
                }
            }
            // 处理单张URL图片
            else if (imageData.startsWith('/') || imageData.startsWith('http://') || imageData.startsWith('https://')) {
                images.push({
                    url: imageData,
                    title: category,
                    category: category
                });
            }
            // 处理DISPIMG格式
            else if (imageData.indexOf('DISPIMG') > -1) {
                try {
                    var matches = imageData.match(/DISPIMG\(\"([^\"]+)\"/);
                    if (matches && matches.length > 1) {
                        var imageId = matches[1];
                        var imageUrl = '/MobileSignReport/GetImage?id=' + encodeURIComponent(imageId);
                        images.push({
                            url: imageUrl,
                            title: category + " (ID: " + imageId + ")",
                            category: category
                        });
                    }
                } catch (e) {
                    console.error("解析DISPIMG出错:", e);
                }
            }

            return images;
        }

        // 显示图片画廊
        function showImageGallery(images, focusType, recordId) {
            var modalHtml = '<div class="modal fade" id="imageGalleryModal" tabindex="-1" role="dialog" aria-labelledby="imageGalleryModalLabel">' +
                '<div class="modal-dialog modal-lg" style="width: 90%; max-width: 1200px;" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>' +
                '<h4 class="modal-title" id="imageGalleryModalLabel">图片展示 - 记录ID: ' + recordId + '</h4>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="row">';

            // 按类别分组显示图片
            var categories = {};
            for (var i = 0; i < images.length; i++) {
                var img = images[i];
                if (!categories[img.category]) {
                    categories[img.category] = [];
                }
                categories[img.category].push(img);
            }

            // 生成每个类别的图片展示
            var categoryIndex = 0;
            for (var category in categories) {
                var categoryImages = categories[category];
                var isActive = (category === focusType) ? 'in active' : '';

                modalHtml += '<div class="col-md-12">' +
                    '<h5><strong>' + category + '</strong> (' + categoryImages.length + '张)</h5>' +
                    '<div class="row" style="margin-bottom: 20px;">';

                for (var j = 0; j < categoryImages.length; j++) {
                    var img = categoryImages[j];
                    modalHtml += '<div class="col-md-3 col-sm-4 col-xs-6" style="margin-bottom: 15px;">' +
                        '<div class="thumbnail" style="margin-bottom: 0;">' +
                        '<img src="' + img.url + '" class="img-responsive gallery-image" ' +
                        'style="height: 150px; width: 100%; object-fit: cover; cursor: pointer;" ' +
                        'alt="' + img.title + '" title="' + img.title + '" ' +
                        'onclick="showFullImage(\'' + img.url + '\', \'' + img.title + '\')" ' +
                        'onerror="this.onerror=null;this.src=\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=\';this.alt=\'图片加载失败\';">' +
                        '<div class="caption text-center">' +
                        '<p style="margin: 5px 0; font-size: 12px;">' + img.title + '</p>' +
                        '</div>' +
                        '</div>' +
                        '</div>';
                }

                modalHtml += '</div></div>';
                categoryIndex++;
            }

            modalHtml += '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除之前的模态框
            $('#imageGalleryModal').remove();

            // 添加新模态框并显示
            $('body').append(modalHtml);
            $('#imageGalleryModal').modal('show');
        }

        // 显示全尺寸图片
        function showFullImage(imageUrl, title) {
            var fullImageModalHtml = '<div class="modal fade" id="fullImageModal" tabindex="-1" role="dialog" aria-labelledby="fullImageModalLabel">' +
                '<div class="modal-dialog modal-lg" style="width: 95%; max-width: 1400px;" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>' +
                '<h4 class="modal-title" id="fullImageModalLabel">' + title + '</h4>' +
                '</div>' +
                '<div class="modal-body text-center" style="padding: 20px;">' +
                '<img src="' + imageUrl + '" style="max-width: 100%; max-height: 80vh; border: 1px solid #ddd;" ' +
                'onerror="this.onerror=null;this.src=\'\';this.alt=\'图片加载失败\';alert(\'图片加载失败\');">' +
                '</div>' +
                '<div class="modal-footer">' +
                '<a href="' + imageUrl + '" target="_blank" class="btn btn-primary">在新窗口中打开</a>' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除之前的全尺寸图片模态框
            $('#fullImageModal').remove();

            // 添加新模态框并显示
            $('body').append(fullImageModalHtml);
            $('#fullImageModal').modal('show');
        }

        // 显示图片加载错误
        function showImageError() {
            alert("无法加载图片，图片可能不存在或格式不支持。");
        }

        // 请求参数
        function queryParams(params) {
            // 获取各个输入框的值
            var reportDate = $("#txt_reportDate").val();
            var type = $("#txt_type").val();
            var accountName = $("#txt_accountName").val();
            var signType = $("#txt_signType").val();
            var sign = $("#txt_sign").val();
            var signCompanyName = $("#txt_signCompanyName").val();
            var unifiedSocialCreditCode = $("#txt_unifiedSocialCreditCode").val();
            var legalPersonName = $("#txt_legalPersonName").val();
            var channelId = $("#txt_channelId").val();

            // 详细调试信息
            console.log("=== 查询参数详细信息 ===");
            console.log("ReportDate:", reportDate, "长度:", reportDate.length);
            console.log("Type:", type, "长度:", type.length);
            console.log("AccountName:", accountName, "长度:", accountName.length);
            console.log("SignType:", signType, "长度:", signType.length);
            console.log("Sign:", sign, "长度:", sign.length);
            console.log("SignCompanyName:", signCompanyName, "长度:", signCompanyName.length);
            console.log("UnifiedSocialCreditCode:", unifiedSocialCreditCode, "长度:", unifiedSocialCreditCode.length);
            console.log("LegalPersonName:", legalPersonName, "长度:", legalPersonName.length);
            console.log("ChannelId:", channelId, "长度:", channelId.length);

            var temp = {
                rows: params.limit,
                page: Math.floor(params.offset / params.limit) + 1,
                sort: params.sort,
                order: params.order,
                ReportDate: reportDate,
                Type: type,
                AccountName: accountName,
                SignType: signType,
                Sign: sign,
                SignCompanyName: signCompanyName,
                UnifiedSocialCreditCode: unifiedSocialCreditCode,
                LegalPersonName: legalPersonName,
                ChannelId: channelId
            };
            console.log("最终查询参数：", temp);
            console.log("========================");
            return temp;
        }
    </script>
</body>
</html> 