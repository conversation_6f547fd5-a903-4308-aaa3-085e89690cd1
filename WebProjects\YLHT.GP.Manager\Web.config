﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="YLHT.GP.Manager.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
  </configSections>
  <connectionStrings>
    <!--共享数据库-->
    <add connectionString="Data Source=orcl;User Id=YLHT2;Password=********,Data Source=127.0.0.1/orcl;User ID=YLHT2;Password=********;Max Pool Size=512;Pooling=true" name="connectionString" />
    <!--<add connectionString="Data Source=orcl1;User Id=YLHT;Password=********,Data Source=*************/orcl;User ID=YLHT;Password=********;Max Pool Size=512;Pooling=true" name="connectionString" />-->
    <!--<add connectionString="Data Source=orcl58;User Id=YLHT;Password=********,Data Source=120.221.20.58/orcl;User ID=YLHT;Password=********;Max Pool Size=512;Pooling=true" name="connectionString" />-->
	  <!--<add connectionString="Data Source=orcl59;User Id=YLHT;Password=********,Data Source=*************/orcl;User ID=YLHT;Password=********;Max Pool Size=512;Pooling=true" name="connectionString" />-->
    <!--本机数据库-->
    <!--<add connectionString="Data Source=ORCL;User Id=YLHT;Password=********" name="connectionString" />-->
  </connectionStrings>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <!--<add key="BillingServiceUrl" value="http://*************:50020/BillingService.asmx" />
    <add key="DataServiceUrl" value="http://*************:50020//DataService.asmx" />
    <add key="InternalServiceUrl" value="http://*************:50010/InternalService.asmx" />-->
    <add key="BillingServiceUrl" value="http://127.0.0.1:5002/BillingService.asmx" />
    <add key="DataServiceUrl" value="http://127.0.0.1:5002//DataService.asmx" />
    <add key="InternalServiceUrl" value="http://127.0.0.1:5001/InternalService.asmx" />
    <!--<add key="ValidateUrl" value="http://localhost:23758/api/Validate" />
    <add key="LogOutUrl" value="http://localhost:23758/Account/Logout" />-->
    <add key="DataSourceUrl" value="YLHT.GP.DataSource.YLHTSmsWebDataSource,YLHT.GP.DataSource,Version=*******,Culture=neutral,PublicKeyToken=null" />
    <add key="ClientUrl" value="http://localhost:8080" />
    <add key="FileUrl" value="D:\MSGDETAILEXPORTDIR" />
    <add key="FileDir" value="MSGDETAILEXPORTDIR" />
    <add key="OneSelfIp" value="*************" />
    <!--<add key="RedisPort" value="*************:6379,abortConnect=false" />-->
    <add key="RedisPort" value="127.0.0.1:6379,abortConnect=false" />
    <add key="SignalRServer" value="http://localhost" />
    <add key="SignalrDirectory" value="/signalr/hubs" />
    <add key="Beforethesuffix" value="$$:**" />
    <add key="ReportDataQueue" value="0" />
    <add key="notuserid" value="" />
    <add key="notchannelid" value="" />
    <add key="IsMsgAuditNoCount" value="1" />
    <add key="utf8userids" value="1847" />
    <add key="RedisConnection" value="127.0.0.1:6379" />
	  <add key="BlacklistApiUrl" value="http://localhost:8087" />
    <add key="filepath" value="D:\YLHT.GP\WebProjects\YLHT.GP.Manager\signalr\prot.txt" />
  </appSettings>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <clear />
        <add name="FileTraceListener" type="YLHT.GP.Common.FileTraceListener,YLHT.GP.Common, Version=*******, Culture=neutral, PublicKeyToken=null" traceOutputOptions="None" initializeData="D:\YLHTTraceListener\YLHT.GP.Manager" />
      </listeners>
    </trace>
  </system.diagnostics>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>
  -->
  <system.web>
    <globalization requestEncoding="utf-8" responseEncoding="utf-8" fileEncoding="utf-8" />
    <compilation debug="true" targetFramework="4.5" />
	  <httpRuntime maxRequestLength="102400" executionTimeout="6000" targetFramework="4.5" />
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
    </httpModules>
    <!--验证forms-->
    <authentication mode="Forms">
      <forms name="services" loginUrl="~/Account/Login" defaultUrl="~/Hone/Index" timeout="2880" />
      <!--<forms loginUrl="http://localhost:23758/Account/SSOLogin" timeout="2880" />-->
    </authentication>
    <sessionState mode="StateServer" timeout="60" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.6.0" newVersion="5.2.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.6.0" newVersion="5.2.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-5.2.4.0" newVersion="5.2.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.6.0" newVersion="5.2.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.Agent.Intercept" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <publisherPolicy apply="no" />
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-**********" newVersion="**********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="ICSharpCode.SharpZipLib" publicKeyToken="1b03e6acf1164f73" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules>
      <remove name="ApplicationInsightsWebTracking" />
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <!-- 配置文件上传大小限制 -->
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="104857600" />
      </requestFiltering>
    </security>
    <staticContent>
      <mimeMap fileExtension=".7z" mimeType="application/octet-stream" />
    </staticContent>
  </system.webServer>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <system.serviceModel>
    <bindings />
    <client />
  </system.serviceModel>
  <applicationSettings>
    <YLHT.GP.Manager.Properties.Settings>
      <setting name="YLHT_GP_Manager_DataService_DataService" serializeAs="String">
        <value>http://127.0.0.1:5002/DataService.asmx</value>
      </setting>
      <setting name="YLHT_GP_Manager_InternalService_InternalService" serializeAs="String">
        <value>http://127.0.0.1:5001/InternalService.asmx</value>
      </setting>
      <setting name="YLHT_GP_Manager_BillingService_BillingService" serializeAs="String">
        <value>http://127.0.0.1:5002/BillingService.asmx</value>
      </setting>
    </YLHT.GP.Manager.Properties.Settings>
  </applicationSettings>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources>
        <dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " />
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>
</configuration>