# 短信平台页面功能详细清单

> **文档说明**: 详细列出短信平台每个页面的功能、解释和操作说明
> **适用范围**: 客户端 + 管理端所有页面
> **更新时间**: 2025-08-01

## 📋 目录导航
- [客户端页面功能](#客户端页面功能)
- [管理端页面功能](#管理端页面功能)
- [页面功能统计](#页面功能统计)

---

## 🖥️ 客户端页面功能

### 🏠 首页与账户模块

#### 1. 客户端首页 (`/Home/Index`)
**功能说明**: 客户登录后的主页面，提供系统概览和快捷操作入口
**页面功能**:
- ✅ 显示账户余额和基本信息
- ✅ 展示今日发送统计数据
- ✅ 提供快捷发送入口
- ✅ 显示最新系统通知
- ✅ 账户状态和权限提示

**主要操作**:
- 查看账户余额和到期时间
- 快速跳转到发送短信页面
- 查看发送成功率和费用统计
- 阅读系统通知和公告

#### 2. 用户登录页面 (`/Account/Login`)
**功能说明**: 用户身份验证和登录页面
**页面功能**:
- ✅ 用户名密码登录
- ✅ 验证码验证
- ✅ 记住登录状态
- ✅ 登录失败提示
- ✅ 密码找回链接

**主要操作**:
- 输入用户名和密码
- 输入图形验证码
- 选择记住登录状态
- 点击登录按钮进入系统

#### 3. 修改密码页面 (`/Home/UpPaswd`)
**功能说明**: 用户修改登录密码的安全页面
**页面功能**:
- ✅ 原密码验证
- ✅ 新密码设置
- ✅ 密码强度检测
- ✅ 确认密码验证
- ✅ 密码修改成功提示

**主要操作**:
- 输入当前密码进行验证
- 设置新密码（需符合安全要求）
- 确认新密码
- 提交密码修改请求

### 📱 短信业务模块

#### 4. 发送短信页面 (`/SmsOperation/SendSms`)
**功能说明**: 普通短信发送的主要操作页面
**页面功能**:
- ✅ **手机号码管理**: 支持单个/批量号码输入，最多10万条
- ✅ **号码过滤功能**: 自动过滤重复、错误、黑名单号码
- ✅ **短信内容编辑**: 支持2000字符内容，实时字数统计
- ✅ **敏感词检测**: 自动检测和提示敏感词
- ✅ **签名选择**: 选择已审核通过的短信签名
- ✅ **业务类型选择**: 支持普通短信(SMS)和闪信(FMS)
- ✅ **定时发送**: 支持立即发送和定时发送
- ✅ **扩展号码**: 支持设置扩展号码
- ✅ **通讯录导入**: 支持从通讯录选择号码
- ✅ **文件导入**: 支持Excel/TXT/CSV文件导入号码
- ✅ **费用预估**: 实时计算发送费用
- ✅ **发送预览**: 预览最终短信效果

**具体操作功能**:
- `SendSms()` - 显示发送页面
- `SendSms(SendSmsModel)` - 执行短信发送
- `WordVerification()` - 敏感词验证
- `TelNumFilter()` - 号码过滤
- `NdcTelNumFilter()` - 高级号码过滤
- `GetClientBook()` - 获取通讯录分组
- `GetClientBookMsisdn()` - 获取通讯录号码

#### 5. 个性化短信页面 (`/SmsOperation/IndivSms`)
**功能说明**: 支持个性化内容的批量短信发送
**页面功能**:
- ✅ **文件上传**: 支持Excel/TXT/CSV格式文件上传
- ✅ **模板变量**: 支持{1},{2}等变量替换
- ✅ **个性化内容**: 每条短信内容可不同
- ✅ **数据预览**: 上传后预览个性化效果
- ✅ **错误检测**: 自动检测号码和内容错误
- ✅ **分批发送**: 大量数据自动分批处理
- ✅ **发送进度**: 实时显示发送进度
- ✅ **失败重试**: 失败号码可重新发送
- ✅ **数据压缩**: 大文件自动压缩传输
- ✅ **格式验证**: 验证文件格式和数据完整性

**具体操作功能**:
- `IndivSms()` - 显示个性化短信页面
- `SendIndivSms()` - 发送个性化短信
- `ImportIndivSms()` - 导入个性化短信数据
- `SendIndivSmsOld()` - 旧版个性化发送

#### 6. 短信状态查询页面 (`/SmsOperation/GetSmsStatusQuery`)
**功能说明**: 查询已发送短信的状态和回执信息
**页面功能**:
- ✅ **任务查询**: 按任务维度查询发送记录
- ✅ **明细查询**: 按号码维度查询详细状态
- ✅ **时间筛选**: 支持按发送时间范围查询
- ✅ **状态筛选**: 按发送状态筛选（成功/失败/未知）
- ✅ **号码查询**: 支持单个或批量号码查询
- ✅ **任务ID查询**: 支持按任务ID精确查询
- ✅ **分页显示**: 大量数据分页展示
- ✅ **实时刷新**: 支持实时刷新状态
- ✅ **导出功能**: 支持导出Excel格式报表
- ✅ **状态说明**: 详细的状态码说明
- ✅ **回执查看**: 查看运营商回执信息
- ✅ **统计汇总**: 显示查询结果统计信息

**具体操作功能**:
- `GetSmsStatusQuery()` - 显示状态查询页面
- `QuerySmsRowPageCount()` - 查询任务分页数据
- `QuerySmsDetailRowsPage()` - 查询明细分页数据
- `QuerySmsDetailReportList()` - 查询状态报告列表
- `GetMtMoMsg()` - 获取上下行消息
- `ExportClientMsgDetialFile()` - 导出明细文件
- `ExportClientMsgTaskFile()` - 导出任务文件
- `ExportClientMsgMoFile()` - 导出上行消息文件

### 🖼️ 彩信业务模块

#### 7. 彩信发送页面 (`/MmsOperation/SendMms`)
**功能说明**: 制作和发送多媒体彩信
**页面功能**:
- ✅ **可视化制作**: 拖拽式彩信制作工具
- ✅ **多媒体支持**: 支持图片、文字、音频元素
- ✅ **图片处理**: 自动压缩、格式转换、尺寸调整
- ✅ **文字编辑**: 支持多种字体、颜色、大小设置
- ✅ **布局设计**: 灵活的页面布局和元素定位
- ✅ **预览功能**: 实时预览彩信效果
- ✅ **兼容性检测**: 检测不同手机型号兼容性
- ✅ **文件大小控制**: 自动控制彩信文件大小
- ✅ **号码管理**: 支持单个/批量号码输入
- ✅ **定时发送**: 支持立即和定时发送
- ✅ **发送确认**: 发送前最终确认和预览

**具体操作功能**:
- `SendMms()` - 显示彩信发送页面
- `SendMms(SendMmsModel)` - 执行彩信发送

#### 8. 动态彩信页面 (`/MmsOperation/SendDynamicMms`)
**功能说明**: 制作包含动态内容的彩信
**页面功能**:
- ✅ **多帧制作**: 支持多个图片帧组成动画
- ✅ **帧管理**: 添加、删除、排序动画帧
- ✅ **时间控制**: 设置每帧显示时间
- ✅ **动画预览**: 实时预览动态效果
- ✅ **文件优化**: 自动优化文件大小和质量
- ✅ **格式支持**: 支持多种图片格式
- ✅ **兼容性测试**: 测试不同设备兼容性
- ✅ **批量发送**: 支持批量号码发送
- ✅ **进度跟踪**: 显示制作和发送进度

**具体操作功能**:
- `SendDynamicMms()` - 显示动态彩信页面
- `SendDynamicMms(string, string)` - 发送动态彩信
- `_FrameView()` - 帧内容视图
- `UpLoadMmsFile()` - 上传彩信文件
- `DeleteMmsFile()` - 删除彩信文件
- `DeleteMmsFileDirectory()` - 删除帧目录
- `GetMmsFile()` - 获取彩信文件

#### 9. 彩信模板管理页面 (`/MmsOperation/MmsTemplate`)
**功能说明**: 管理彩信模板，提高制作效率
**页面功能**:
- ✅ **模板创建**: 创建可重复使用的彩信模板
- ✅ **模板编辑**: 修改现有模板内容和样式
- ✅ **模板分类**: 按业务类型分类管理模板
- ✅ **模板预览**: 预览模板在不同设备上的效果
- ✅ **模板复制**: 复制现有模板快速创建新模板
- ✅ **审核状态**: 查看模板审核状态和结果
- ✅ **使用统计**: 统计模板使用次数和效果
- ✅ **模板搜索**: 按名称、分类搜索模板
- ✅ **模板导入导出**: 支持模板的导入导出功能

**具体操作功能**:
- `MmsTemplate()` - 显示模板管理页面
- `TemplateMms()` - 创建/编辑模板
- `MmsTemplateView()` - 模板查询和查看

#### 10. 保存动态彩信页面 (`/MmsOperation/SaveDynamicMms`)
**功能说明**: 将制作的动态彩信保存为模板
**页面功能**:
- ✅ **模板保存**: 将动态彩信保存为可重用模板
- ✅ **模板信息**: 设置模板名称、描述、分类
- ✅ **审核提交**: 提交模板进行审核
- ✅ **模板预览**: 保存前预览模板效果
- ✅ **版本管理**: 支持模板版本控制

**具体操作功能**:
- `SaveDynamicMms()` - 显示保存页面
- `SaveTemplateDynamicMms()` - 保存动态彩信模板
- `SaveTemplateDynamicMmsOld()` - 旧版保存功能

#### 11. 发送模板彩信页面 (`/MmsOperation/SendMmsTemplate`)
**功能说明**: 使用已审核的模板发送彩信
**页面功能**:
- ✅ **模板选择**: 选择已审核通过的彩信模板
- ✅ **变量替换**: 支持模板变量个性化替换
- ✅ **批量发送**: 支持批量号码发送
- ✅ **发送预览**: 发送前预览最终效果
- ✅ **定时发送**: 支持定时发送功能

**具体操作功能**:
- `SendMmsTemplate()` - 显示模板发送页面
- `MmsTemplateSend()` - 发送模板彩信
- `MmsTemplateSendVars()` - 带变量的模板发送

#### 12. 彩信详情查看页面
**功能说明**: 查看已发送彩信的详细信息
**页面功能**:
- ✅ **内容查看**: 查看彩信完整内容
- ✅ **发送状态**: 查看发送状态和回执
- ✅ **下载功能**: 下载彩信文件
- ✅ **统计信息**: 查看发送统计数据

**具体操作功能**:
- `MmsFileDetail()` - 彩信文件详情
- `MmsTemplateFileDetail()` - 模板文件详情
- `DownloadMms()` - 下载彩信
- `DownloadMmsTemplate()` - 下载模板彩信
- `QueryMmsRowPageCount()` - 查询彩信分页数据
- `QueryMmsDetailRowsPage()` - 查询彩信明细数据

### 🔊 语音短信模块

#### 13. 语音短信发送页面 (`/Voice/SendVoice`)
**功能说明**: 发送语音短信给指定用户
**页面功能**:
- ✅ **语音模板选择**: 选择已审核通过的语音模板
- ✅ **语音预听**: 发送前预听语音内容
- ✅ **号码管理**: 支持单个/批量号码输入
- ✅ **触发短信**: 设置语音播放完成后的触发短信
- ✅ **发送时间**: 支持立即发送和定时发送
- ✅ **语音质量**: 选择语音播放质量
- ✅ **发送统计**: 查看语音发送统计数据
- ✅ **状态跟踪**: 实时跟踪语音发送状态

**具体操作功能**:
- `SendVoice()` - 显示语音发送页面
- `SendSmsVoice()` - 发送语音短信
- `QuerySmsRowPageCount()` - 查询语音任务分页数据
- `QueryVoiceDetailRowsPage()` - 查询语音明细数据
- `ExportClientDetialFile()` - 导出语音发送明细

#### 14. 语音模板管理页面 (`/Voice/VoiceTemplate`)
**功能说明**: 管理语音文件和模板
**页面功能**:
- ✅ **模板列表**: 显示所有语音模板列表
- ✅ **模板搜索**: 按名称、状态搜索模板
- ✅ **分页显示**: 大量模板分页展示
- ✅ **状态筛选**: 按审核状态筛选模板
- ✅ **模板预听**: 预听语音模板内容
- ✅ **使用统计**: 查看模板使用次数

**具体操作功能**:
- `VoiceTemplate()` - 显示模板列表页面

#### 15. 语音模板添加页面 (`/Voice/VoiceTemplateAdd`)
**功能说明**: 创建新的语音模板
**页面功能**:
- ✅ **文件上传**: 支持WAV/MP3等音频格式上传
- ✅ **格式转换**: 自动转换为标准格式
- ✅ **质量调整**: 调整音频质量和文件大小
- ✅ **模板信息**: 设置模板名称、描述等信息
- ✅ **预览播放**: 上传后预览播放效果
- ✅ **审核提交**: 提交模板进行审核
- ✅ **FFmpeg处理**: 使用FFmpeg进行音频处理

**具体操作功能**:
- `VoiceTemplateAdd()` - 显示添加页面
- `VoiceTemplateAdd(VoiceTemplate)` - 提交语音模板
- `VoiceTemplateAdd1()` - 备用添加方法
- `VoiceTemplateDelete()` - 删除语音模板

#### 16. 语音短信模板页面 (`/Voice/VoiceSmsTemplate`)
**功能说明**: 管理语音短信关联的短信模板
**页面功能**:
- ✅ **短信模板**: 管理语音播放后的触发短信模板
- ✅ **模板关联**: 将短信模板与语音模板关联
- ✅ **内容编辑**: 编辑触发短信的内容
- ✅ **模板审核**: 提交短信模板审核

**具体操作功能**:
- `VoiceSmsTemplate()` - 显示短信模板页面
- `VoiceSmsTemplateAdd()` - 添加短信模板
- `SendSmsTemplate()` - 发送短信模板

### 📊 统计查询模块

#### 12. 发送统计页面 (`/SendStatistics/Index`)
**功能说明**: 查看各维度的发送数据统计
**页面功能**:
- ✅ 按时间维度统计
- ✅ 按业务类型统计
- ✅ 成功率分析图表
- ✅ 费用统计报表
- ✅ 数据导出功能
- ✅ 自定义统计条件

**主要操作**:
- 选择统计时间范围
- 筛选业务类型
- 查看图表和报表
- 分析发送趋势
- 导出统计数据

#### 13. 消费明细页面 (`/ConsumptionDetail/Index`)
**功能说明**: 查看账户消费明细和费用记录
**页面功能**:
- ✅ 消费记录查询
- ✅ 费用明细展示
- ✅ 余额变动记录
- ✅ 充值记录查看
- ✅ 账单导出功能
- ✅ 费用统计分析

**主要操作**:
- 查询指定时间的消费记录
- 查看详细费用明细
- 跟踪余额变动情况
- 导出账单和发票
- 分析消费趋势

### 📝 内容管理模块

#### 14. 内容报备页面 (`/ContentPreparation/Index`)
**功能说明**: 提交短信内容模板进行报备审核
**页面功能**:
- ✅ 模板内容编辑
- ✅ 模板分类选择
- ✅ 使用场景说明
- ✅ 报备材料上传
- ✅ 审核状态跟踪
- ✅ 历史报备记录

**主要操作**:
- 编写短信模板内容
- 选择模板分类
- 上传相关证明材料
- 提交报备申请
- 跟踪审核进度
- 查看审核结果

### 👤 客户信息模块

#### 15. 客户信息页面 (`/Customer/Index`)
**功能说明**: 查看和管理客户基本信息
**页面功能**:
- ✅ 基本信息展示
- ✅ 联系方式管理
- ✅ 账户状态查看
- ✅ 权限信息显示
- ✅ 信息修改申请
- ✅ 认证状态查看

**主要操作**:
- 查看客户基本信息
- 更新联系方式
- 查看账户权限
- 申请信息修改
- 查看认证状态

### 📁 文件管理模块

#### 16. 文件列表页面 (`/FileList/Index`)
**功能说明**: 管理上传的文件和素材
**页面功能**:
- ✅ 文件列表展示
- ✅ 文件上传功能
- ✅ 文件预览功能
- ✅ 文件下载功能
- ✅ 文件删除管理
- ✅ 存储空间统计

**主要操作**:
- 上传各类文件
- 预览文件内容
- 下载已上传文件
- 删除不需要的文件
- 查看存储使用情况

### 📢 通知模块

#### 17. 历史通知页面 (`/HistoryNotiecs/Index`)
**功能说明**: 查看系统历史通知和公告
**页面功能**:
- ✅ 通知列表展示
- ✅ 通知内容查看
- ✅ 通知分类筛选
- ✅ 已读状态管理
- ✅ 重要通知标记
- ✅ 通知搜索功能

**主要操作**:
- 浏览历史通知列表
- 查看通知详细内容
- 标记通知已读状态
- 搜索特定通知
- 筛选通知类型

### 📖 帮助模块

#### 18. 客户手册页面 (`/ClientBook/Index`)
**功能说明**: 查看平台使用手册和帮助文档
**页面功能**:
- ✅ 使用手册展示
- ✅ API文档查看
- ✅ 常见问题解答
- ✅ 操作视频教程
- ✅ 联系方式信息
- ✅ 文档搜索功能

**主要操作**:
- 阅读使用手册
- 查看API接口文档
- 搜索相关问题
- 观看操作教程
- 获取技术支持联系方式

---

## 🛠️ 管理端页面功能

### 🏠 管理首页模块

#### 1. 管理首页 (`/Home/Index`)
**功能说明**: 管理员登录后的主控制台页面
**页面功能**:
- ✅ **系统监控**: 实时监控系统运行状态和服务健康度
- ✅ **数据概览**: 显示关键业务数据和KPI指标
- ✅ **告警中心**: 显示系统告警、异常和待处理事项
- ✅ **快捷操作**: 提供常用管理功能的快捷入口
- ✅ **资源监控**: 显示服务器CPU、内存、磁盘使用情况
- ✅ **业务动态**: 显示最新业务变化、用户活动和系统事件
- ✅ **通知中心**: 显示系统通知、公告和重要消息
- ✅ **权限控制**: 根据管理员权限显示相应功能模块

### 👥 客户管理模块

#### 2. 客户管理首页 (`/Client/Index`)
**功能说明**: 客户账号管理的主入口页面
**页面功能**:
- ✅ **客户列表**: 显示所有客户账号列表
- ✅ **搜索筛选**: 按用户名、状态、类型等条件搜索
- ✅ **批量操作**: 支持批量启用、禁用、删除客户
- ✅ **快捷操作**: 快速充值、修改、查看客户信息
- ✅ **状态监控**: 显示客户在线状态和最后登录时间
- ✅ **分页显示**: 大量客户数据分页展示

#### 3. 语音客户管理 (`/Client/VoiceCustomerIndex`)
**功能说明**: 专门管理语音短信客户
**页面功能**:
- ✅ **语音客户列表**: 显示所有语音短信客户
- ✅ **权限管理**: 管理语音发送权限和限制
- ✅ **费用设置**: 设置语音短信费用和计费方式
- ✅ **使用统计**: 查看语音短信使用统计
- ✅ **客户搜索**: 按多种条件搜索语音客户

**具体操作功能**:
- `VoiceCustomerIndex()` - 显示语音客户列表
- `AddVoiceCustomer()` - 添加语音客户页面
- `AddVoiceSmsCustomer()` - 创建语音客户账号
- `UpdateVoiceCustomer()` - 修改语音客户信息

#### 4. 直接客户管理 (`/Client/DirectCustomerIndex`)
**功能说明**: 管理直接签约的客户账号
**页面功能**:
- ✅ **直客列表**: 显示所有直接客户
- ✅ **合同管理**: 管理客户合同信息
- ✅ **价格设置**: 设置客户专属价格
- ✅ **服务配置**: 配置客户专属服务
- ✅ **结算管理**: 管理客户结算方式

#### 5. 间接客户管理 (`/Client/IndirectCustomerIndex`)
**功能说明**: 管理代理商下级客户
**页面功能**:
- ✅ **下级客户**: 显示代理商的下级客户
- ✅ **层级关系**: 显示客户层级关系
- ✅ **分润设置**: 设置代理商分润比例
- ✅ **权限继承**: 管理权限继承关系

#### 6. 客户充值管理 (`/Client/Recharge`)
**功能说明**: 管理客户账户充值和余额
**页面功能**:
- ✅ **充值操作**: 为客户账户充值
- ✅ **充值记录**: 查看历史充值记录
- ✅ **余额调整**: 调整客户账户余额
- ✅ **充值审核**: 审核充值申请
- ✅ **发票管理**: 管理充值发票

#### 2. 数据统计页面 (`/Home/Start`)
**功能说明**: 系统整体数据统计和分析页面
**页面功能**:
- ✅ 多维度数据统计
- ✅ 图表可视化展示
- ✅ 趋势分析功能
- ✅ 数据对比分析
- ✅ 自定义统计条件
- ✅ 报表导出功能

**主要操作**:
- 查看各类业务统计数据
- 分析业务发展趋势
- 对比不同时期数据
- 导出统计报表
- 设置统计维度和条件

---

## 📊 页面功能统计

### 📈 客户端页面统计
| 功能模块 | 页面数量 | 主要功能 |
|----------|----------|----------|
| **首页与账户** | 3页 | 登录、首页、密码管理 |
| **短信业务** | 3页 | 普通短信、个性化短信、状态查询 |
| **彩信业务** | 3页 | 彩信发送、动态彩信、模板管理 |
| **语音短信** | 2页 | 语音发送、模板管理 |
| **统计查询** | 2页 | 发送统计、消费明细 |
| **内容管理** | 1页 | 内容报备 |
| **客户信息** | 1页 | 信息管理 |
| **文件管理** | 1页 | 文件管理 |
| **通知帮助** | 2页 | 历史通知、使用手册 |
| **总计** | **18页** | 完整的客户端功能 |

### 🛠️ 管理端页面统计
| 功能模块 | 页面数量 | 主要功能 |
|----------|----------|----------|
| **系统管理** | 8页 | 首页、统计、日志、控制台等 |
| **客户管理** | 15页 | 各类客户管理、充值、配置 |
| **通道管理** | 8页 | 通道配置、监控、限速策略 |
| **内容审核** | 12页 | 签名、模板、敏感词管理 |
| **业务管理** | 10页 | 彩信、语音、消息管理 |
| **权限管理** | 6页 | 用户权限、角色管理 |
| **数据管理** | 8页 | 统计报表、详情查询 |
| **系统配置** | 15页 | 各类系统参数配置 |
| **总计** | **82页** | 完整的管理端功能 |

### 🎯 功能复杂度分析
| 复杂度等级 | 页面数量 | 说明 |
|------------|----------|------|
| **高复杂度** | 25页 | 涉及复杂业务逻辑、多步骤操作 |
| **中复杂度** | 45页 | 标准CRUD操作、数据展示 |
| **低复杂度** | 30页 | 简单查询、基础配置 |
| **总计** | **100页** | 覆盖完整业务流程 |

### 📱 页面类型分布
| 页面类型 | 数量 | 占比 |
|----------|------|------|
| **业务操作页面** | 35页 | 35% |
| **数据查询页面** | 30页 | 30% |
| **管理配置页面** | 25页 | 25% |
| **系统功能页面** | 10页 | 10% |

---

## 📋 重构建议

### 🎯 页面优化重点
1. **高频使用页面**: 短信发送、客户管理、通道监控
2. **复杂业务页面**: 彩信制作、个性化短信、权限管理
3. **数据展示页面**: 统计报表、监控面板、详情查询

### 🔧 技术改进方向
1. **前端现代化**: 使用Vue.js/React重构交互复杂的页面
2. **响应式设计**: 适配移动端和平板设备
3. **用户体验优化**: 简化操作流程、提升页面加载速度
4. **组件化开发**: 提取公共组件、提高开发效率

---

*📅 文档更新时间: 2025-08-01*
*📋 页面总数: 100+ 个主要页面*
*🔄 维护状态: 持续更新中*