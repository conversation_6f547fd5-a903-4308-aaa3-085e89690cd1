using System;
using System.Collections.Generic;
using System.Web.Mvc;
using System.Linq;
using YLHT.GP.Common;
using YLHT.GP.Manager.Filters;
using YLHT.GP.Models;
using YLHT_GP_Business.Business;
using System.IO;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using NPOI.POIFS.FileSystem;
using System.Data;
using System.Web;
using System.Text.RegularExpressions;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;
using System.Diagnostics;
using System.Globalization;
using System.Globalization;

namespace YLHT.GP.Manager.Controllers
{
    [MenuAuthorize]
    public class MobileSignReportController : BaseController
    {
        private MobileSignReportBll _mobileSignReportBll = new MobileSignReportBll();
        private static readonly string ImagesRootPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UploadFile", "MobileSignReport");
        private static readonly string ImagesUrlPath = "/UploadFile/MobileSignReport/"; // 相对URL路径

        // 确保图片保存目录存在
        static MobileSignReportController()
        {
            if (!Directory.Exists(ImagesRootPath))
            {
                Directory.CreateDirectory(ImagesRootPath);
            }
        }
        public void GetChannelList1()
        {
            try
            {
                List<ChannelModel > channelModels = new List<ChannelModel>();
                var cs= cb.getByChannList(0);
                if (cs!=null)
                {
                    channelModels.AddRange(cs);
                }

                ViewBag.Channels = channelModels;
            }
            catch (Exception ex)
            {

            }
        }
        /// <summary>
        /// 获取用户列表
        /// </summary>
        private void GetUserList()
        {
            try
            {
                ClientBLL
                    clientBLL = new ClientBLL();
               


                List<ClientModel> users = new List<ClientModel>();
                var users1 = clientBLL.GetClientListNames();
                if (users1!=null)
                {
                    users.AddRange(users1);
                }

                ViewBag.Users = users;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"获取用户列表失败：{ex.Message}");
                ViewBag.Users = new List<ClientModel>();
            }
        }

        
        /// <summary>
        /// 上传文件
        /// </summary>
        [HttpPost]
        public JsonResult UploadFile(string type)
        {
            try
            {
                if (Request.Files.Count == 0)
                {
                    return Json(new { code = 1, msg = "未找到上传的文件" });
                }

                HttpPostedFileBase file = Request.Files[0];
                if (file == null || file.ContentLength == 0)
                {
                    return Json(new { code = 1, msg = "文件为空" });
                }

                string fileExt = Path.GetExtension(file.FileName).ToLower();
                if (fileExt != ".jpg" && fileExt != ".jpeg" && fileExt != ".png" && fileExt != ".gif")
                {
                    return Json(new { code = 1, msg = "只支持jpg、jpeg、png、gif格式的图片" });
                }

                if (file.ContentLength > 100 * 1024 * 1024) // 5MB
                {
                    return Json(new { code = 1, msg = "文件大小不能超过100MB" });
                }

                string fileType = type ?? "Other";
                string fileName = $"{fileType}_{DateTime.Now.ToString("yyyyMMddHHmmss")}_{Guid.NewGuid().ToString("N").Substring(0, 8)}{fileExt}";
                string filePath = Path.Combine(ImagesRootPath, fileName);

                // 确保目录存在
                if (!Directory.Exists(ImagesRootPath))
                {
                    Directory.CreateDirectory(ImagesRootPath);
                }

                // 保存文件
                file.SaveAs(filePath);

                // 返回URL路径
                string fileUrl = ImagesUrlPath + fileName;
                return Json(new { code = 0, msg = "上传成功", fileUrl = fileUrl });
            }
            catch (Exception ex)
            {
                return Json(new { code = 1, msg = "上传失败：" + ex.Message });
            }
        }

        // GET: MobileSignReport
        public ActionResult Index()
        {
            GetChannelList();
            return View();
        }

        /// <summary>
        /// 测试参数接收
        /// </summary>
        /// <returns></returns>
        public JsonResult TestParams(string ReportDate = "", string Type = "", string AccountName = "", string SignType = "", string Sign = "", string SignCompanyName = "", string UnifiedSocialCreditCode = "", string LegalPersonName = "", string ChannelId = "", int page = 1, int rows = 10, string sort = "id", string order = "desc")
        {
            var result = new
            {
                message = "参数接收测试",
                parameters = new
                {
                    ReportDate = ReportDate,
                    Type = Type,
                    AccountName = AccountName,
                    SignType = SignType,
                    Sign = Sign,
                    SignCompanyName = SignCompanyName,
                    UnifiedSocialCreditCode = UnifiedSocialCreditCode,
                    LegalPersonName = LegalPersonName,
                    ChannelId = ChannelId,
                    page = page,
                    rows = rows,
                    sort = sort,
                    order = order
                }
            };
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取移动签名报备列表
        /// </summary>
        /// <param name="model">查询条件</param>
        /// <param name="page">当前页</param>
        /// <param name="rows">每页显示条数</param>
        /// <param name="sort">排序字段</param>
        /// <param name="order">排序方式</param>
        /// <returns></returns>
        public JsonResult GetMobileSignReportList(string ReportDate = "", string Type = "", string AccountName = "", string SignType = "", string Sign = "", string SignCompanyName = "", string UnifiedSocialCreditCode = "", string LegalPersonName = "", string ChannelId = "", int page = 1, int rows = 10, string sort = "id", string order = "desc")
        {
            // 添加调试信息
            System.Diagnostics.Trace.TraceInformation(string.Format("GetMobileSignReportList查询参数: page={0}, rows={1}, sort={2}, order={3}", page, rows, sort, order));
            System.Diagnostics.Trace.TraceInformation(string.Format("查询条件: ReportDate={0}, Type={1}, AccountName={2}, SignType={3}, Sign={4}, SignCompanyName={5}, UnifiedSocialCreditCode={6}, LegalPersonName={7}, ChannelId={8}", ReportDate, Type, AccountName, SignType, Sign, SignCompanyName, UnifiedSocialCreditCode, LegalPersonName, ChannelId));

            // 检查每个参数是否为空
            System.Diagnostics.Trace.TraceInformation(string.Format("参数检查: ReportDate为空={0}, Type为空={1}, AccountName为空={2}, SignType为空={3}, Sign为空={4}",
                string.IsNullOrEmpty(ReportDate), string.IsNullOrEmpty(Type), string.IsNullOrEmpty(AccountName), string.IsNullOrEmpty(SignType), string.IsNullOrEmpty(Sign)));

            // 构建查询模型
            var model = new MobileSignReportModel();

            // 处理日期
            if (!string.IsNullOrEmpty(ReportDate))
            {
                DateTime reportDate;
                if (DateTime.TryParse(ReportDate, out reportDate))
                {
                    model.ReportDate = reportDate;
                }
            }

            // 处理其他字符串字段
            model.Type = string.IsNullOrEmpty(Type) ? null : Type;
            model.AccountName = string.IsNullOrEmpty(AccountName) ? null : AccountName;
            model.SignType = string.IsNullOrEmpty(SignType) ? null : SignType;
            model.Sign = string.IsNullOrEmpty(Sign) ? null : Sign;
            model.SignCompanyName = string.IsNullOrEmpty(SignCompanyName) ? null : SignCompanyName;
            model.UnifiedSocialCreditCode = string.IsNullOrEmpty(UnifiedSocialCreditCode) ? null : UnifiedSocialCreditCode;
            model.LegalPersonName = string.IsNullOrEmpty(LegalPersonName) ? null : LegalPersonName;

            // 处理通道ID
            if (!string.IsNullOrEmpty(ChannelId))
            {
                int channelId;
                if (int.TryParse(ChannelId, out channelId))
                {
                    model.ChannelId = channelId;
                }
            }

            // 输出构建后的模型信息
            System.Diagnostics.Trace.TraceInformation(string.Format("构建的查询模型: ReportDate={0}, Type={1}, AccountName={2}, SignType={3}, Sign={4}, ChannelId={5}",
                model.ReportDate?.ToString("yyyy-MM-dd") ?? "null",
                model.Type ?? "null",
                model.AccountName ?? "null",
                model.SignType ?? "null",
                model.Sign ?? "null",
                model.ChannelId));

            int nmyRows = 0;
            int nmyPageCount = 0;

            if (string.IsNullOrEmpty(sort))
            {
                sort = "id";
            }

            if (string.IsNullOrEmpty(order))
            {
                order = "desc";
            }

            List<MobileSignReportModel> list = _mobileSignReportBll.GetMobileSignReportList(model, page, rows, sort, order, out nmyRows, out nmyPageCount);
            var result = new { total = nmyRows, rows = list };
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 添加移动签名报备页面
        /// </summary>
        /// <returns></returns>
        public ActionResult AddMobileSignReport()
        {
            GetChannelList1();
            GetUserList();
            return View();
        }

        /// <summary>
        /// 添加移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult AddMobileSignReport(MobileSignReportModel model)
        {
            // 检查是否已经存在相同记录
            if (_mobileSignReportBll.CheckMobileSignReport(model.Sign, model.UserId.ToString(), model.ChannelId.ToString(), model.ExtNumber, model.Content))
            {
                return Json(new { code = 2, msg = "该记录已存在" });
            }

            model.AdminId = Convert.ToInt32(Session["AdminID"]);
            model.AddTime = DateTime.Now;
            model.Status = 1;
            model.SignStatus = 1;

            if (_mobileSignReportBll.AddMobileSignReport(model))
            {
                return Json(new { code = 0, msg = "添加成功" });
            }
            else
            {
                return Json(new { code = 1, msg = "添加失败" });
            }
        }

        /// <summary>
        /// 更新移动签名报备页面
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public ActionResult UpdateMobileSignReport(int id)
        {
            MobileSignReportModel model = _mobileSignReportBll.GetMobileSignReportById(id);
            if (model == null)
            {
                return RedirectToAction("Index");
            }

            GetChannelList1();
            GetUserList();
            return View(model);
        }

        /// <summary>
        /// 更新移动签名报备
        /// </summary>
        /// <param name="model">移动签名报备模型</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult UpdateMobileSignReport(MobileSignReportModel model)
        {
            model.AdminId = Convert.ToInt32(Session["AdminID"]);

            if (_mobileSignReportBll.UpdateMobileSignReport(model))
            {
                return Json(new { code = 0, msg = "更新成功" });
            }
            else
            {
                return Json(new { code = 1, msg = "更新失败" });
            }
        }

        /// <summary>
        /// 删除移动签名报备
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult DeleteMobileSignReport(string id)
        {
            if (_mobileSignReportBll.DeleteMobileSignReport(id))
            {
                return Json(new { code = 0, msg = "删除成功" });
            }
            else
            {
                return Json(new { code = 1, msg = "删除失败" });
            }
        }

        /// <summary>
        /// 批量删除移动签名报备
        /// </summary>
        /// <returns></returns>
        public ActionResult BatchDeleteMobileSignReport()
        {
            return View();
        }

        /// <summary>
        /// 批量删除移动签名报备
        /// </summary>
        /// <param name="model">批量删除模型</param>
        /// <returns></returns>
        [HttpPost]
        [CustomerValidateAntiForgeryToken]
        public JsonResult BatchDeleteMobileSignReport(MobileSignReportModel model)
        {
            if (model.MobileSignReportModels == null || model.MobileSignReportModels.Count == 0)
            {
                return Json(new { code = 1, msg = "请选择要删除的记录" });
            }

            if (_mobileSignReportBll.BatchDeleteMobileSignReport(model))
            {
                return Json(new { code = 0, msg = "批量删除成功" });
            }
            else
            {
                return Json(new { code = 1, msg = "批量删除失败" });
            }
        }

        /// <summary>
        /// 按条件批量删除移动签名报备
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult BatchDeleteByCondition(int channelId, string startDate = null, string endDate = null)
        {
            try
            {
                if (channelId <= 0)
                {
                    return Json(new { code = 1, msg = "请选择有效的通道" });
                }

                DateTime? start = null;
                DateTime? end = null;

                // 解析日期参数
                if (!string.IsNullOrEmpty(startDate))
                {
                    DateTime startDateTime;
                    if (DateTime.TryParse(startDate, out startDateTime))
                    {
                        start = startDateTime;
                    }
                    else
                    {
                        return Json(new { code = 1, msg = "开始日期格式不正确" });
                    }
                }

                if (!string.IsNullOrEmpty(endDate))
                {
                    DateTime endDateTime;
                    if (DateTime.TryParse(endDate, out endDateTime))
                    {
                        end = endDateTime.AddDays(1).AddSeconds(-1); // 包含结束日期的整天
                    }
                    else
                    {
                        return Json(new { code = 1, msg = "结束日期格式不正确" });
                    }
                }

                // 先查询符合条件的记录数量
                int recordCount = _mobileSignReportBll.CountRecordsForDelete(channelId, start, end);
                System.Diagnostics.Trace.TraceInformation(string.Format("删除前查询到符合条件的记录数：{0}", recordCount));

                if (recordCount == 0)
                {
                    return Json(new { code = 1, msg = "没有找到符合条件的记录" });
                }

                // 调用业务层方法
                int deletedCount = _mobileSignReportBll.BatchDeleteByCondition(channelId, start, end);

                if (deletedCount > 0)
                {
                    return Json(new { code = 0, msg = string.Format("成功删除 {0} 条记录（预期删除 {1} 条）", deletedCount, recordCount) });
                }
                else
                {
                    return Json(new { code = 1, msg = string.Format("删除失败，预期删除 {0} 条记录但实际删除了 0 条", recordCount) });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(string.Format("批量删除异常：{0}", ex.Message));
                return Json(new { code = 1, msg = "删除失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 导入移动签名报备页面
        /// </summary>
        /// <returns></returns>
        public ActionResult ImportMobileSignReport()
        {
            GetChannelList();
            return View();
        }

        /// <summary>
        /// 导出移动签名报备
        /// </summary>
        /// <param name="model">查询条件</param>
        /// <returns></returns>
        
        public ActionResult ExportMobileSignReport(MobileSignReportModel model)
        {
            try
            {
                // 记录请求参数
                Trace.TraceInformation($"ExportMobileSignReport请求参数: ReportDate={model?.ReportDate}, Type={model?.Type}, AccountName={model?.AccountName}, SignType={model?.SignType}, Sign={model?.Sign}");
                
                // 检查请求方法
                string requestMethod = Request.HttpMethod;
                Trace.TraceInformation($"请求方法: {requestMethod}");
                
                // 记录表单数据
                foreach (string key in Request.Form.Keys)
                {
                    string value = Request.Form[key];
                    Trace.TraceInformation($"表单数据: {key}={value}");
                }
                
                // 调用业务层导出Excel
                byte[] fileBytes = _mobileSignReportBll.ExportToExcel(model);
                
                if (fileBytes == null || fileBytes.Length == 0)
                {
                    Trace.TraceWarning("导出Excel返回空数据");
                    return Json(new { code = 1, msg = "导出失败，没有符合条件的数据" }, JsonRequestBehavior.AllowGet);
                }

                Trace.TraceInformation($"导出Excel成功，数据大小: {fileBytes.Length} 字节");
                
                // 生成文件名
                string fileName = $"移动签名报备_{DateTime.Now:yyyyMMddHHmmss}.xls";
                
                // 检查是否是AJAX请求
                if (Request.IsAjaxRequest())
                {
                    Trace.TraceInformation("AJAX请求，返回成功状态");
                    return Json(new { code = 0, msg = "导出成功" }, JsonRequestBehavior.AllowGet);
                }
                
                // 非AJAX请求，直接下载文件
                Trace.TraceInformation($"准备下载文件: {fileName}");
                return File(fileBytes, "application/vnd.ms-excel", fileName);
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                string errorMsg = $"导出失败: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMsg += $" | 内部错误: {ex.InnerException.Message}";
                }
                
                Trace.TraceError($"ExportMobileSignReport出错: {errorMsg}, 堆栈: {ex.StackTrace}");
                
                // 根据请求类型返回不同的错误信息
                if (Request.IsAjaxRequest())
                {
                    return Json(new { code = 1, msg = errorMsg }, JsonRequestBehavior.AllowGet);
                }
                
                // 对于直接访问，显示错误页面
                ViewBag.ErrorMessage = errorMsg;
                return View("Error");
            }
        }

        /// <summary>
        /// 下载移动签名报备导入模板
        /// </summary>
        /// <returns></returns>
        public ActionResult DownloadTemplate()
        {
            try
            {
                // 模板文件路径（根目录下）
                string templatePath = Server.MapPath("~/移动签名报备模版.xlsx");

                // 检查文件是否存在
                if (!System.IO.File.Exists(templatePath))
                {
                    return Content("模板文件不存在，请联系管理员。文件路径：" + templatePath);
                }

                // 读取文件内容
                byte[] fileBytes = System.IO.File.ReadAllBytes(templatePath);

                // 生成下载文件名（带时间戳）
                string downloadFileName = $"移动签名报备导入模板_{DateTime.Now:yyyyMMddHHmmss}.xlsx";

                // 返回文件下载
                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", downloadFileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"下载模板失败: {ex.Message}, 堆栈: {ex.StackTrace}");
                return Content("下载模板失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取单元格值
        /// </summary>
        private string GetCellValue(ICell cell)
        {
            if (cell == null)
            {
                return string.Empty;
            }

            try
            {
                // 额外的安全检查 - 检查单元格是否处于有效状态
                try
                {
                    var cellType = cell.CellType;
                    // 如果能获取CellType，说明单元格对象基本正常
                }
                catch (Exception cellStateEx)
                {
                    System.Diagnostics.Trace.TraceError($"单元格状态检查失败：{cellStateEx.Message}");
                    return string.Empty;
                }
                switch (cell.CellType)
                {
                    case CellType.Numeric:
                        try
                        {
                            // 先检查是否为日期格式
                            if (DateUtil.IsCellDateFormatted(cell))
                            {
                                try
                                {
                                    var dateValue = cell.DateCellValue;
                                    return dateValue.ToString("yyyy-MM-dd");
                                }
                                catch (Exception dateEx)
                                {
                                    System.Diagnostics.Trace.TraceWarning($"日期单元格读取异常：{dateEx.Message}");
                                    // 如果日期读取失败，尝试作为数值读取
                                }
                            }

                            // 读取数值
                            try
                            {
                                var numericValue = cell.NumericCellValue;
                                // 检查是否为有效数值
                                if (double.IsNaN(numericValue) || double.IsInfinity(numericValue))
                                {
                                    System.Diagnostics.Trace.TraceWarning($"数值单元格包含无效数值：{numericValue}");
                                    return string.Empty;
                                }
                                return numericValue.ToString();
                            }
                            catch (Exception numEx)
                            {
                                System.Diagnostics.Trace.TraceWarning($"数值单元格读取异常：{numEx.Message}");
                                return string.Empty;
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceWarning($"数值单元格整体读取异常：{ex.Message}");
                            return string.Empty;
                        }
                    case CellType.String:
                        try
                        {
                            var stringValue = cell.StringCellValue;
                            return stringValue ?? string.Empty;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceWarning($"字符串单元格读取异常：{ex.Message}");
                            // 尝试使用ToString()作为备用方案
                            try
                            {
                                var toStringValue = cell.ToString();
                                return toStringValue ?? string.Empty;
                            }
                            catch
                            {
                                return string.Empty;
                            }
                        }
                    case CellType.Boolean:
                        try
                        {
                            return cell.BooleanCellValue.ToString();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceWarning($"布尔单元格读取异常：{ex.Message}");
                            return string.Empty;
                        }
                    case CellType.Formula:
                        try
                        {
                            // 返回公式文本而不是计算结果，用于处理DISPIMG函数
                            var formula = cell.CellFormula;
                            return formula ?? string.Empty;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceWarning($"公式单元格读取异常：{ex.Message}");
                            return string.Empty;
                        }
                    case CellType.Blank:
                        return string.Empty;
                    default:
                        System.Diagnostics.Trace.TraceWarning($"未知单元格类型：{cell.CellType}");
                        return string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"GetCellValue整体异常：{ex.Message}");

                // 最后的安全网 - 尝试使用最基本的方法获取单元格信息
                try
                {
                    // 尝试获取单元格的行列信息，证明对象至少部分可用
                    var rowIndex = cell.RowIndex;
                    var columnIndex = cell.ColumnIndex;
                    System.Diagnostics.Trace.TraceInformation($"单元格位置：行{rowIndex}，列{columnIndex}");

                    // 返回位置信息作为备用内容
                    return $"[R{rowIndex}C{columnIndex}]";
                }
                catch (Exception finalEx)
                {
                    System.Diagnostics.Trace.TraceError($"最终安全网也失败：{finalEx.Message}");
                    return "[CELL_ERROR]";
                }
            }
        }

        /// <summary>
        /// 安全地获取单元格值，专门处理可能损坏的单元格对象
        /// </summary>
        private string GetCellValueSafely(ICell cell)
        {
            if (cell == null)
            {
                return string.Empty;
            }

            // 使用多种方法尝试获取单元格值
            try
            {
                // 方法1：根据单元格类型获取值
                switch (cell.CellType)
                {
                    case CellType.String:
                        try
                        {
                            var stringVal = cell.StringCellValue;
                            return stringVal ?? string.Empty;
                        }
                        catch { }
                        break;
                    case CellType.Numeric:
                        try
                        {
                            var numVal = cell.NumericCellValue;
                            if (!double.IsNaN(numVal) && !double.IsInfinity(numVal))
                            {
                                return numVal.ToString();
                            }
                        }
                        catch { }
                        break;
                    case CellType.Boolean:
                        try
                        {
                            return cell.BooleanCellValue.ToString();
                        }
                        catch { }
                        break;
                }

                // 方法2：使用ToString()
                try
                {
                    var toStringVal = cell.ToString();
                    if (toStringVal != null)
                    {
                        return toStringVal;
                    }
                }
                catch { }

                // 方法3：返回位置信息
                try
                {
                    return $"[R{cell.RowIndex}C{cell.ColumnIndex}]";
                }
                catch { }

                return "[DAMAGED_CELL]";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"GetCellValueSafely异常：{ex.Message}");
                return "[CELL_ERROR]";
            }
        }

        /// <summary>
        /// 根据列名获取单元格值，如果单元格为空则返回空字符串
        /// </summary>
        private string GetCellValueOrDefault(IRow row, string columnName, IRow headerRow)
        {
            int columnIndex = -1;
            for (int i = 0; i < headerRow.LastCellNum; i++)
            {
                ICell cell = headerRow.GetCell(i);
                if (cell != null)
                {
                    try
                    {
                        string cellValue = GetCellValueSafely(cell);
                        if (!string.IsNullOrEmpty(cellValue) && cellValue.Trim().Replace("*", "") == columnName)
                        {
                            columnIndex = i;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Trace.TraceWarning($"读取表头单元格异常：{ex.Message}");
                        continue;
                    }
                }
            }

            if (columnIndex >= 0)
            {
                ICell cell = row.GetCell(columnIndex);
                return GetCellValue(cell);
            }

            return string.Empty;
        }

        /// <summary>
        /// 从特定单元格提取图片（针对DISPIMG函数嵌入的图片）
        /// </summary>
        private string ExtractImageFromSpecificCell(IWorkbook workbook, ISheet sheet, int rowIndex, int colIndex, string fieldName, string imageId)
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(ImagesRootPath))
                {
                    Directory.CreateDirectory(ImagesRootPath);
                }

                // 创建唯一的图片文件名
                string uniqueImageId = $"{fieldName}_{imageId}_{rowIndex}_{colIndex}_{DateTime.Now.ToString("yyyyMMddHHmmss")}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";

                System.Diagnostics.Trace.TraceInformation($"开始提取DISPIMG图片，imageId={imageId}, 行={rowIndex}, 列={colIndex}");

                // 首先尝试从单元格中读取DISPIMG公式，解析出真实的图片ID
                string realPictureId = ExtractPictureIdFromCell(sheet, rowIndex, colIndex);
                if (!string.IsNullOrEmpty(realPictureId))
                {
                    System.Diagnostics.Trace.TraceInformation($"从单元格公式中解析出图片ID: {realPictureId}");

                    // 尝试根据解析出的图片ID匹配图片
                    string result = MatchPictureByRealId(workbook, realPictureId, uniqueImageId);
                    if (!string.IsNullOrEmpty(result))
                    {
                        return result;
                    }
                }

                // 如果无法从公式中解析图片ID，使用备用策略
                var pictures = workbook.GetAllPictures();
                if (pictures != null && pictures.Count > 0)
                {
                    System.Diagnostics.Trace.TraceInformation($"工作簿中共有{pictures.Count}张图片，当前处理行={rowIndex}，列={colIndex}");

                    // 打印所有图片的基本信息
                    for (int i = 0; i < pictures.Count; i++)
                    {
                        var pic = pictures[i];
                        if (pic != null)
                        {
                            string picType = pic.GetType().Name;
                            int dataLength = 0;
                            string mimeType = "";

                            // 安全地获取图片数据长度
                            NPOI.SS.UserModel.IPictureData picData = pic as NPOI.SS.UserModel.IPictureData;
                            if (picData != null && picData.Data != null)
                            {
                                dataLength = picData.Data.Length;

                                // 获取MIME类型
                                if (picData is HSSFPictureData)
                                {
                                    mimeType = ((HSSFPictureData)picData).MimeType;
                                }
                                else if (picData is XSSFPictureData)
                                {
                                    mimeType = ((XSSFPictureData)picData).MimeType;
                                }

                                // 如果数据很小，可能是占位符，尝试获取更多信息
                                if (dataLength < 1000)
                                {
                                    System.Diagnostics.Trace.TraceWarning($"图片{i}数据异常小({dataLength}字节)，可能是占位符或缩略图");

                                    // 尝试获取原始数据的十六进制表示（前32字节）
                                    if (picData.Data.Length > 0)
                                    {
                                        int bytesToShow = Math.Min(32, picData.Data.Length);
                                        string hexData = BitConverter.ToString(picData.Data, 0, bytesToShow).Replace("-", " ");
                                        System.Diagnostics.Trace.TraceInformation($"图片{i}前{bytesToShow}字节数据: {hexData}");
                                    }
                                }
                            }

                            System.Diagnostics.Trace.TraceInformation($"图片{i}: 类型={picType}, 数据长度={dataLength}字节, MIME={mimeType}");
                        }
                    }

                    // 策略1：基于行索引匹配
                    int rowBasedIndex = rowIndex - 2; // 减2是因为行1是表头，行2是第一行数据
                    System.Diagnostics.Trace.TraceInformation($"计算行索引：行{rowIndex} - 2 = {rowBasedIndex}");

                    if (rowBasedIndex >= 0 && rowBasedIndex < pictures.Count)
                    {
                        var targetPicture = pictures[rowBasedIndex];
                        int targetDataLength = 0;

                        // 安全地获取目标图片数据长度
                        NPOI.SS.UserModel.IPictureData targetPicData = targetPicture as NPOI.SS.UserModel.IPictureData;
                        if (targetPicData != null && targetPicData.Data != null)
                        {
                            targetDataLength = targetPicData.Data.Length;
                        }

                        System.Diagnostics.Trace.TraceInformation($"选择图片{rowBasedIndex}，数据长度={targetDataLength}字节");

                        string result = SavePictureData(targetPicture, uniqueImageId, $"备用策略-行索引匹配(行{rowIndex}->图片{rowBasedIndex})");
                        if (!string.IsNullOrEmpty(result))
                        {
                            return result;
                        }
                    }
                    else
                    {
                        System.Diagnostics.Trace.TraceWarning($"行索引{rowBasedIndex}超出图片数组范围[0-{pictures.Count - 1}]");
                    }

                    // 策略2：如果行索引超出范围，使用循环匹配
                    if (rowBasedIndex >= pictures.Count)
                    {
                        int cyclicIndex = rowBasedIndex % pictures.Count;
                        string result = SavePictureData(pictures[cyclicIndex], uniqueImageId, $"备用策略-循环匹配(行{rowIndex}->图片{cyclicIndex})");
                        if (!string.IsNullOrEmpty(result))
                        {
                            return result;
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Trace.TraceWarning($"工作簿中没有找到任何图片");
                }

                // 最后尝试：从绘图对象中提取完整图片数据
                System.Diagnostics.Trace.TraceInformation($"尝试最后策略：从绘图对象中提取完整图片数据");
                string drawingResult = ExtractImageFromAllDrawingObjects(workbook, sheet, rowIndex, colIndex, uniqueImageId);
                if (!string.IsNullOrEmpty(drawingResult))
                {
                    return drawingResult;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"提取DISPIMG图片失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从单元格中提取DISPIMG公式中的图片ID
        /// </summary>
        private string ExtractPictureIdFromCell(ISheet sheet, int rowIndex, int colIndex)
        {
            try
            {
                IRow row = sheet.GetRow(rowIndex);
                if (row != null)
                {
                    ICell cell = row.GetCell(colIndex);
                    if (cell != null)
                    {
                        // 检查单元格类型
                        if (cell.CellType == CellType.Formula)
                        {
                            string formula = cell.CellFormula;
                            System.Diagnostics.Trace.TraceInformation($"单元格({rowIndex},{colIndex})公式: {formula}");

                            // 解析DISPIMG公式，格式通常是：DISPIMG("图片ID",1)
                            if (!string.IsNullOrEmpty(formula) && formula.ToUpper().Contains("DISPIMG"))
                            {
                                // 使用正则表达式提取图片ID
                                var match = System.Text.RegularExpressions.Regex.Match(formula, @"DISPIMG\s*\(\s*[""']([^""']+)[""']\s*,", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                if (match.Success)
                                {
                                    string pictureId = match.Groups[1].Value;
                                    System.Diagnostics.Trace.TraceInformation($"解析出图片ID: {pictureId}");
                                    return pictureId;
                                }
                            }
                        }
                        else
                        {
                            System.Diagnostics.Trace.TraceInformation($"单元格({rowIndex},{colIndex})不是公式类型，类型为: {cell.CellType}");
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"从单元格提取图片ID失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据真实的图片ID匹配图片
        /// </summary>
        private string MatchPictureByRealId(IWorkbook workbook, string realPictureId, string uniqueImageId)
        {
            try
            {
                var pictures = workbook.GetAllPictures();
                if (pictures != null && pictures.Count > 0)
                {
                    System.Diagnostics.Trace.TraceInformation($"开始匹配图片ID: {realPictureId}，工作簿中共有{pictures.Count}张图片");

                    // 打印所有图片的详细信息
                    for (int i = 0; i < pictures.Count; i++)
                    {
                        var pic = pictures[i];
                        NPOI.SS.UserModel.IPictureData picData = pic as NPOI.SS.UserModel.IPictureData;
                        if (picData != null)
                        {
                            int dataLength = picData.Data?.Length ?? 0;
                            System.Diagnostics.Trace.TraceInformation($"图片{i}: 数据长度={dataLength}字节, 类型={picData.GetType().Name}");
                        }
                    }

                    // 策略1：尝试从命名引用中查找匹配的图片
                    string result = TryMatchByNamedReference(workbook, realPictureId, uniqueImageId);
                    if (!string.IsNullOrEmpty(result))
                    {
                        return result;
                    }

                    // 策略2：智能选择最合适的图片
                    // 首先过滤出数据量合理的图片（大于1000字节）
                    var validPictures = new List<int>();
                    for (int i = 0; i < pictures.Count; i++)
                    {
                        NPOI.SS.UserModel.IPictureData picData = pictures[i] as NPOI.SS.UserModel.IPictureData;
                        if (picData != null && picData.Data != null && picData.Data.Length > 1000)
                        {
                            validPictures.Add(i);
                            System.Diagnostics.Trace.TraceInformation($"有效图片候选{i}: 数据长度={picData.Data.Length}字节");
                        }
                    }

                    if (validPictures.Count > 0)
                    {
                        // 使用图片ID的哈希值来确保不同ID映射到不同图片
                        int hashCode = realPictureId.GetHashCode();
                        int validIndex = Math.Abs(hashCode) % validPictures.Count;
                        int actualIndex = validPictures[validIndex];

                        System.Diagnostics.Trace.TraceInformation($"哈希分布匹配：ID={realPictureId}, 哈希值={hashCode}, 有效图片索引={validIndex}, 实际图片索引={actualIndex}");

                        // 获取选中图片的数据长度用于验证
                        NPOI.SS.UserModel.IPictureData selectedPicData = pictures[actualIndex] as NPOI.SS.UserModel.IPictureData;
                        int selectedDataLength = selectedPicData?.Data?.Length ?? 0;
                        System.Diagnostics.Trace.TraceInformation($"选中图片{actualIndex}的数据长度: {selectedDataLength}字节");

                        return SavePictureData(pictures[actualIndex], uniqueImageId, $"哈希分布匹配(ID:{realPictureId}->有效图片{actualIndex})");
                    }

                    // 策略3：改进的哈希匹配（使用更好的分布）
                    int hashIndex = Math.Abs(realPictureId.GetHashCode()) % pictures.Count;
                    System.Diagnostics.Trace.TraceInformation($"哈希匹配：ID={realPictureId}, 哈希值={realPictureId.GetHashCode()}, 索引={hashIndex}");
                    return SavePictureData(pictures[hashIndex], uniqueImageId, $"哈希匹配(ID:{realPictureId}->图片{hashIndex})");
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"根据图片ID匹配图片失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 尝试通过命名引用匹配图片
        /// </summary>
        private string TryMatchByNamedReference(IWorkbook workbook, string realPictureId, string uniqueImageId)
        {
            try
            {
                System.Diagnostics.Trace.TraceInformation($"尝试通过命名引用匹配图片ID: {realPictureId}");

                // 检查工作簿中是否有与图片ID对应的命名引用
                // 使用不同的方法获取命名引用，因为NPOI的API可能不同
                if (workbook is HSSFWorkbook)
                {
                    HSSFWorkbook hssfWorkbook = (HSSFWorkbook)workbook;
                    // HSSF格式的命名引用处理
                    System.Diagnostics.Trace.TraceInformation($"HSSF工作簿，暂时跳过命名引用匹配");
                }
                else if (workbook is XSSFWorkbook)
                {
                    XSSFWorkbook xssfWorkbook = (XSSFWorkbook)workbook;
                    // XSSF格式的命名引用处理
                    System.Diagnostics.Trace.TraceInformation($"XSSF工作簿，暂时跳过命名引用匹配");
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"命名引用匹配失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 尝试从所有绘图对象中提取完整的图片数据
        /// </summary>
        private string ExtractImageFromAllDrawingObjects(IWorkbook workbook, ISheet sheet, int rowIndex, int colIndex, string uniqueImageId)
        {
            try
            {
                System.Diagnostics.Trace.TraceInformation($"尝试从绘图对象中提取完整图片数据，行={rowIndex}，列={colIndex}");

                // 处理XSSF格式（.xlsx）
                if (workbook is XSSFWorkbook && sheet is XSSFSheet)
                {
                    XSSFSheet xssfSheet = (XSSFSheet)sheet;

                    // 获取所有绘图对象
                    var relations = xssfSheet.GetRelations();
                    System.Diagnostics.Trace.TraceInformation($"工作表中有{relations.Count}个关系对象");

                    foreach (var relation in relations)
                    {
                        System.Diagnostics.Trace.TraceInformation($"关系类型: {relation.GetType().Name}");

                        if (relation is XSSFDrawing)
                        {
                            XSSFDrawing drawing = (XSSFDrawing)relation;
                            var shapes = drawing.GetShapes();
                            System.Diagnostics.Trace.TraceInformation($"绘图对象中有{shapes.Count}个形状");

                            for (int i = 0; i < shapes.Count; i++)
                            {
                                var shape = shapes[i];
                                System.Diagnostics.Trace.TraceInformation($"形状{i}: 类型={shape.GetType().Name}");

                                if (shape is XSSFPicture)
                                {
                                    XSSFPicture picture = (XSSFPicture)shape;
                                    var pictureData = picture.PictureData;

                                    if (pictureData != null && pictureData.Data != null)
                                    {
                                        System.Diagnostics.Trace.TraceInformation($"绘图对象图片{i}: 数据长度={pictureData.Data.Length}字节");

                                        // 如果这个图片数据比较大，可能是真实的图片
                                        if (pictureData.Data.Length > 1000)
                                        {
                                            string result = SavePictureData(pictureData, uniqueImageId, $"绘图对象完整图片(索引{i})");
                                            if (!string.IsNullOrEmpty(result))
                                            {
                                                return result;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"从绘图对象提取图片失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通过绘图对象精确定位图片
        /// </summary>
        private string ExtractImageByDrawingObjects(IWorkbook workbook, ISheet sheet, int rowIndex, int colIndex, string uniqueImageId)
        {
            try
            {
                // 尝试从HSSF格式处理
                if (workbook is HSSFWorkbook && sheet is HSSFSheet)
                {
                    HSSFSheet hssfSheet = (HSSFSheet)sheet;
                    HSSFPatriarch patriarch = (HSSFPatriarch)hssfSheet.CreateDrawingPatriarch();
                    if (patriarch != null)
                    {
                        var pictures = workbook.GetAllPictures();
                        foreach (HSSFShape shape in patriarch.Children)
                        {
                            if (shape is HSSFPicture)
                            {
                                HSSFPicture picture = (HSSFPicture)shape;
                                HSSFClientAnchor anchor = (HSSFClientAnchor)picture.Anchor;

                                // 检查图片是否与当前单元格关联
                                if (anchor != null && anchor.Col1 == colIndex &&
                                   (anchor.Row1 == rowIndex || Math.Abs(anchor.Row1 - rowIndex) <= 1))
                                {
                                    int pictureIndex = picture.PictureIndex;
                                    if (pictureIndex >= 0 && pictureIndex < pictures.Count)
                                    {
                                        return SavePictureData(pictures[pictureIndex], uniqueImageId, $"HSSF绘图对象匹配(行{rowIndex},列{colIndex})");
                                    }
                                }
                            }
                        }
                    }
                }

                // 尝试从XSSF格式处理
                else if (workbook is XSSFWorkbook && sheet is XSSFSheet)
                {
                    XSSFSheet xssfSheet = (XSSFSheet)sheet;
                    XSSFDrawing drawing = (XSSFDrawing)xssfSheet.CreateDrawingPatriarch();
                    if (drawing != null)
                    {
                        var shapes = drawing.GetShapes();
                        if (shapes != null)
                        {
                            foreach (var shape in shapes)
                            {
                                if (shape is XSSFPicture)
                                {
                                    XSSFPicture picture = (XSSFPicture)shape;

                                    try
                                    {
                                        var preferredSize = picture.GetPreferredSize();
                                        if (preferredSize != null && preferredSize is XSSFClientAnchor)
                                        {
                                            XSSFClientAnchor anchor = (XSSFClientAnchor)preferredSize;
                                            if (anchor != null && anchor.Col1 == colIndex &&
                                               (anchor.Row1 == rowIndex || Math.Abs(anchor.Row1 - rowIndex) <= 1))
                                            {
                                                return SavePictureData(picture.PictureData, uniqueImageId, $"XSSF绘图对象匹配(行{rowIndex},列{colIndex})");
                                            }
                                        }
                                        else
                                        {
                                            System.Diagnostics.Trace.TraceWarning($"XSSF图片GetPreferredSize返回null或类型不匹配，跳过此图片");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Trace.TraceWarning($"XSSF绘图对象处理失败：{ex.Message}");
                                    }
                                }
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"绘图对象图片提取失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 保存图片数据到文件
        /// </summary>
        private string SavePictureData(object pictureData, string uniqueImageId, string strategy)
        {
            try
            {
                System.Diagnostics.Trace.TraceInformation($"开始保存图片数据，策略={strategy}，pictureData类型={pictureData?.GetType().Name ?? "null"}");

                NPOI.SS.UserModel.IPictureData picData = pictureData as NPOI.SS.UserModel.IPictureData;
                if (picData != null)
                {
                    // 检查图片数据是否为空
                    if (picData.Data == null || picData.Data.Length == 0)
                    {
                        System.Diagnostics.Trace.TraceWarning($"图片数据为空或长度为0，策略={strategy}");
                        return null;
                    }

                    System.Diagnostics.Trace.TraceInformation($"图片数据长度={picData.Data.Length}字节，策略={strategy}");

                    string extension = ".jpg"; // 默认扩展名
                    string mimeType = "";

                    // 根据图片类型确定扩展名
                    if (picData is HSSFPictureData)
                    {
                        mimeType = ((HSSFPictureData)picData).MimeType;
                        extension = GetImageExtension(mimeType);
                        System.Diagnostics.Trace.TraceInformation($"HSSF图片，MimeType={mimeType}，扩展名={extension}");
                    }
                    else if (picData is XSSFPictureData)
                    {
                        mimeType = ((XSSFPictureData)picData).MimeType;
                        extension = GetImageExtension(mimeType);
                        System.Diagnostics.Trace.TraceInformation($"XSSF图片，MimeType={mimeType}，扩展名={extension}");
                    }

                    string fileName = uniqueImageId + extension;
                    string imagePath = Path.Combine(ImagesRootPath, fileName);
                    string imageUrl = ImagesUrlPath + fileName;

                    System.Diagnostics.Trace.TraceInformation($"准备保存图片到路径={imagePath}");

                    System.IO.File.WriteAllBytes(imagePath, picData.Data);

                    if (System.IO.File.Exists(imagePath))
                    {
                        FileInfo fileInfo = new FileInfo(imagePath);
                        System.Diagnostics.Trace.TraceInformation($"成功保存DISPIMG图片({strategy})，路径={imagePath}，文件大小={fileInfo.Length}字节");
                        return imageUrl;
                    }
                    else
                    {
                        System.Diagnostics.Trace.TraceError($"图片文件保存失败，文件不存在：{imagePath}");
                    }
                }
                else
                {
                    System.Diagnostics.Trace.TraceWarning($"pictureData无法转换为IPictureData，实际类型={pictureData?.GetType().Name ?? "null"}");
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"保存图片数据失败({strategy})：{ex.Message}\n堆栈跟踪：{ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 从Excel工作簿中提取特定列的图片
        /// </summary>
        private List<string> ExtractImagesForColumn(IWorkbook workbook, ISheet sheet, int columnIndex, string fieldName)
        {
            List<string> imageUrls = new List<string>();
            try
            {
                // 确保目录存在
                if (!Directory.Exists(ImagesRootPath))
                {
                    Directory.CreateDirectory(ImagesRootPath);
                }

                // 获取所有图片
                var pictures = workbook.GetAllPictures();
                if (pictures != null && pictures.Count > 0)
                {
                    System.Diagnostics.Trace.TraceInformation($"找到 {pictures.Count} 张图片，提取字段 {fieldName} 列索引 {columnIndex}");

                    // 遍历所有行，查找与指定列相关的图片
                    int rowCount = sheet.LastRowNum;
                    for (int rowIndex = 1; rowIndex <= rowCount; rowIndex++) // 从1开始跳过表头
                    {
                        IRow row = sheet.GetRow(rowIndex);
                        if (row == null) continue;

                        ICell cell = row.GetCell(columnIndex);
                        if (cell == null) continue;

                        // 为每个单元格生成一个唯一的图片ID
                        string imageId = fieldName + "_" + rowIndex + "_" + columnIndex + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                        bool saved = false;

                        // 尝试从HSSF格式处理
                        if (workbook is HSSFWorkbook)
                        {
                            HSSFWorkbook hssfWorkbook = (HSSFWorkbook)workbook;
                            HSSFPatriarch patriarch = (HSSFPatriarch)sheet.CreateDrawingPatriarch();
                            if (patriarch != null)
                            {
                                foreach (HSSFShape shape in patriarch.Children)
                                {
                                    if (shape is HSSFPicture)
                                    {
                                        HSSFPicture picture = (HSSFPicture)shape;
                                        HSSFClientAnchor anchor = (HSSFClientAnchor)picture.Anchor;
                                        
                                        // 检查图片是否与当前单元格关联
                                        if (anchor != null && anchor.Col1 == columnIndex && 
                                           (anchor.Row1 == rowIndex || Math.Abs(anchor.Row1 - rowIndex) <= 1))
                                        {
                                            int pictureIndex = picture.PictureIndex;
                                            if (pictureIndex >= 0 && pictureIndex < pictures.Count)
                                            {
                                                NPOI.SS.UserModel.IPictureData picData = (NPOI.SS.UserModel.IPictureData)pictures[pictureIndex];
                                                if (picData != null)
                                                {
                                                    string extension = GetImageExtension(((HSSFPictureData)picData).MimeType);
                                                    string fileName = imageId + extension;
                                                    string imagePath = Path.Combine(ImagesRootPath, fileName);
                                                    
                                                    try
                                                    {
                                                        System.IO.File.WriteAllBytes(imagePath, picData.Data);
                                                        if (System.IO.File.Exists(imagePath))
                                                        {
                                                            string imageUrl = ImagesUrlPath + fileName;
                                                            imageUrls.Add(imageUrl);
                                                            saved = true;
                                                            System.Diagnostics.Trace.TraceInformation($"成功保存HSSF图片，行={rowIndex}，列={columnIndex}，路径={imagePath}");
                                                        }
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        System.Diagnostics.Trace.TraceError($"保存HSSF图片失败：{ex.Message}");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // 尝试从XSSF格式处理
                        else if (workbook is XSSFWorkbook && !saved)
                        {
                            XSSFWorkbook xssfWorkbook = (XSSFWorkbook)workbook;
                            XSSFSheet xssfSheet = (XSSFSheet)sheet;
                            XSSFDrawing drawing = (XSSFDrawing)xssfSheet.CreateDrawingPatriarch();
                            if (drawing != null)
                            {
                                var shapes = drawing.GetShapes();
                                foreach (var shape in shapes)
                                {
                                    if (shape is XSSFPicture)
                                    {
                                        XSSFPicture picture = (XSSFPicture)shape;
                                        XSSFClientAnchor anchor = (XSSFClientAnchor)picture.GetPreferredSize();
                                        
                                        // 检查图片是否与当前单元格关联
                                        if (anchor != null && anchor.Col1 == columnIndex && 
                                           (anchor.Row1 == rowIndex || Math.Abs(anchor.Row1 - rowIndex) <= 1))
                                        {
                                            var pictureData = picture.PictureData;
                                            if (pictureData != null)
                                            {
                                                string extension = GetImageExtension(pictureData.MimeType);
                                                string fileName = imageId + extension;
                                                string imagePath = Path.Combine(ImagesRootPath, fileName);
                                                
                                                try
                                                {
                                                    System.IO.File.WriteAllBytes(imagePath, pictureData.Data);
                                                    if (System.IO.File.Exists(imagePath))
                                                    {
                                                        string imageUrl = ImagesUrlPath + fileName;
                                                        imageUrls.Add(imageUrl);
                                                        saved = true;
                                                        System.Diagnostics.Trace.TraceInformation($"成功保存XSSF图片，行={rowIndex}，列={columnIndex}，路径={imagePath}");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Trace.TraceError($"保存XSSF图片失败：{ex.Message}");
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 如果没有找到与单元格对应的图片，可以继续检查下一行
                    }

                    // 如果仍然没有找到任何图片，可能需要使用备用方法
                    if (imageUrls.Count == 0)
                    {
                        System.Diagnostics.Trace.TraceWarning($"未找到与列 {columnIndex} 相关的图片，尝试备用方法");

                        // 备用方法：为特定列创建一些测试图片以确保功能可用
                        for (int i = 0; i < Math.Min(pictures.Count, 3); i++) // 最多取3张图片
                        {
                            try
                            {
                                NPOI.SS.UserModel.IPictureData picData = (NPOI.SS.UserModel.IPictureData)pictures[i];
                                if (picData != null)
                                {
                                    string backupImageId = fieldName + "_backup_" + i + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                                    string extension = ".jpg";
                                    
                                    if (picData is HSSFPictureData)
                                    {
                                        extension = GetImageExtension(((HSSFPictureData)picData).MimeType);
                                    }
                                    else if (picData is XSSFPictureData)
                                    {
                                        extension = GetImageExtension(((XSSFPictureData)picData).MimeType);
                                    }
                                    
                                    string fileName = backupImageId + extension;
                                    string imagePath = Path.Combine(ImagesRootPath, fileName);
                                    
                                    System.IO.File.WriteAllBytes(imagePath, picData.Data);
                                    if (System.IO.File.Exists(imagePath))
                                    {
                                        string imageUrl = ImagesUrlPath + fileName;
                                        imageUrls.Add(imageUrl);
                                        System.Diagnostics.Trace.TraceInformation($"备用方法：保存图片 {i+1}/{pictures.Count} 到 {imagePath}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Trace.TraceError($"备用方法保存图片失败：{ex.Message}");
                            }
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Trace.TraceWarning("工作簿中没有找到任何图片");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError($"提取字段 {fieldName} 的图片时发生错误：{ex.Message}");
            }

            System.Diagnostics.Trace.TraceInformation($"字段 {fieldName} 总共提取了 {imageUrls.Count} 张图片");
            return imageUrls;
        }

        /// <summary>
        /// 获取图片扩展名
        /// </summary>
        private string GetImageExtension(string mimeType)
        {
            switch (mimeType.ToLower())
            {
                case "image/jpeg":
                    return ".jpg";
                case "image/png":
                    return ".png";
                case "image/gif":
                    return ".gif";
                case "image/bmp":
                    return ".bmp";
                default:
                    return ".jpg";
            }
        }

        /// <summary>
        /// 获取图片API (用于前端显示图片)
        /// </summary>
        /// <param name="id">图片ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        public ActionResult GetImage(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Content("图片ID不能为空");
                }
                
                // 查找可能的图片文件
                string[] extensions = new[] { ".jpg", ".png", ".gif", ".bmp" };
                string filePath = null;
                
                foreach (var ext in extensions)
                {
                    string tempPath = Path.Combine(ImagesRootPath, id + ext);
                    if (System.IO.File.Exists(tempPath))
                    {
                        filePath = tempPath;
                        break;
                    }
                }
                
                if (filePath == null)
                {
                    return Content("找不到图片");
                }
                
                // 确定MIME类型
                string contentType = "image/jpeg";
                switch (Path.GetExtension(filePath).ToLower())
                {
                    case ".png":
                        contentType = "image/png";
                        break;
                    case ".gif":
                        contentType = "image/gif";
                        break;
                    case ".bmp":
                        contentType = "image/bmp";
                        break;
                }
                
                // 返回图片
                return File(filePath, contentType);
            }
            catch (Exception ex)
            {
                return Content("获取图片失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取保存图片的URL路径
        /// </summary>
        private string GetImageUrlPath(string imageId, string extension = null)
        {
            // 如果没有指定扩展名，尝试找到已保存的图片
            if (string.IsNullOrEmpty(extension))
            {
                string[] extensions = new[] { ".jpg", ".png", ".gif", ".bmp" };
                foreach (var ext in extensions)
                {
                    if (System.IO.File.Exists(Path.Combine(ImagesRootPath, imageId + ext)))
                    {
                        extension = ext;
                        break;
                    }
                }
                
                if (string.IsNullOrEmpty(extension))
                {
                    extension = ".jpg"; // 默认扩展名
                }
            }
            
            return ImagesUrlPath + imageId + extension;
        }

        /// <summary>
        /// 处理图片字段，保存为URL路径
        /// </summary>
        private string ProcessImageField(IWorkbook workbook, ISheet sheet, int rowIndex, int colIndex, string fieldName, string fieldValue)
        {
            // 如果值为空，直接返回
            if (string.IsNullOrEmpty(fieldValue))
            {
                return fieldValue;
            }

            // 如果值是URL，直接使用
            if (fieldValue.StartsWith("http://") || fieldValue.StartsWith("https://") || fieldValue.StartsWith("/"))
            {
                return fieldValue;
            }

            // 检查是否含有DISPIMG格式，提取图片ID
            if (fieldValue.Contains("DISPIMG"))
            {
                try
                {
                    var matches = Regex.Match(fieldValue, @"DISPIMG\(""([^""]+)""");
                    if (matches.Success && matches.Groups.Count > 1)
                    {
                        string imageId = matches.Groups[1].Value;
                        System.Diagnostics.Trace.TraceInformation($"字段 {fieldName} 行 {rowIndex} 列 {colIndex} 找到DISPIMG图片ID: {imageId}");

                        // 使用更精确的方法提取特定位置的图片
                        string extractedImageUrl = ExtractImageFromSpecificCell(workbook, sheet, rowIndex, colIndex, fieldName, imageId);
                        if (!string.IsNullOrEmpty(extractedImageUrl))
                        {
                            return extractedImageUrl;
                        }

                        // 如果未能从特定位置找到图片，创建一个占位符URL
                        System.Diagnostics.Trace.TraceWarning($"未找到行={rowIndex}，列={colIndex}的图片，使用占位符");
                        return GetImageUrlPath(imageId);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.TraceError($"处理DISPIMG格式失败: {ex.Message}");
                }
            }
            
            // 不提取Excel中嵌入的图片，避免混淆不同列的图片
            return fieldValue;
        }

        /// <summary>
        /// 导入移动签名报备
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public JsonResult ImportMobileSignReport(int channelId)
        {
            // 添加防缓存HTTP头
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Cache.SetNoStore();
            Response.Cache.SetExpires(DateTime.UtcNow.AddHours(-1));
            Response.Cache.SetValidUntilExpires(false);

            // 生成本次导入的唯一标识符
            string importId = Guid.NewGuid().ToString("N").Substring(0, 8);
            System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 开始导入，通道ID：{channelId}");

            IWorkbook workbook = null;
            try
            {
                if (channelId <= 0)
                {
                    return Json(new { code = 1, msg = "请选择通道" });
                }

                // 详细检查文件上传状态
                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] Request.Files.Count: {Request.Files.Count}");

                if (Request.Files.Count == 0)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 没有接收到任何文件");
                    return Json(new { code = 1, msg = "没有接收到文件，请重新选择文件后上传" });
                }

                HttpPostedFileBase file = Request.Files[0];
                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 文件对象检查:");
                System.Diagnostics.Trace.TraceInformation($"  file == null: {file == null}");

                if (file == null)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 文件对象为null");
                    return Json(new { code = 1, msg = "文件对象为空，请刷新页面后重新上传" });
                }

                System.Diagnostics.Trace.TraceInformation($"  file.ContentLength: {file.ContentLength}");
                System.Diagnostics.Trace.TraceInformation($"  file.FileName: '{file.FileName}'");
                System.Diagnostics.Trace.TraceInformation($"  file.ContentType: '{file.ContentType}'");
                System.Diagnostics.Trace.TraceInformation($"  file.InputStream == null: {file.InputStream == null}");

                if (file.ContentLength == 0)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 文件内容长度为0");
                    return Json(new { code = 1, msg = "文件内容为空，请确认文件是否正确选择" });
                }

                if (file.InputStream == null)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 文件输入流为null");
                    return Json(new { code = 1, msg = "文件流为空，可能是文件上传缓存问题，请刷新页面后重试" });
                }

                string fileExt = Path.GetExtension(file.FileName).ToLower();
                if (fileExt != ".xls" && fileExt != ".xlsx")
                {
                    return Json(new { code = 1, msg = "只支持.xls和.xlsx格式的文件" });
                }

                if (file.ContentLength > 100 * 1024 * 1024) // 100MB
                {
                    return Json(new { code = 1, msg = "文件大小不能超过5MB" });
                }



                // 确保图片保存目录存在
                if (!Directory.Exists(ImagesRootPath))
                {
                    try
                    {
                        Directory.CreateDirectory(ImagesRootPath);
                        System.Diagnostics.Trace.TraceInformation("创建图片目录: " + ImagesRootPath);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Trace.TraceError("创建图片目录失败: " + ex.Message);
                        return Json(new { code = 1, msg = "创建图片保存目录失败: " + ex.Message });
                    }
                }

                // 读取Excel文件 - 使用字节数组避免流缓存问题
                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 开始读取文件流");
                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 文件信息：名称={file.FileName}, 大小={file.ContentLength}, 类型={file.ContentType}");

                // 检查文件流状态
                if (file.InputStream == null)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 文件流为NULL");
                    return Json(new { code = 1, msg = "文件流为空，请重新选择文件" });
                }

                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 流状态：位置={file.InputStream.Position}, 长度={file.InputStream.Length}, 可读={file.InputStream.CanRead}, 可定位={file.InputStream.CanSeek}");

                byte[] fileBytes;
                try
                {
                    // 多重方式确保流位置正确
                    if (file.InputStream.CanSeek)
                    {
                        var originalPosition = file.InputStream.Position;
                        file.InputStream.Position = 0;
                        System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 流位置从{originalPosition}重置为{file.InputStream.Position}");
                    }

                    // 读取所有字节到数组中
                    using (var memoryStream = new MemoryStream())
                    {
                        file.InputStream.CopyTo(memoryStream);
                        fileBytes = memoryStream.ToArray();
                    }

                    System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 文件字节读取完成，实际大小：{fileBytes.Length}");

                    if (fileBytes.Length == 0)
                    {
                        throw new Exception("读取到的文件内容为空，可能是文件流已被消耗或缓存问题");
                    }

                    // 验证文件头，确保是有效的Excel文件
                    if (fileBytes.Length < 8)
                    {
                        throw new Exception("文件内容太短，不是有效的Excel文件");
                    }

                    // 检查Excel文件头
                    string fileHeader = BitConverter.ToString(fileBytes.Take(8).ToArray());
                    System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 文件头：{fileHeader}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] 文件流读取失败：{ex.Message}");
                    return Json(new { code = 1, msg = "文件读取失败，可能是文件缓存问题，请刷新页面后重试：" + ex.Message });
                }

                // 使用字节数组创建工作簿 - 每次都创建全新的工作簿对象
                try
                {
                    // 创建新的内存流，确保每次都是全新的
                    using (var workbookStream = new MemoryStream(fileBytes))
                    {
                        // 强制从头开始读取
                        workbookStream.Position = 0;

                        System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 开始创建工作簿，文件类型：{fileExt}");

                        if (fileExt == ".xls")
                        {
                            workbook = new HSSFWorkbook(workbookStream);
                        }
                        else
                        {
                            workbook = new XSSFWorkbook(workbookStream);
                        }

                        // 立即验证工作簿是否正确创建
                        if (workbook == null)
                        {
                            throw new Exception("工作簿创建失败，返回null");
                        }

                        int sheetCount = workbook.NumberOfSheets;
                        System.Diagnostics.Trace.TraceInformation($"[导入{importId}] Excel工作簿创建成功，工作表数量：{sheetCount}");
                    }

                    // 立即检查工作簿内容
                    try
                    {
                        ISheet debugSheet = workbook.GetSheetAt(0);
                        if (debugSheet != null)
                        {
                            int totalRows = debugSheet.LastRowNum + 1;
                            System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 工作表检查：总行数={totalRows}");

                            // 检查前几行的内容
                            for (int i = 0; i < Math.Min(3, totalRows); i++)
                            {
                                IRow row = debugSheet.GetRow(i);
                                if (row != null)
                                {
                                    var firstCell = row.GetCell(0);
                                    string cellValue;
                                    try
                                    {
                                        // 使用更安全的方法获取单元格值
                                        cellValue = GetCellValueSafely(firstCell);
                                        if (string.IsNullOrEmpty(cellValue))
                                        {
                                            cellValue = "EMPTY";
                                        }

                                        // 额外的调试信息
                                        if (firstCell != null)
                                        {
                                            try
                                            {
                                                var cellType = firstCell.CellType;
                                                System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 第{i}行第0列类型：{cellType}");
                                            }
                                            catch (Exception typeEx)
                                            {
                                                System.Diagnostics.Trace.TraceWarning($"[导入{importId}] 第{i}行第0列类型获取失败：{typeEx.Message}");
                                            }
                                        }
                                    }
                                    catch (Exception cellEx)
                                    {
                                        cellValue = $"SAFE_ERROR: {cellEx.Message}";
                                        System.Diagnostics.Trace.TraceError($"[导入{importId}] 安全方法也失败：{cellEx.Message}");
                                    }
                                    System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 第{i}行第0列内容：'{cellValue}'");
                                }
                                else
                                {
                                    System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 第{i}行为NULL");
                                }
                            }
                        }
                        else
                        {
                            System.Diagnostics.Trace.TraceError($"[导入{importId}] 工作表为NULL");
                        }
                    }
                    catch (Exception checkEx)
                    {
                        System.Diagnostics.Trace.TraceError($"[导入{importId}] 工作表内容检查失败：{checkEx.Message}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.TraceError($"[导入{importId}] Excel工作簿创建失败：{ex.Message}");
                    return Json(new { code = 1, msg = "Excel文件解析失败，请确认文件格式正确：" + ex.Message });
                }

                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    return Json(new { code = 1, msg = "Excel文件中没有找到工作表" });
                }

                // 检查表头
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    return Json(new { code = 1, msg = "Excel文件格式不正确，找不到表头" });
                }

                // 需要检查的必填字段在表头中的位置
                Dictionary<string, int> requiredFields = new Dictionary<string, int>
                {
                    { "日期", -1 },
                    { "类型", -1 },
                    { "客户名称", -1 },
                    { "签名类型:企业简称/APP/商标", -1 },
                    { "签名", -1 }
                };

                // 需要处理的图片字段在表头中的位置
                Dictionary<string, int> imageFields = new Dictionary<string, int>
                {
                    { "签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）", -1 },
                    { "身份证照片(反正面)", -1 },
                    { "身份证材料1", -1 },
                    { "身份证材料2", -1 },
                    { "身份证材料3", -1 },
                    { "身份证材料4", -1 },
                    { "身份证材料5", -1 }
                };

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    if (cell != null)
                    {
                        try
                        {
                            string header = GetCellValueSafely(cell);
                            if (!string.IsNullOrEmpty(header))
                            {
                                header = header.Trim().Replace("*", "");
                                if (requiredFields.ContainsKey(header))
                                {
                                    requiredFields[header] = i;
                                }
                                if (imageFields.ContainsKey(header))
                                {
                                    imageFields[header] = i;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceWarning($"读取表头第{i}列异常：{ex.Message}");
                            continue;
                        }
                    }
                }

                // 检查所有必填字段是否都有对应的列
                foreach (var field in requiredFields)
                {
                    if (field.Value == -1)
                    {
                        return Json(new { code = 1, msg = $"Excel文件缺少必填列：{field.Key}" });
                    }
                }

                // 读取数据并导入
                List<MobileSignReportModel> models = new List<MobileSignReportModel>();
                int adminId = Convert.ToInt32(Session["AdminID"]);
                int rowCount = sheet.LastRowNum;
                int successCount = 0;
                StringBuilder errorMsg = new StringBuilder();

                // 处理图片字段 - 分别为每个列提取图片
                List<string> signMaterialsImages = new List<string>();
                List<string> idCardPhotosImages = new List<string>();
                
                // 保存每行图片的映射关系
                Dictionary<int, string> rowSignMaterials = new Dictionary<int, string>();
                Dictionary<int, List<string>> rowIdCardPhotos = new Dictionary<int, List<string>>();

                // 首先遍历每行，检查是否含有DISPIMG格式，并处理图片字段
                for (int i = 1; i <= rowCount; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;

                    // 处理签名材料列
                    if (imageFields["签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）"] >= 0)
                    {
                        int colIndex = imageFields["签名材料：1.商标截图（商标网截图）2.APP（工信部ICP备案截图）3.企业简称（营业执照）"];
                        ICell cell = row.GetCell(colIndex);
                        if (cell != null)
                        {
                            string cellValue = GetCellValue(cell);
                            if (!string.IsNullOrEmpty(cellValue) && cellValue.Contains("DISPIMG"))
                            {
                                // 处理这行的签名材料图片
                                string processedValue = ProcessImageField(workbook, sheet, i, colIndex, "SignMaterials", cellValue);
                                if (!string.IsNullOrEmpty(processedValue) &&
                                    (processedValue.StartsWith("/") || processedValue.StartsWith("http")))
                                {
                                    rowSignMaterials[i] = processedValue;
                                }
                            }
                        }
                    }

                    // 处理身份证相关的所有列（身份证照片 + 5个身份证材料列）
                    List<string> idCardImagesList = new List<string>();

                    // 身份证照片(反正面)列
                    if (imageFields["身份证照片(反正面)"] >= 0)
                    {
                        int colIndex = imageFields["身份证照片(反正面)"];
                        ICell cell = row.GetCell(colIndex);
                        if (cell != null)
                        {
                            string cellValue = GetCellValue(cell);
                            if (!string.IsNullOrEmpty(cellValue) && cellValue.Contains("DISPIMG"))
                            {
                                // 处理这行的身份证照片图片
                                string processedValue = ProcessImageField(workbook, sheet, i, colIndex, "IdCardPhotos", cellValue);
                                if (!string.IsNullOrEmpty(processedValue) &&
                                    (processedValue.StartsWith("/") || processedValue.StartsWith("http")))
                                {
                                    idCardImagesList.Add(processedValue);
                                }
                            }
                        }
                    }

                    // 身份证材料1-5列
                    for (int materialIndex = 1; materialIndex <= 5; materialIndex++)
                    {
                        string fieldName = $"身份证材料{materialIndex}";
                        if (imageFields[fieldName] >= 0)
                        {
                            int colIndex = imageFields[fieldName];
                            ICell cell = row.GetCell(colIndex);
                            if (cell != null)
                            {
                                string cellValue = GetCellValue(cell);
                                if (!string.IsNullOrEmpty(cellValue) && cellValue.Contains("DISPIMG"))
                                {
                                    // 处理这行的身份证材料图片
                                    string processedValue = ProcessImageField(workbook, sheet, i, colIndex, $"IdCardMaterial{materialIndex}", cellValue);
                                    if (!string.IsNullOrEmpty(processedValue) &&
                                        (processedValue.StartsWith("/") || processedValue.StartsWith("http")))
                                    {
                                        idCardImagesList.Add(processedValue);
                                    }
                                }
                            }
                        }
                    }

                    // 如果有身份证相关图片，保存到映射中
                    if (idCardImagesList.Count > 0)
                    {
                        rowIdCardPhotos[i] = idCardImagesList;
                    }
                }

                for (int i = 1; i <= rowCount; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                    {
                        continue;
                    }

                    try
                    {
                        MobileSignReportModel model = new MobileSignReportModel();
                        model.ChannelId = channelId;
                        model.AdminId = UserId;
                        //model.UserId = UserId; // 设置当前登录用户的UserId
                        model.AddTime = DateTime.Now;
                        model.Status = 1;
                        model.SignStatus = 1;

                        // 读取必填字段
                        ICell dateCell = row.GetCell(requiredFields["日期"]);
                        if (dateCell == null)
                        {
                            errorMsg.AppendLine($"第{i+1}行：日期不能为空");
                            continue;
                        }
                        try
                        {
                            // 安全检查单元格类型和日期格式
                            bool isNumericDate = false;
                            try
                            {
                                isNumericDate = (dateCell.CellType == CellType.Numeric && DateUtil.IsCellDateFormatted(dateCell));
                            }
                            catch (Exception formatEx)
                            {
                                System.Diagnostics.Trace.TraceWarning($"第{i+1}行日期格式检查异常：{formatEx.Message}");
                                isNumericDate = false;
                            }

                            if (isNumericDate)
                            {
                                try
                                {
                                    // 完全安全的日期读取方式
                                    string cellValue = GetCellValueSafely(dateCell);
                                    DateTime parsedDate;
                                    if (!string.IsNullOrEmpty(cellValue) && DateTime.TryParse(cellValue, out parsedDate))
                                    {
                                        model.ReportDate = parsedDate;
                                    }
                                    else
                                    {
                                        // 如果安全方法也失败，直接使用默认值
                                        System.Diagnostics.Trace.TraceWarning($"第{i+1}行日期单元格无法解析，使用当前时间作为默认值");
                                        model.ReportDate = DateTime.Now; // 默认值
                                    }
                                }
                                catch (Exception dateEx)
                                {
                                    System.Diagnostics.Trace.TraceWarning($"日期单元格读取异常：{dateEx.Message}");
                                    model.ReportDate = DateTime.Now; // 设置默认值
                                }
                            }
                            else
                            {
                                // 非数值类型或非日期格式，使用安全方法获取
                                try
                                {
                                    string cellValue = GetCellValueSafely(dateCell);
                                    DateTime parsedDate;
                                    if (!string.IsNullOrEmpty(cellValue) && DateTime.TryParse(cellValue, out parsedDate))
                                    {
                                        model.ReportDate = parsedDate;
                                    }
                                    else
                                    {
                                        model.ReportDate = DateTime.Now; // 默认值
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Trace.TraceWarning($"日期解析异常：{ex.Message}");
                                    model.ReportDate = DateTime.Now; // 默认值
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.TraceError($"第{i+1}行日期处理异常：{ex.Message}");
                            errorMsg.AppendLine($"第{i+1}行：日期格式不正确或无法读取");
                            continue;
                        }

                        ICell typeCell = row.GetCell(requiredFields["类型"]);
                        if (typeCell == null || string.IsNullOrEmpty(GetCellValue(typeCell)))
                        {
                            errorMsg.AppendLine($"第{i+1}行：类型不能为空");
                            continue;
                        }
                        model.Type = GetCellValue(typeCell);

                        ICell accountNameCell = row.GetCell(requiredFields["客户名称"]);
                        if (accountNameCell == null || string.IsNullOrEmpty(GetCellValue(accountNameCell)))
                        {
                            errorMsg.AppendLine($"第{i+1}行：客户名称不能为空");
                            continue;
                        }
                        model.AccountName = GetCellValue(accountNameCell);

                        ICell signTypeCell = row.GetCell(requiredFields["签名类型:企业简称/APP/商标"]);
                        if (signTypeCell == null || string.IsNullOrEmpty(GetCellValue(signTypeCell)))
                        {
                            errorMsg.AppendLine($"第{i+1}行：签名类型不能为空");
                            continue;
                        }
                        model.SignType = GetCellValue(signTypeCell);

                        ICell signCell = row.GetCell(requiredFields["签名"]);
                        if (signCell == null || string.IsNullOrEmpty(GetCellValue(signCell)))
                        {
                            errorMsg.AppendLine($"第{i+1}行：签名不能为空");
                            continue;
                        }
                        model.Sign = GetCellValue(signCell);

                        // 读取其他字段
                        model.SignCompanyName = GetCellValueOrDefault(row, "签名企业名称", headerRow);
                        model.UnifiedSocialCreditCode = GetCellValueOrDefault(row, "统一社会信用代码", headerRow);
                        model.LegalPerson = GetCellValueOrDefault(row, "法人", headerRow);
                        model.LegalPersonName = GetCellValueOrDefault(row, "法人/经办人姓名", headerRow);
                        model.LegalPersonIdCard = GetCellValueOrDefault(row, "法人/经办人身份证号", headerRow);
                        model.LegalPersonPhone = GetCellValueOrDefault(row, "法人/经办人手机号（必须是本人实名验证，否则无法通过校验）", headerRow);
                        model.DrainageLink = GetCellValueOrDefault(row, "引流链接（1个子端口对应1个链接）", headerRow);
                        model.DrainageNumber1 = GetCellValueOrDefault(row, "引流号码1", headerRow);
                        model.DrainageNumber2 = GetCellValueOrDefault(row, "引流号码2", headerRow);
                        model.ExtNumber = GetCellValueOrDefault(row, "扩展号", headerRow);
                        model.Content = GetCellValueOrDefault(row, "内容", headerRow);

                        // 使用之前处理好的图片URL
                        if (rowSignMaterials.ContainsKey(i))
                        {
                            model.SignMaterials = rowSignMaterials[i];
                        }

                        if (rowIdCardPhotos.ContainsKey(i))
                        {
                            // 将多个身份证图片URL用分号连接
                            model.IdCardPhotos = string.Join(";", rowIdCardPhotos[i]);
                        }

                        // 检查是否已存在
                        System.Diagnostics.Trace.TraceInformation($"[导入{importId}] 第{i+1}行重复检查参数：");
                        System.Diagnostics.Trace.TraceInformation($"  签名: '{model.Sign}'");
                        System.Diagnostics.Trace.TraceInformation($"  用户ID: '{model.UserId}'");
                        System.Diagnostics.Trace.TraceInformation($"  通道ID: '{model.ChannelId}'");
                        System.Diagnostics.Trace.TraceInformation($"  扩展号: '{model.ExtNumber ?? "NULL"}'");
                        System.Diagnostics.Trace.TraceInformation($"  内容: '{model.Content ?? "NULL"}' (长度: {model.Content?.Length ?? 0})");

                        if (_mobileSignReportBll.CheckMobileSignReport(model.Sign, model.UserId.ToString(), model.ChannelId.ToString(), model.ExtNumber, model.Content))
                        {
                            System.Diagnostics.Trace.TraceWarning($"[导入{importId}] 第{i+1}行：发现重复记录！");
                            errorMsg.AppendLine($"第{i+1}行：记录已存在，签名 {model.Sign}，通道 {model.ChannelId}");
                            continue;
                        }

                        // 添加到数据库
                        if (_mobileSignReportBll.AddMobileSignReport(model))
                        {
                            successCount++;
                        }
                        else
                        {
                            errorMsg.AppendLine($"第{i+1}行：导入失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorMsg.AppendLine($"第{i+1}行：{ex.Message}");
                    }
                }

                if (successCount > 0)
                {
                    string msg = $"成功导入{successCount}条记录";
                    if (errorMsg.Length > 0)
                    {
                        msg += "，但有部分记录导入失败：" + errorMsg.ToString();
                    }
                    return Json(new { code = 0, msg = msg });
                }
                else
                {
                    return Json(new { code = 1, msg = "导入失败：" + errorMsg.ToString() });
                }
            }
            catch (Exception ex)
            {
                return Json(new { code = 1, msg = "导入失败：" + ex.Message });
            }
        }
    }
} 