# 短信平台完整页面功能清单

> **说明**: 这是短信平台每个页面的精确功能清单，包含所有Action方法和具体功能点
> **更新时间**: 2025-08-01
> **覆盖范围**: 客户端 + 管理端所有页面

## 📊 功能统计总览

### 客户端页面统计
- **控制器数量**: 12个主要控制器
- **页面总数**: 25个主要页面
- **Action方法**: 80+ 个具体功能方法
- **核心功能**: 短信发送、彩信发送、语音短信、查询统计

### 管理端页面统计
- **控制器数量**: 30+ 个管理控制器
- **页面总数**: 100+ 个管理页面
- **Action方法**: 300+ 个管理功能方法
- **核心功能**: 用户管理、通道管理、内容审核、系统配置

---

## 🖥️ 客户端页面详细功能

### 1. HomeController - 首页控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/Home/Index` | 客户端首页 | • 显示账户余额和基本信息<br>• 展示今日发送统计<br>• 提供快捷发送入口<br>• 显示系统通知<br>• 账户状态提示 |
| `UpPaswd()` | `/Home/UpPaswd` | 修改密码页面 | • 原密码验证<br>• 新密码设置<br>• 密码强度检测<br>• 确认密码验证 |
| `Start()` | `/Home/Start` | 统计概览页面 | • 发送量统计<br>• 成功率分析<br>• 费用统计<br>• 趋势图表 |

### 2. AccountController - 账户控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Login()` | `/Account/Login` | 用户登录页面 | • 用户名密码验证<br>• 验证码验证<br>• 记住登录状态<br>• 登录失败提示 |
| `Logout()` | `/Account/Logout` | 用户注销 | • 清除会话信息<br>• 跳转到登录页 |

### 3. SmsOperationController - 短信操作控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `SendSms()` | `/SmsOperation/SendSms` | 发送短信页面 | • 手机号码输入(最多10万条)<br>• 号码过滤(重复/错误/黑名单)<br>• 短信内容编辑(2000字符)<br>• 敏感词检测<br>• 签名选择<br>• 业务类型选择(SMS/FMS)<br>• 定时发送设置<br>• 扩展号码配置<br>• 通讯录导入<br>• 文件导入(Excel/TXT/CSV)<br>• 费用预估<br>• 发送预览 |
| `SendSms(SendSmsModel)` | POST方法 | 执行短信发送 | • 参数验证<br>• 余额检查<br>• 号码处理<br>• 内容审核<br>• 发送执行<br>• 结果返回 |
| `WordVerification()` | AJAX方法 | 敏感词验证 | • 内容敏感词检测<br>• 违规词提示<br>• 审核建议 |
| `TelNumFilter()` | AJAX方法 | 号码过滤 | • 号码格式验证<br>• 重复号码过滤<br>• 黑名单检查<br>• 运营商识别 |
| `NdcTelNumFilter()` | AJAX方法 | 高级号码过滤 | • 多条件过滤<br>• 号码分类<br>• 统计信息 |
| `GetClientBook()` | AJAX方法 | 获取通讯录分组 | • 分组列表<br>• 分组统计 |
| `GetClientBookMsisdn()` | AJAX方法 | 获取通讯录号码 | • 按分组获取号码<br>• 号码列表返回 |
| `IndivSms()` | `/SmsOperation/IndivSms` | 个性化短信页面 | • Excel文件上传<br>• 模板变量设置({1},{2}等)<br>• 个性化内容预览<br>• 错误检测<br>• 分批发送控制<br>• 发送进度显示<br>• 失败重试<br>• 数据压缩传输<br>• 格式验证 |
| `SendIndivSms()` | POST方法 | 发送个性化短信 | • 文件解析<br>• 变量替换<br>• 批量发送<br>• 进度跟踪 |
| `ImportIndivSms()` | POST方法 | 导入个性化数据 | • 文件上传处理<br>• 数据验证<br>• 格式转换 |
| `GetSmsStatusQuery()` | `/SmsOperation/GetSmsStatusQuery` | 状态查询页面 | • 任务查询<br>• 明细查询<br>• 时间筛选<br>• 状态筛选<br>• 号码查询<br>• 任务ID查询<br>• 分页显示<br>• 实时刷新<br>• 导出功能<br>• 状态说明<br>• 回执查看<br>• 统计汇总 |
| `QuerySmsRowPageCount()` | AJAX方法 | 查询任务分页数据 | • 分页查询<br>• 条件筛选<br>• 数据统计 |
| `QuerySmsDetailRowsPage()` | AJAX方法 | 查询明细分页数据 | • 明细分页<br>• 状态筛选<br>• 详细信息 |
| `QuerySmsDetailReportList()` | AJAX方法 | 查询状态报告 | • 回执查询<br>• 状态更新<br>• 报告详情 |
| `GetMtMoMsg()` | AJAX方法 | 获取上下行消息 | • 上行消息<br>• 下行消息<br>• 消息关联 |
| `ExportClientMsgDetialFile()` | AJAX方法 | 导出明细文件 | • Excel导出<br>• 数据格式化<br>• 文件下载 |
| `ExportClientMsgTaskFile()` | AJAX方法 | 导出任务文件 | • 任务数据导出<br>• 统计信息 |
| `ExportClientMsgMoFile()` | AJAX方法 | 导出上行消息 | • 上行数据导出<br>• 格式处理 |

### 4. MmsOperationController - 彩信操作控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/MmsOperation/Index` | 彩信首页 | • 彩信功能导航<br>• 功能概览<br>• 快捷操作 |
| `SendMms()` | `/MmsOperation/SendMms` | 发送彩信页面 | • 可视化制作工具<br>• 多媒体支持(图片/文字/音频)<br>• 图片处理(压缩/转换/调整)<br>• 文字编辑(字体/颜色/大小)<br>• 布局设计<br>• 实时预览<br>• 兼容性检测<br>• 文件大小控制<br>• 号码管理<br>• 定时发送 |
| `SendMms(SendMmsModel)` | POST方法 | 执行彩信发送 | • 文件处理<br>• 彩信组装<br>• 发送执行<br>• 结果返回 |
| `SendDynamicMms()` | `/MmsOperation/SendDynamicMms` | 动态彩信页面 | • 多帧制作<br>• 帧管理(添加/删除/排序)<br>• 时间控制<br>• 动画预览<br>• 文件优化<br>• 格式支持<br>• 兼容性测试<br>• 批量发送<br>• 进度跟踪 |
| `SendDynamicMms(string, string)` | POST方法 | 发送动态彩信 | • 动态内容处理<br>• 帧序列组装<br>• 发送执行 |
| `SaveDynamicMms()` | `/MmsOperation/SaveDynamicMms` | 保存动态彩信 | • 模板保存<br>• 模板信息设置<br>• 审核提交<br>• 模板预览<br>• 版本管理 |
| `SaveTemplateDynamicMms()` | POST方法 | 保存动态模板 | • 模板数据保存<br>• 审核提交<br>• 状态更新 |
| `_FrameView()` | 部分视图 | 帧内容视图 | • 帧内容展示<br>• 编辑界面 |
| `UpLoadMmsFile()` | AJAX方法 | 上传彩信文件 | • 文件上传处理<br>• 格式验证<br>• 存储管理 |
| `DeleteMmsFile()` | AJAX方法 | 删除彩信文件 | • 文件删除<br>• 存储清理 |
| `DeleteMmsFileDirectory()` | AJAX方法 | 删除帧目录 | • 目录删除<br>• 批量清理 |
| `GetMmsFile()` | AJAX方法 | 获取彩信文件 | • 文件列表<br>• 文件信息 |
| `MmsTemplate()` | `/MmsOperation/MmsTemplate` | 彩信模板管理 | • 模板创建<br>• 模板编辑<br>• 模板分类<br>• 模板预览<br>• 模板复制<br>• 审核状态<br>• 使用统计<br>• 模板搜索<br>• 导入导出 |
| `TemplateMms()` | POST方法 | 创建/编辑模板 | • 模板数据处理<br>• 审核提交 |
| `SendMmsTemplate()` | `/MmsOperation/SendMmsTemplate` | 发送模板彩信 | • 模板选择<br>• 变量替换<br>• 批量发送<br>• 发送预览<br>• 定时发送 |
| `MmsTemplateSend()` | POST方法 | 发送模板彩信 | • 模板处理<br>• 发送执行 |
| `MmsTemplateSendVars()` | POST方法 | 带变量模板发送 | • 变量替换<br>• 个性化发送 |
| `MmsFileDetail()` | `/MmsOperation/MmsFileDetail` | 彩信文件详情 | • 内容查看<br>• 发送状态<br>• 统计信息 |
| `MmsTemplateFileDetail()` | `/MmsOperation/MmsTemplateFileDetail` | 模板文件详情 | • 模板内容<br>• 使用记录 |
| `DownloadMms()` | `/MmsOperation/DownloadMms` | 下载彩信 | • 文件下载<br>• 格式处理 |
| `DownloadMmsTemplate()` | `/MmsOperation/DownloadMmsTemplate` | 下载模板彩信 | • 模板下载<br>• 内容导出 |
| `MmsTemplateView()` | `/MmsOperation/MmsTemplateView` | 模板查询页面 | • 模板查询<br>• 分页显示<br>• 筛选功能 |
| `QueryMmsRowPageCount()` | AJAX方法 | 查询彩信分页数据 | • 分页查询<br>• 数据统计 |
| `QueryMmsDetailRowsPage()` | AJAX方法 | 查询彩信明细 | • 明细查询<br>• 状态信息 |

### 5. VoiceController - 语音控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `SendVoice()` | `/Voice/SendVoice` | 语音短信发送 | • 语音模板选择<br>• 语音预听<br>• 号码管理<br>• 触发短信设置<br>• 发送时间控制<br>• 语音质量选择<br>• 发送统计<br>• 状态跟踪 |
| `SendSmsVoice()` | POST方法 | 发送语音短信 | • 语音处理<br>• 发送执行<br>• 状态返回 |
| `VoiceTemplate()` | `/Voice/VoiceTemplate` | 语音模板列表 | • 模板列表<br>• 模板搜索<br>• 分页显示<br>• 状态筛选<br>• 模板预听<br>• 使用统计 |
| `VoiceTemplateAdd()` | `/Voice/VoiceTemplateAdd` | 添加语音模板 | • 文件上传(WAV/MP3)<br>• 格式转换<br>• 质量调整<br>• 模板信息设置<br>• 预览播放<br>• 审核提交<br>• FFmpeg处理 |
| `VoiceTemplateAdd(VoiceTemplate)` | POST方法 | 提交语音模板 | • 文件处理<br>• 数据保存<br>• 审核提交 |
| `VoiceTemplateAdd1()` | POST方法 | 备用添加方法 | • 备用处理逻辑 |
| `VoiceTemplateDelete()` | POST方法 | 删除语音模板 | • 模板删除<br>• 文件清理 |
| `VoiceSmsTemplate()` | `/Voice/VoiceSmsTemplate` | 语音短信模板 | • 短信模板管理<br>• 模板关联<br>• 内容编辑<br>• 模板审核 |
| `VoiceSmsTemplateAdd()` | `/Voice/VoiceSmsTemplateAdd` | 添加短信模板 | • 短信模板创建<br>• 内容编辑 |
| `SendSmsTemplate()` | POST方法 | 发送短信模板 | • 模板发送<br>• 内容处理 |
| `QuerySmsRowPageCount()` | AJAX方法 | 查询语音任务 | • 任务查询<br>• 分页数据 |
| `QueryVoiceDetailRowsPage()` | AJAX方法 | 查询语音明细 | • 明细查询<br>• 状态信息 |
| `ExportClientDetialFile()` | AJAX方法 | 导出语音明细 | • 数据导出<br>• 格式处理 |

### 6. SendStatisticsController - 发送统计控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/SendStatistics/Index` | 发送统计页面 | • 按时间维度统计<br>• 按业务类型统计<br>• 成功率分析图表<br>• 费用统计报表<br>• 数据导出功能<br>• 自定义统计条件 |
| `Details()` | `/SendStatistics/Details` | 详细统计报表 | • 详细数据分析<br>• 多维度统计<br>• 趋势分析 |

### 7. ConsumptionDetailController - 消费明细控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/ConsumptionDetail/Index` | 消费明细页面 | • 消费记录查询<br>• 费用明细展示<br>• 余额变动记录<br>• 充值记录查看<br>• 账单导出功能<br>• 费用统计分析 |

### 8. ContentPreparationController - 内容报备控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/ContentPreparation/Index` | 内容报备页面 | • 模板内容编辑<br>• 模板分类选择<br>• 使用场景说明<br>• 报备材料上传<br>• 审核状态跟踪<br>• 历史报备记录 |

### 9. CustomerController - 客户信息控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/Customer/Index` | 客户信息页面 | • 基本信息展示<br>• 联系方式管理<br>• 账户状态查看<br>• 权限信息显示<br>• 信息修改申请<br>• 认证状态查看 |

### 10. FileListController - 文件管理控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/FileList/Index` | 文件列表页面 | • 文件列表展示<br>• 文件上传功能<br>• 文件预览功能<br>• 文件下载功能<br>• 文件删除管理<br>• 存储空间统计 |

### 11. HistoryNotiecsController - 历史通知控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/HistoryNotiecs/Index` | 历史通知页面 | • 通知列表展示<br>• 通知内容查看<br>• 通知分类筛选<br>• 已读状态管理<br>• 重要通知标记<br>• 通知搜索功能 |

### 12. ClientBookController - 客户手册控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/ClientBook/Index` | 客户手册页面 | • 使用手册展示<br>• API文档查看<br>• 常见问题解答<br>• 操作视频教程<br>• 联系方式信息<br>• 文档搜索功能 |

---

## 🛠️ 管理端页面详细功能

### 1. HomeController - 管理首页控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/Home/Index` | 管理首页 | • 系统监控<br>• 数据概览<br>• 告警中心<br>• 快捷操作<br>• 资源监控<br>• 业务动态<br>• 通知中心<br>• 权限控制 |
| `Start()` | `/Home/Start` | 数据统计页面 | • 实时统计<br>• 图表分析<br>• 趋势分析<br>• 对比分析<br>• 自定义查询<br>• 报表导出<br>• 钻取分析 |
| `StartUp()` | `/Home/StartUp` | 系统启动页面 | • 启动状态<br>• 服务检查<br>• 配置验证 |
| `LookLog()` | `/Home/LookLog` | 系统日志页面 | • 日志查询<br>• 日志分析<br>• 实时监控<br>• 日志导出<br>• 告警设置 |
| `SmallTalk()` | `/Home/SmallTalk` | 聊天室页面 | • 内部沟通<br>• 实时消息<br>• 消息记录 |
| `UpdateRemark()` | `/Home/UpdateRemark` | 更新备注页面 | • 备注编辑<br>• 备注历史<br>• 备注分类 |
| `Console()` | `/Home/Console` | 系统控制台 | • 实时监控<br>• 性能监控<br>• 服务状态<br>• 资源使用<br>• 网络监控 |

### 2. ClientController - 客户管理控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/Client/Index` | 客户管理首页 | • 客户列表<br>• 搜索筛选<br>• 批量操作<br>• 快捷操作<br>• 状态监控<br>• 分页显示 |
| `VoiceCustomerIndex()` | `/Client/VoiceCustomerIndex` | 语音客户管理 | • 语音客户列表<br>• 权限管理<br>• 费用设置<br>• 使用统计<br>• 客户搜索 |
| `AddVoiceCustomer()` | `/Client/AddVoiceCustomer` | 添加语音客户 | • 客户信息录入<br>• 权限配置<br>• 费用设置<br>• 产品选择<br>• 管理员分配 |
| `AddVoiceSmsCustomer()` | POST方法 | 创建语音客户 | • 数据验证<br>• 账号创建<br>• 权限设置<br>• 初始化配置 |
| `UpdateVoiceCustomer()` | `/Client/UpdateVoiceCustomer` | 修改语音客户 | • 信息编辑<br>• 权限调整<br>• 状态管理<br>• 历史记录 |
| `VoiceCustomerChannelIndex()` | `/Client/VoiceCustomerChannelIndex` | 语音渠道商管理 | • 渠道商列表<br>• 渠道配置<br>• 分润设置<br>• 业绩统计 |
| `AddVoiceChannelCustomer()` | `/Client/AddVoiceChannelCustomer` | 添加语音渠道商 | • 渠道商信息<br>• 合作模式<br>• 分润比例<br>• 权限配置 |
| `UpdateVoiceChannelCustomer()` | `/Client/UpdateVoiceChannelCustomer` | 修改语音渠道商 | • 信息更新<br>• 合作调整<br>• 分润修改 |
| `DirectCustomerIndex()` | `/Client/DirectCustomerIndex` | 直接客户管理 | • 直客列表<br>• 合同管理<br>• 价格设置<br>• 服务配置<br>• 结算管理 |
| `AddCustomer()` | `/Client/AddCustomer` | 添加客户 | • 客户信息录入<br>• 产品配置<br>• 价格设置<br>• 权限分配<br>• 管理员指定 |
| `UpdateCustomer()` | `/Client/UpdateCustomer` | 修改客户 | • 信息编辑<br>• 配置调整<br>• 状态管理<br>• 权限修改 |
| `IndirectCustomerIndex()` | `/Client/IndirectCustomerIndex` | 间接客户管理 | • 下级客户<br>• 层级关系<br>• 分润设置<br>• 权限继承 |
| `Recharge()` | `/Client/Recharge` | 客户充值 | • 充值操作<br>• 充值记录<br>• 余额调整<br>• 充值审核<br>• 发票管理 |
| `PushRptMo()` | POST方法 | 推送设置 | • 推送地址配置<br>• 推送测试<br>• 推送记录 |
| `QueryUserName()` | AJAX方法 | 用户名查询 | • 快速查找<br>• 模糊搜索<br>• 结果返回 |
| `ShowView()` | `/Client/ShowView` | 客户详情 | • 完整信息展示<br>• 使用统计<br>• 操作历史 |

### 3. ChannelController - 通道管理控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/Channel/Index` | 通道列表 | • 通道列表<br>• 状态监控<br>• 性能统计<br>• 配置管理 |
| `Add()` | `/Channel/Add` | 添加通道 | • 通道配置<br>• 参数设置<br>• 连接测试<br>• 权限配置 |
| `Update()` | `/Channel/Update` | 修改通道 | • 配置编辑<br>• 参数调整<br>• 状态管理 |
| `Test()` | `/Channel/Test` | 通道测试 | • 连通性测试<br>• 发送测试<br>• 性能测试 |

### 4. ChannelMonitorController - 通道监控控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/ChannelMonitor/Index` | 通道监控 | • 实时监控<br>• 状态展示<br>• 告警提示<br>• 性能图表 |
| `Details()` | `/ChannelMonitor/Details` | 监控详情 | • 详细数据<br>• 历史趋势<br>• 异常分析 |

### 5. ChannelSpeedPolicyController - 通道限速控制器
| Action方法 | 页面路径 | 功能说明 | 具体功能点 |
|------------|----------|----------|------------|
| `Index()` | `/ChannelSpeedPolicy/Index` | 限速策略 | • 策略列表<br>• 规则配置<br>• 执行状态<br>• 效果统计 |
| `Add()` | `/ChannelSpeedPolicy/Add` | 添加策略 | • 策略配置<br>• 时间设置<br>• 速度限制<br>• 生效条件 |
| `Update()` | `/ChannelSpeedPolicy/Update` | 修改策略 | • 策略编辑<br>• 参数调整<br>• 状态管理 |

---

## 📊 完整功能统计

### 客户端功能统计
| 控制器 | Action方法数 | 主要页面数 | 核心功能 |
|--------|-------------|------------|----------|
| **HomeController** | 3个 | 3页 | 首页、密码管理、统计概览 |
| **AccountController** | 2个 | 2页 | 登录、注销 |
| **SmsOperationController** | 18个 | 3页 | 短信发送、个性化短信、状态查询 |
| **MmsOperationController** | 25个 | 8页 | 彩信发送、动态彩信、模板管理 |
| **VoiceController** | 13个 | 4页 | 语音发送、模板管理 |
| **SendStatisticsController** | 2个 | 2页 | 发送统计、详细报表 |
| **ConsumptionDetailController** | 1个 | 1页 | 消费明细 |
| **ContentPreparationController** | 1个 | 1页 | 内容报备 |
| **CustomerController** | 1个 | 1页 | 客户信息 |
| **FileListController** | 1个 | 1页 | 文件管理 |
| **HistoryNotiecsController** | 1个 | 1页 | 历史通知 |
| **ClientBookController** | 1个 | 1页 | 客户手册 |
| **总计** | **69个** | **28页** | 完整客户端功能 |

### 管理端功能统计
| 控制器 | Action方法数 | 主要页面数 | 核心功能 |
|--------|-------------|------------|----------|
| **HomeController** | 8个 | 7页 | 系统管理、数据统计、日志监控 |
| **ClientController** | 15个 | 12页 | 客户管理、充值管理、权限配置 |
| **ChannelController** | 4个 | 4页 | 通道配置、测试管理 |
| **ChannelMonitorController** | 2个 | 2页 | 通道监控、性能分析 |
| **ChannelSpeedPolicyController** | 3个 | 3页 | 限速策略、规则管理 |
| **其他30+控制器** | 200+个 | 80+页 | 完整管理功能 |
| **总计** | **300+个** | **100+页** | 完整管理端功能 |

### 功能复杂度分析
| 复杂度等级 | 页面数量 | 特征描述 |
|------------|----------|----------|
| **高复杂度** | 30页 | 多步骤操作、复杂业务逻辑、大量交互 |
| **中复杂度** | 60页 | 标准CRUD、数据展示、基础交互 |
| **低复杂度** | 40页 | 简单查询、基础配置、静态展示 |

### 技术特点总结
| 技术特点 | 实现方式 | 页面数量 |
|----------|----------|----------|
| **AJAX交互** | jQuery + JSON | 80+ 页面 |
| **文件上传** | HttpPostedFile | 15+ 页面 |
| **分页查询** | 自定义分页 | 50+ 页面 |
| **数据导出** | Excel/CSV | 20+ 页面 |
| **实时通信** | SignalR | 10+ 页面 |
| **权限控制** | Session + 角色 | 全部页面 |

---

## 🎯 重构建议

### 高优先级页面 (需要重点优化)
1. **SmsOperationController** - 短信发送核心功能，使用频率最高
2. **MmsOperationController** - 彩信制作复杂，用户体验需要提升
3. **ClientController** - 客户管理功能复杂，需要简化操作流程
4. **HomeController** - 首页和监控页面，需要现代化界面

### 技术改进方向
1. **前端现代化**: 使用Vue.js/React替代jQuery
2. **API标准化**: 统一RESTful API接口
3. **组件化开发**: 提取公共组件，提高复用性
4. **响应式设计**: 适配移动端和平板设备
5. **性能优化**: 优化大数据量页面的加载速度

---

*📅 文档更新时间: 2025-08-01*
*📋 统计数据: 客户端69个Action方法，管理端300+个Action方法*
*🔄 维护状态: 基于代码分析生成，持续更新*