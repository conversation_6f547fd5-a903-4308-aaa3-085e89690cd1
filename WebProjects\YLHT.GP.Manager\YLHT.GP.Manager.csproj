﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.7\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.7\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4E72DD44-7308-4156-81DF-3DB8C574CC0B}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YLHT.GP.Manager</RootNamespace>
    <AssemblyName>YLHT.GP.Manager</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress>true</Use64BitIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <WebGreaseLibPath>..\..\packages\WebGrease.1.5.2\lib</WebGreaseLibPath>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.4.1.9004, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Aspose.Cells, Version=18.12.0.0, Culture=neutral, PublicKeyToken=716fcc553a201e56, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Aspose.Cells.18.12.0\lib\net40\Aspose.Cells.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Portable.BouncyCastle.1.8.6\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=*******, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EPPlus.5.6.3\lib\net45\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=*********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SharpZipLib.1.2.0\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.0.7\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.2.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.2.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.2.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.Web.2.2.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.WindowsServer.2.2.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Client, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.SignalR.Client.2.4.1\lib\net45\Microsoft.AspNet.SignalR.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.SignalR.Core.2.4.1\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.SignalR.SystemWeb.2.4.1\lib\net45\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=1.0.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.7\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=1.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.IO.RecyclableMemoryStream.1.4.1\lib\net45\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.4.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Cors.4.0.1\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Host.SystemWeb.2.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Security.2.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.11.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.5.1\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.5.1\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.5.1\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.5.1\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=2.112.1.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="StackExchange.Redis, Version=1.2.0.0, Culture=neutral, PublicKeyToken=null" />
    <Reference Include="Swashbuckle.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Messaging" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.6\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.6\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.6\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.6\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.6\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Mvc.5.2.4\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Razor.3.2.4\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebGrease, Version=1.5.2.14234, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\AmountController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\BlackMsisdnController.cs" />
    <Compile Include="Controllers\CategoryController.cs" />
    <Compile Include="Controllers\ChannelController.cs" />
    <Compile Include="Controllers\ChannelMonitorController.cs" />
    <Compile Include="Controllers\ChannelSpeedPolicyController.cs" />
    <Compile Include="Controllers\ChannelTextTemplateController.cs" />
    <Compile Include="Controllers\ClientController.cs" />
    <Compile Include="Controllers\CompanyController.cs" />
    <Compile Include="Controllers\ContentPreparationLinkController.cs" />
    <Compile Include="Controllers\ContentPreparationController.cs" />
    <Compile Include="Controllers\FileListController.cs" />
    <Compile Include="Controllers\GroupController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\IPWhiteController.cs" />
    <Compile Include="Controllers\KeyWordRoutingController.cs" />
    <Compile Include="Controllers\LableController.cs" />
    <Compile Include="Controllers\MmsAuditController.cs" />
    <Compile Include="Controllers\MmsController.cs" />
    <Compile Include="Controllers\MmsPlanController.cs" />
    <Compile Include="Controllers\MobileSecitonController.cs" />
    <Compile Include="Controllers\MobileSignReportController.cs" />
    <Compile Include="Controllers\MsgAuditController.cs" />
    <Compile Include="Controllers\MsgDetailsController.cs" />
    <Compile Include="Controllers\MsgMoController.cs" />
    <Compile Include="Controllers\MsgPlanController.cs" />
    <Compile Include="Controllers\MsgTaskController.cs" />
    <Compile Include="Controllers\MsisdnBackController.cs" />
    <Compile Include="Controllers\MsisdnConvertController.cs" />
    <Compile Include="Controllers\NoticesController.cs" />
    <Compile Include="Controllers\OperMobileSectionController.cs" />
    <Compile Include="Controllers\PermissionController.cs" />
    <Compile Include="Controllers\ProductController.cs" />
    <Compile Include="Controllers\RealTimeStatisticsController.cs" />
    <Compile Include="Controllers\RedMsisdnController.cs" />
    <Compile Include="Controllers\RestSendRuleController.cs" />
    <Compile Include="Controllers\SalesManController.cs" />
    <Compile Include="Controllers\SendStatisticsController.cs" />
    <Compile Include="Controllers\SensitiveWordController.cs" />
    <Compile Include="Controllers\SignRecordsController.cs" />
    <Compile Include="Controllers\SignReportController.cs" />
    <Compile Include="Controllers\StaffController.cs" />
    <Compile Include="Controllers\SystemPropertyController.cs" />
    <Compile Include="Controllers\TextSignExtNumberController.cs" />
    <Compile Include="Controllers\TextTemplatePinciController.cs" />
    <Compile Include="Controllers\VoiceDetailsController.cs" />
    <Compile Include="Controllers\VoiceTemplateController.cs" />
    <Compile Include="Controllers\WhiteMsisdnController.cs" />
    <Compile Include="Filters\MenuAuthorizeAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Filters\CustomerValidateAntiForgeryTokenAttribute.cs" />
    <Compile Include="Methods\MvcHelper.cs" />
    <Compile Include="Models\QueueModel.cs" />
    <Compile Include="Models\StartModel1.cs" />
    <Compile Include="Models\StartModel.cs" />
    <Compile Include="Models\TestSendSms.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resource.Designer.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Services\ChannelSpeedPolicyService.cs" />
    <Compile Include="signalr\NotificationHub.cs" />
    <Compile Include="Web References\BillingService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\DataService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\InternalService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="404.html" />
    <Content Include="bin\Antlr3.Runtime.dll" />
    <Content Include="bin\Microsoft.ApplicationInsights.dll" />
    <Content Include="bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll" />
    <Content Include="bin\Microsoft.Web.Infrastructure.dll" />
    <Content Include="bin\System.Web.Helpers.dll" />
    <Content Include="bin\System.Web.Mvc.dll" />
    <Content Include="bin\System.Web.Optimization.dll" />
    <Content Include="bin\System.Web.Razor.dll" />
    <Content Include="bin\System.Web.WebPages.Deployment.dll" />
    <Content Include="bin\System.Web.WebPages.dll" />
    <Content Include="bin\System.Web.WebPages.Razor.dll" />
    <Content Include="bin\WebGrease.dll" />
    <Content Include="bin\YLHT.GP.Common.dll" />
    <Content Include="bin\YLHT.GP.Manager.pdb" />
    <Content Include="bin\YLHT.GP.Models.dll" />
    <Content Include="bin\YLHT_GP_Business.dll" />
    <Content Include="Content\bootstrap-select.min.css" />
    <Content Include="Content\chosen.min.css" />
    <Content Include="Content\H-ui.admin.css" />
    <Content Include="Content\H-ui.css" />
    <Content Include="Content\H-ui.login.css" />
    <Content Include="Content\H-ui.min.css" />
    <Content Include="Content\iconfont.css" />
    <Content Include="Content\img\close.png" />
    <Content Include="Content\img\filter.png" />
    <Content Include="Content\img\nc.png" />
    <Content Include="Content\img\open.png" />
    <Content Include="Content\img\yc.png" />
    <Content Include="Content\jquery.searchableSelect.css" />
    <Content Include="Content\laypage\1.2\laypage.js" />
    <Content Include="Content\laypage\1.2\skin\laypage.css" />
    <Content Include="Content\layui.css" />
    <Content Include="Content\layui\css\layui.css" />
    <Content Include="Content\layui\css\layui.mobile.css" />
    <Content Include="Content\layui\css\modules\code.css" />
    <Content Include="Content\layui\css\modules\laydate\default\laydate.css" />
    <Content Include="Content\layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="Content\layui\css\modules\layer\default\icon.png" />
    <Content Include="Content\layui\css\modules\layer\default\layer.css" />
    <Content Include="Content\layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="Content\layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="Content\layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="Content\layui\font\iconfont.svg" />
    <Content Include="Content\layui\images\face\0.gif" />
    <Content Include="Content\layui\images\face\1.gif" />
    <Content Include="Content\layui\images\face\10.gif" />
    <Content Include="Content\layui\images\face\11.gif" />
    <Content Include="Content\layui\images\face\12.gif" />
    <Content Include="Content\layui\images\face\13.gif" />
    <Content Include="Content\layui\images\face\14.gif" />
    <Content Include="Content\layui\images\face\15.gif" />
    <Content Include="Content\layui\images\face\16.gif" />
    <Content Include="Content\layui\images\face\17.gif" />
    <Content Include="Content\layui\images\face\18.gif" />
    <Content Include="Content\layui\images\face\19.gif" />
    <Content Include="Content\layui\images\face\2.gif" />
    <Content Include="Content\layui\images\face\20.gif" />
    <Content Include="Content\layui\images\face\21.gif" />
    <Content Include="Content\layui\images\face\22.gif" />
    <Content Include="Content\layui\images\face\23.gif" />
    <Content Include="Content\layui\images\face\24.gif" />
    <Content Include="Content\layui\images\face\25.gif" />
    <Content Include="Content\layui\images\face\26.gif" />
    <Content Include="Content\layui\images\face\27.gif" />
    <Content Include="Content\layui\images\face\28.gif" />
    <Content Include="Content\layui\images\face\29.gif" />
    <Content Include="Content\layui\images\face\3.gif" />
    <Content Include="Content\layui\images\face\30.gif" />
    <Content Include="Content\layui\images\face\31.gif" />
    <Content Include="Content\layui\images\face\32.gif" />
    <Content Include="Content\layui\images\face\33.gif" />
    <Content Include="Content\layui\images\face\34.gif" />
    <Content Include="Content\layui\images\face\35.gif" />
    <Content Include="Content\layui\images\face\36.gif" />
    <Content Include="Content\layui\images\face\37.gif" />
    <Content Include="Content\layui\images\face\38.gif" />
    <Content Include="Content\layui\images\face\39.gif" />
    <Content Include="Content\layui\images\face\4.gif" />
    <Content Include="Content\layui\images\face\40.gif" />
    <Content Include="Content\layui\images\face\41.gif" />
    <Content Include="Content\layui\images\face\42.gif" />
    <Content Include="Content\layui\images\face\43.gif" />
    <Content Include="Content\layui\images\face\44.gif" />
    <Content Include="Content\layui\images\face\45.gif" />
    <Content Include="Content\layui\images\face\46.gif" />
    <Content Include="Content\layui\images\face\47.gif" />
    <Content Include="Content\layui\images\face\48.gif" />
    <Content Include="Content\layui\images\face\49.gif" />
    <Content Include="Content\layui\images\face\5.gif" />
    <Content Include="Content\layui\images\face\50.gif" />
    <Content Include="Content\layui\images\face\51.gif" />
    <Content Include="Content\layui\images\face\52.gif" />
    <Content Include="Content\layui\images\face\53.gif" />
    <Content Include="Content\layui\images\face\54.gif" />
    <Content Include="Content\layui\images\face\55.gif" />
    <Content Include="Content\layui\images\face\56.gif" />
    <Content Include="Content\layui\images\face\57.gif" />
    <Content Include="Content\layui\images\face\58.gif" />
    <Content Include="Content\layui\images\face\59.gif" />
    <Content Include="Content\layui\images\face\6.gif" />
    <Content Include="Content\layui\images\face\60.gif" />
    <Content Include="Content\layui\images\face\61.gif" />
    <Content Include="Content\layui\images\face\62.gif" />
    <Content Include="Content\layui\images\face\63.gif" />
    <Content Include="Content\layui\images\face\64.gif" />
    <Content Include="Content\layui\images\face\65.gif" />
    <Content Include="Content\layui\images\face\66.gif" />
    <Content Include="Content\layui\images\face\67.gif" />
    <Content Include="Content\layui\images\face\68.gif" />
    <Content Include="Content\layui\images\face\69.gif" />
    <Content Include="Content\layui\images\face\7.gif" />
    <Content Include="Content\layui\images\face\70.gif" />
    <Content Include="Content\layui\images\face\71.gif" />
    <Content Include="Content\layui\images\face\8.gif" />
    <Content Include="Content\layui\images\face\9.gif" />
    <Content Include="Content\layui\layui.all.js" />
    <Content Include="Content\layui\layui.js" />
    <Content Include="Content\layui\lay\modules\carousel.js" />
    <Content Include="Content\layui\lay\modules\code.js" />
    <Content Include="Content\layui\lay\modules\colorpicker.js" />
    <Content Include="Content\layui\lay\modules\element.js" />
    <Content Include="Content\layui\lay\modules\flow.js" />
    <Content Include="Content\layui\lay\modules\form.js" />
    <Content Include="Content\layui\lay\modules\jquery.js" />
    <Content Include="Content\layui\lay\modules\laydate.js" />
    <Content Include="Content\layui\lay\modules\layedit.js" />
    <Content Include="Content\layui\lay\modules\layer.js" />
    <Content Include="Content\layui\lay\modules\laypage.js" />
    <Content Include="Content\layui\lay\modules\laytpl.js" />
    <Content Include="Content\layui\lay\modules\mobile.js" />
    <Content Include="Content\layui\lay\modules\rate.js" />
    <Content Include="Content\layui\lay\modules\slider.js" />
    <Content Include="Content\layui\lay\modules\table.js" />
    <Content Include="Content\layui\lay\modules\tree.js" />
    <Content Include="Content\layui\lay\modules\upload.js" />
    <Content Include="Content\layui\lay\modules\util.js" />
    <Content Include="Content\loading1.css" />
    <Content Include="Content\skin\blue\acrossTab-bg.png" />
    <Content Include="Content\skin\blue\acrossTab.png" />
    <Content Include="Content\skin\blue\icon_arrow.png" />
    <Content Include="Content\skin\blue\skin.css" />
    <Content Include="Content\skin\default\acrossTab-bg.png" />
    <Content Include="Content\skin\default\acrossTab.png" />
    <Content Include="Content\skin\default\icon_arrow.png" />
    <Content Include="Content\skin\default\skin.css" />
    <Content Include="Content\skin\green\acrossTab-bg.png" />
    <Content Include="Content\skin\green\acrossTab.png" />
    <Content Include="Content\skin\green\icon_arrow.png" />
    <Content Include="Content\skin\green\skin.css" />
    <Content Include="Content\skin\orange\acrossTab-bg.png" />
    <Content Include="Content\skin\orange\acrossTab.png" />
    <Content Include="Content\skin\orange\icon_arrow.png" />
    <Content Include="Content\skin\orange\skin.css" />
    <Content Include="Content\skin\red\acrossTab-bg.png" />
    <Content Include="Content\skin\red\acrossTab.png" />
    <Content Include="Content\skin\red\icon_arrow.png" />
    <Content Include="Content\skin\red\skin.css" />
    <Content Include="Content\skin\yellow\acrossTab-bg.png" />
    <Content Include="Content\skin\yellow\acrossTab.png" />
    <Content Include="Content\skin\yellow\icon_arrow.png" />
    <Content Include="Content\skin\yellow\skin.css" />
    <Content Include="Content\style.css" />
    <Content Include="Content\toastr\toastr.min.css" />
    <Content Include="Content\vue.js" />
    <Content Include="CSS\1.1.3_sweetalert.min.css" />
    <Content Include="CSS\jquery-ui.css" />
    <Content Include="CSS\jquery.ui.all.css" />
    <Content Include="CSS\ng-tags-input.bootstrap.min.css" />
    <Content Include="CSS\ng-tags-input.min.css" />
    <Content Include="CSS\select2-spinner.gif" />
    <Content Include="CSS\select2.css" />
    <Content Include="CSS\select2.png" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Content\Site.css" />
    <Content Include="Highcharts-4.1.7\gfx\vml-radial-gradient.png" />
    <Content Include="Highcharts-4.1.7\graphics\meteogram-symbols-30px.png" />
    <Content Include="Highcharts-4.1.7\graphics\skies.jpg" />
    <Content Include="Highcharts-4.1.7\graphics\snow.png" />
    <Content Include="Highcharts-4.1.7\graphics\sun.png" />
    <Content Include="Highcharts-4.1.7\js\adapters\standalone-framework.js" />
    <Content Include="Highcharts-4.1.7\js\adapters\standalone-framework.src.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts-3d.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts-3d.src.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts-more.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts-more.src.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts.js" />
    <Content Include="Highcharts-4.1.7\js\highcharts.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\broken-axis.js" />
    <Content Include="Highcharts-4.1.7\js\modules\broken-axis.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\canvas-tools.js" />
    <Content Include="Highcharts-4.1.7\js\modules\canvas-tools.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\data.js" />
    <Content Include="Highcharts-4.1.7\js\modules\data.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\drilldown.js" />
    <Content Include="Highcharts-4.1.7\js\modules\drilldown.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\exporting.js" />
    <Content Include="Highcharts-4.1.7\js\modules\exporting.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\funnel.js" />
    <Content Include="Highcharts-4.1.7\js\modules\funnel.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\heatmap.js" />
    <Content Include="Highcharts-4.1.7\js\modules\heatmap.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\no-data-to-display.js" />
    <Content Include="Highcharts-4.1.7\js\modules\no-data-to-display.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\solid-gauge.js" />
    <Content Include="Highcharts-4.1.7\js\modules\solid-gauge.src.js" />
    <Content Include="Highcharts-4.1.7\js\modules\treemap.js" />
    <Content Include="Highcharts-4.1.7\js\modules\treemap.src.js" />
    <Content Include="Highcharts-4.1.7\js\themes\dark-blue.js" />
    <Content Include="Highcharts-4.1.7\js\themes\dark-green.js" />
    <Content Include="Highcharts-4.1.7\js\themes\dark-unica.js" />
    <Content Include="Highcharts-4.1.7\js\themes\gray.js" />
    <Content Include="Highcharts-4.1.7\js\themes\grid-light.js" />
    <Content Include="Highcharts-4.1.7\js\themes\grid.js" />
    <Content Include="Highcharts-4.1.7\js\themes\sand-signika.js" />
    <Content Include="Highcharts-4.1.7\js\themes\skies.js" />
    <Content Include="images\1-16.png" />
    <Content Include="images\2-16.png" />
    <Content Include="images\3-16.png" />
    <Content Include="images\9-16.png" />
    <Content Include="images\acrossTab\acrossTab-2.png" />
    <Content Include="images\acrossTab\acrossTab-bg.png" />
    <Content Include="images\acrossTab\acrossTab-close.png" />
    <Content Include="images\acrossTab\acrossTab.png" />
    <Content Include="images\admin-login-bg.jpg" />
    <Content Include="images\admin-loginform-bg.png" />
    <Content Include="images\black.gif" />
    <Content Include="images\cn.gif" />
    <Content Include="images\dataTable\sort_asc.png" />
    <Content Include="images\dataTable\sort_both.png" />
    <Content Include="images\dataTable\sort_desc.png" />
    <Content Include="images\foot03\l01.png" />
    <Content Include="images\foot03\l02.png" />
    <Content Include="images\foot03\l03.png" />
    <Content Include="images\foot03\l04.png" />
    <Content Include="images\foot03\l05.png" />
    <Content Include="images\foot03\l06.png" />
    <Content Include="images\foot03\ll01.png" />
    <Content Include="images\foot03\ll03.png" />
    <Content Include="images\foot03\ll04.png" />
    <Content Include="images\foot03\ll05.png" />
    <Content Include="images\foot03\ll06.png" />
    <Content Include="images\foot03\top.jpg" />
    <Content Include="images\foot03\youhui.png" />
    <Content Include="images\foot03\审核通过.jpg" />
    <Content Include="images\foot03\手动完成.jpg" />
    <Content Include="images\foot03\更换通道.jpg" />
    <Content Include="images\foot03\添加免审.jpg" />
    <Content Include="images\foot03\退回审核.jpg" />
    <Content Include="images\green.gif" />
    <Content Include="images\iCheck\aero%402x.png" />
    <Content Include="images\iCheck\aero.png" />
    <Content Include="images\iCheck\blue%402x.png" />
    <Content Include="images\iCheck\blue.png" />
    <Content Include="images\iCheck\green%402x.png" />
    <Content Include="images\iCheck\green.png" />
    <Content Include="images\iCheck\grey%402x.png" />
    <Content Include="images\iCheck\grey.png" />
    <Content Include="images\iCheck\minimal%402x.png" />
    <Content Include="images\iCheck\minimal.png" />
    <Content Include="images\iCheck\orange%402x.png" />
    <Content Include="images\iCheck\orange.png" />
    <Content Include="images\iCheck\pink%402x.png" />
    <Content Include="images\iCheck\pink.png" />
    <Content Include="images\iCheck\purple%402x.png" />
    <Content Include="images\iCheck\purple.png" />
    <Content Include="images\iCheck\red%402x.png" />
    <Content Include="images\iCheck\red.png" />
    <Content Include="images\iCheck\yellow%402x.png" />
    <Content Include="images\iCheck\yellow.png" />
    <Content Include="images\loading.gif" />
    <Content Include="images\loading1.gif" />
    <Content Include="images\logo.png" />
    <Content Include="images\mms-16.png" />
    <Content Include="images\pause_green.png" />
    <Content Include="images\play_blue.png" />
    <Content Include="images\red.gif" />
    <Content Include="images\sms-16.png" />
    <Content Include="images\start-16.png" />
    <Content Include="images\stop-16.png" />
    <Content Include="images\ucnter\avatar-default-S.gif" />
    <Content Include="images\ucnter\avatar-default.jpg" />
    <Content Include="images\ucnter\avatar.png" />
    <Content Include="images\ucnter\noavatar_small.gif" />
    <Content Include="images\um.gif" />
    <Content Include="MmsElement\1545728103019010020\QQ.jpg" />
    <Content Include="MmsElement\1545892123366010992\QQ.jpg" />
    <Content Include="MmsElement\1546924402364010780\QQ1.jpg" />
    <Content Include="MmsElement\1546924550229010781\c8eeadcdd927dc537e08d33e6b3ece2f.jpg" />
    <Content Include="MmsElement\1546924550229010781\QQ1.jpg" />
    <Content Include="MmsElement\2856be59be1bbba61625863b82988752.mp4" />
    <Content Include="new_file.html" />
    <Content Include="NoPermission.html" />
    <Content Include="Resources\CookieJs.txt" />
    <Content Include="Scripts\7.21.1sweetalert2.all.js" />
    <Content Include="Scripts\Angular\angular.js" />
    <Content Include="Scripts\Angular\angular.min.js" />
    <Content Include="Scripts\BlackMsisdn\Index.js" />
    <Content Include="Scripts\bootstrap-select.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Content\iconfont.ttf" />
    <Content Include="Content\iconfont.woff" />
    <Content Include="App_Browsers\BrowserFile1.browser" />
    <Content Include="Content\layui\font\iconfont.eot" />
    <Content Include="Content\layui\font\iconfont.ttf" />
    <Content Include="Content\layui\font\iconfont.woff" />
    <Content Include="Scripts\bt.css" />
    <Content Include="Scripts\bt.js" />
    <Content Include="Scripts\highcharts\highcharts-3d.js" />
    <Content Include="Scripts\MobileSign\css\bootstrap-datepicker.min.css" />
    <Content Include="Scripts\MobileSign\css\bootstrap-table.min.css" />
    <Content Include="Scripts\MobileSign\css\bootstrap.min.css" />
    <Content Include="Scripts\MobileSign\css\fileinput.min.css" />
    <Content Include="Scripts\MobileSign\css\font-awesome.min.css" />
    <Content Include="Scripts\MobileSign\js\bootstrap-datepicker.min.js" />
    <Content Include="Scripts\MobileSign\js\bootstrap-datepicker.zh-CN.min.js" />
    <Content Include="Scripts\MobileSign\js\bootstrap-table-zh-CN.min.js" />
    <Content Include="Scripts\MobileSign\js\bootstrap-table.min.js" />
    <Content Include="Scripts\MobileSign\js\bootstrap.min.js" />
    <Content Include="Scripts\MobileSign\js\fileinput.min.js" />
    <Content Include="Scripts\MobileSign\js\jquery.min.js" />
    <Content Include="Scripts\MobileSign\js\jquery.validate.min.js" />
    <Content Include="Scripts\MobileSign\js\messages_zh.min.js" />
    <Content Include="Scripts\MobileSign\js\moment.min.js" />
    <Content Include="Scripts\MobileSign\js\sweetalert.min.js" />
    <Content Include="Scripts\MobileSign\js\theme.min.js" />
    <Content Include="Scripts\MobileSign\js\zh.min.js" />
    <Content Include="Scripts\MobileSign\js\新建 文本文档 - 副本.txt" />
    <Content Include="Scripts\MobileSign\js\新建 文本文档.txt" />
    <Content Include="Scripts\RealTime\all.min.css" />
    <Content Include="Scripts\RealTime\bootstrap.min.css" />
    <Content Include="Scripts\RealTime\bootstrap.min.js" />
    <Content Include="Scripts\RealTime\echarts.min.js" />
    <Content Include="Scripts\RealTime\jquery-ui.min.css" />
    <Content Include="Scripts\RealTime\jquery-ui.min.js" />
    <Content Include="Scripts\RealTime\jquery.min.js" />
    <Content Include="Scripts\RealTime\moment.min.js" />
    <Content Include="Scripts\RealTime\popper.min.js" />
    <Content Include="Scripts\RealTime\select2.min.css" />
    <Content Include="Scripts\RealTime\select2.min.js" />
    <Content Include="Scripts\RealTime\tempusdominus-bootstrap-4.min.css" />
    <Content Include="Scripts\RealTime\tempusdominus-bootstrap-4.min.js" />
    <Content Include="X4cBAF5XcPOAKiKKADb1EVEwIEg433.mp3" />
    <Content Include="mmssignchannel.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Scripts\Channel\AddChannel.js" />
    <Content Include="Scripts\Channel\Index.js" />
    <Content Include="Scripts\Channel\SendTest.js" />
    <Content Include="Scripts\Channel\UpDateChannel.js" />
    <Content Include="Scripts\chosen.jquery.min.js" />
    <Content Include="Scripts\Client\AddCustomer.js" />
    <Content Include="Scripts\Client\DirectCustomerIndex.js" />
    <Content Include="Scripts\Client\IndirectCustomerIndex.js" />
    <Content Include="Scripts\Client\Recharge.js" />
    <Content Include="Scripts\Client\UpdateCustomer.js" />
    <Content Include="Scripts\Common.js" />
    <Content Include="Scripts\ContentPreparation\AddContent.js" />
    <Content Include="Scripts\ContentPreparation\Index.js" />
    <Content Include="Scripts\DD_belatedPNG_0.0.8a-min.js" />
    <Content Include="Scripts\formSelects-v3.js" />
    <Content Include="CSS\formSelects-v4.css" />
    <Content Include="Scripts\formSelects-v4.js" />
    <Content Include="Scripts\formSelects-v4.min.js" />
    <Content Include="Scripts\H-ui.admin.js" />
    <Content Include="Scripts\H-ui.min.js" />
    <Content Include="Scripts\highcharts.js" />
    <Content Include="Scripts\highcharts\exporting.js" />
    <Content Include="Scripts\highcharts\highcharts-zh_CN.js" />
    <Content Include="Scripts\highcharts\highcharts.js" />
    <Content Include="Scripts\html5shiv.js" />
    <Content Include="Scripts\Home\Index.js" />
    <Content Include="Scripts\hubs.js" />
    <Content Include="Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery-ui.js" />
    <Content Include="Scripts\jquery.combo.clientselect.js" />
    <Content Include="Scripts\jquery.contextmenu\jquery.contextmenu.r2.js" />
    <Content Include="Scripts\jquery.dataTables.min.js" />
    <Content Include="Scripts\jquery.searchableSelect.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.min.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\jquery.validation\1.14.0\additional-methods.js" />
    <Content Include="Scripts\jquery.validation\1.14.0\jquery.validate.js" />
    <Content Include="Scripts\jquery.validation\1.14.0\messages_zh.js" />
    <Content Include="Scripts\jquery.validation\1.14.0\validate-methods.js" />
    <Content Include="Scripts\jquery\1.9.1\jquery.js" />
    <Content Include="Scripts\jquery\1.9.1\jquery.min.js" />
    <Content Include="Scripts\layer.js" />
    <Content Include="Scripts\MmsAudit\Index.js" />
    <Content Include="Scripts\MobileSeciton\AddMobile.js" />
    <Content Include="Scripts\MobileSeciton\Index.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\MsgAudit\Index.js" />
    <Content Include="Scripts\MsgDetails\ReceiveReportList.js" />
    <Content Include="Scripts\MsgMo\ClientReceiveMoList.js" />
    <Content Include="Scripts\MsgPlan\Index.js" />
    <Content Include="Scripts\My97DatePicker\4.8\calendar.js" />
    <Content Include="Scripts\My97DatePicker\4.8\lang\en.js" />
    <Content Include="Scripts\My97DatePicker\4.8\lang\zh-cn.js" />
    <Content Include="Scripts\My97DatePicker\4.8\lang\zh-tw.js" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\datePicker.gif" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\default\datepicker.css" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\default\img.gif" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\twoer\datepicker-dev.css" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\twoer\datepicker.css" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\twoer\img.gif" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\twoer\img.png" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\WdatePicker.css" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\whyGreen\bg.jpg" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\whyGreen\datepicker.css" />
    <Content Include="Scripts\My97DatePicker\4.8\skin\whyGreen\img.gif" />
    <Content Include="Scripts\My97DatePicker\4.8\WdatePicker.js" />
    <Content Include="Scripts\MyScript\Jquery.AuditProduct1.js" />
    <Content Include="Scripts\MyScript\Jquery.Common.js" />
    <Content Include="Scripts\MyScript\Jquery.Customer.js" />
    <Content Include="Scripts\MyScript\Jquery.AuditProduct.js" />
    <Content Include="Scripts\MyScript\Jquery.Product1.js" />
    <Content Include="Scripts\MyScript\Jquery.Product.js" />
    <Content Include="Scripts\Account\login.js" />
    <Content Include="Scripts\Angular\ng-tags-input.min.js" />
    <Content Include="Scripts\OperMobileSection\Index.js" />
    <Content Include="Scripts\Permission\ClientList.js" />
    <Content Include="Scripts\Permission\Index.js" />
    <Content Include="Scripts\Permission\RoleClientEdit.js" />
    <Content Include="Scripts\Permission\RoleEdit.js" />
    <Content Include="Scripts\respond.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="CSS\Style.Product.css" />
    <Content Include="Scripts\select2.js" />
    <Content Include="Scripts\SensitiveWord\Index.js" />
    <Content Include="Scripts\SignRecords\UpdateSign.js" />
    <Content Include="Scripts\SignRecords\AddSign.js" />
    <Content Include="Scripts\SignRecords\AddSign1.js" />
    <Content Include="Scripts\SignRecords\Index.js" />
    <Content Include="Scripts\SignRecords\Index1.js" />
    <Content Include="Scripts\skin\default\icon-ext.png" />
    <Content Include="Scripts\skin\default\icon.png" />
    <Content Include="Scripts\skin\default\loading-0.gif" />
    <Content Include="Scripts\skin\default\loading-1.gif" />
    <Content Include="Scripts\skin\default\loading-2.gif" />
    <Content Include="Scripts\skin\layer.css" />
    <Content Include="Scripts\Staff\Department.js" />
    <Content Include="Scripts\Staff\Index.js" />
    <Content Include="Scripts\Staff\Staff.js" />
    <Content Include="Scripts\sweetalert.min.js" />
    <Content Include="Scripts\SystemProperty\Index.js" />
    <Content Include="Scripts\toastr\toastr.min.js" />
    <Content Include="Scripts\Worker\jquerynodom.js" />
    <Content Include="Scripts\Worker\worker.js" />
    <Content Include="SessionKeeper.asp" />
    <Content Include="signalr\prot.txt" />
    <Content Include="signalr\signalr.exe" />
    <Content Include="UploadFile\Black20181022103328--U1--服务器.txt" />
    <Content Include="MmsView.html" />
    <Content Include="Views\MsgMo\GetMsMoList.cshtml" />
    <Content Include="Views\Shared\_NgFooterPager.cshtml" />
    <Content Include="Views\SignRecords\AddSign1.cshtml" />
    <Content Include="Views\SignRecords\ExportSign.cshtml" />
    <Content Include="Views\MsgMo\ClientReceiveMoList.cshtml" />
    <Content Include="Views\MsgDetails\ReceiveReportList.cshtml" />
    <Content Include="Web References\BillingService\AmountResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Account\Notfound.cshtml" />
    <Content Include="Views\Home\Start.cshtml" />
    <Content Include="Views\SendStatistics\UserStatistics.cshtml" />
    <Content Include="Views\SendStatistics\ChannelStatistics.cshtml" />
    <Content Include="Views\FileList\Index.cshtml" />
    <Content Include="Views\SensitiveWord\UpdateSensitiveWord.cshtml" />
    <Content Include="Views\BlackMsisdn\UpdateBlackMsisdn.cshtml" />
    <Content Include="Views\SignRecords\Index.cshtml" />
    <Content Include="Views\SignRecords\AddSign.cshtml" />
    <Content Include="Views\SalesMan\Index.cshtml" />
    <Content Include="Views\SalesMan\AddSalesMan.cshtml" />
    <Content Include="Views\SalesMan\UpdateSalesMan.cshtml" />
    <Content Include="Views\MsgAudit\BatchAudityuan.cshtml" />
    <Content Include="Views\Notices\NoticesIndex.cshtml" />
    <Content Include="Views\Notices\AddNotices.cshtml" />
    <Content Include="Views\Notices\UpdateNotices.cshtml" />
    <Content Include="Views\Shared\_AjaxFooterPager.cshtml" />
    <Content Include="UploadFile\SensitiveWord20180824155340--U1--15.csv" />
    <Content Include="Views\MsgPlan\UpDataPlanContent.cshtml" />
    <Content Include="Views\Product\ConfigChannel.cshtml" />
    <Content Include="Views\Product\ReplaceUserProduct.cshtml" />
    <Content Include="Views\Product\SmsProudctPage.cshtml" />
    <Content Include="Views\Mms\MmsDetails.cshtml" />
    <Content Include="Views\Mms\MmsUserDetails.cshtml" />
    <Content Include="Views\MmsAudit\Index.cshtml" />
    <Content Include="Views\MmsAudit\BatchAudit.cshtml" />
    <Content Include="Views\MmsAudit\UpdateMmsChannel.cshtml" />
    <Content Include="Views\Product\MmsIndex.cshtml" />
    <Content Include="Views\Product\MmsUpdateProduct.cshtml" />
    <Content Include="Views\Product\ConfigMmsChannel.cshtml" />
    <Content Include="Views\Product\ReplaceUserMmsProduct.cshtml" />
    <Content Include="Views\Product\MmsAddProduct.cshtml" />
    <Content Include="Views\Mms\MmsTask.cshtml" />
    <Content Include="Views\Mms\MmsFileDetail.cshtml" />
    <Content Include="Views\MmsAudit\UpDateMmsContent.cshtml" />
    <Content Include="Views\MmsPlan\Index.cshtml" />
    <Content Include="Views\MmsPlan\UpdatePlanChannel.cshtml" />
    <Content Include="Views\MsgDetails\MsgReceiveReport.cshtml" />
    <Content Include="Views\Mms\MmsView.cshtml" />
    <Content Include="Views\Shared\_Layout2.cshtml" />
    <Content Include="Views\Shared\_Layout3.cshtml" />
    <Content Include="Views\MsgAudit\BatchAudit.cshtml" />
    <Content Include="Views\Shared\_LayoutLayui.cshtml" />
    <Content Include="Views\MsgAudit\Index1.cshtml" />
    <Content Include="Views\Home\Start - 复制.cshtml" />
    <Content Include="finereport.frm" />
    <Content Include="Views\Home\Index - 复制.cshtml" />
    <Content Include="Views\Channel\QueueMove.cshtml" />
    <Content Include="Views\Channel\SelectQueue.cshtml" />
    <Content Include="Views\Mms\MmsTemlpateView.cshtml" />
    <Content Include="Views\Mms\MmsTemplateFileDetail.cshtml" />
    <Content Include="Views\Mms\MmsTemplateAudit.cshtml" />
    <Content Include="Views\Mms\MmsTemplateReplaceChannel.cshtml" />
    <Content Include="Views\Mms\MmsTemplateView.cshtml" />
    <Content Include="Views\MmsAudit\LookContentMatch.cshtml" />
    <Content Include="Views\Client\AddCustomer.cshtml" />
    <Content Include="Views\Client\AddCustomer - 复制%282%29.cshtml" />
    <Content Include="Views\Client\UpdateCustomer - 复制.cshtml" />
    <Content Include="Views\Product\GetFmsProductList.cshtml" />
    <Content Include="Views\Product\FmsUpdateProduct.cshtml" />
    <Content Include="Views\Product\FmsAddProduct.cshtml" />
    <Content Include="Views\MsgDetails\UserExportField.cshtml" />
    <Content Include="Views\Shared\_ExportField.cshtml" />
    <Content Include="Views\MsgDetails\ChannelExportField.cshtml" />
    <Content Include="Views\SignRecords\AddSignRouting.cshtml" />
    <Content Include="Views\SignRecords\SignRouting.cshtml" />
    <Content Include="Views\SignRecords\UpdateSignRouting.cshtml" />
    <Content Include="Views\Client\PushRptMo.cshtml" />
    <Content Include="Views\SignRecords\UpdateSign.cshtml" />
    <Content Include="Views\SendStatistics\ChannelStatusStatistics.cshtml" />
    <Content Include="Views\KeyWordRouting\Index.cshtml" />
    <Content Include="Views\KeyWordRouting\AddKeyWordRouting.cshtml" />
    <Content Include="Views\KeyWordRouting\UpdataKeyWordRouting.cshtml" />
    <Content Include="Views\Home\UpdateRemark.cshtml" />
    <Content Include="Views\SignRecords\BatchDelete.cshtml" />
    <Content Include="Views\SignRecords\GetSign.cshtml" />
    <Content Include="Views\Category\Index.cshtml" />
    <Content Include="Views\Lable\Index.cshtml" />
    <Content Include="Views\Company\Index.cshtml" />
    <Content Include="Views\Company\AddCompany.cshtml" />
    <Content Include="Views\MsgAudit\BatchUpText.cshtml" />
    <Content Include="Views\Company\CompanyUpdate.cshtml" />
    <Content Include="Views\Group\RelevanceSaleMan.cshtml" />
    <Content Include="Views\Group\Index.cshtml" />
    <Content Include="Views\Group\AddGroup.cshtml" />
    <Content Include="Views\Group\UpdateGroup.cshtml" />
    <Content Include="Views\Group\GroupStatistics.cshtml" />
    <Content Include="Views\RedMsisdn\Index.cshtml" />
    <Content Include="Views\RedMsisdn\AddRed.cshtml" />
    <Content Include="Views\RedMsisdn\UpdateRedMsisdn.cshtml" />
    <Content Include="Views\Lable\AddLabel.cshtml" />
    <Content Include="Views\Lable\UpdateLable.cshtml" />
    <Content Include="Views\SalesMan\RelevanceLabel.cshtml" />
    <Content Include="Views\Lable\RelevanceUser.cshtml" />
    <Content Include="Views\Home\StartUp.cshtml" />
    <Content Include="Views\SignRecords\BatchDeleteSign.cshtml" />
    <Content Include="Views\KeyWordRouting\DeleteBatchKeyWordRoutings.cshtml" />
    <Content Include="Views\Home\Console.cshtml" />
    <Content Include="Views\Client\AddCustomer201010.cshtml" />
    <Content Include="Views\ChannelMonitor\Index.cshtml" />
    <Content Include="Views\ChannelMonitor\AddChannelMonitor.cshtml" />
    <Content Include="Views\ChannelMonitor\UpdateChannelMonitors.cshtml" />
    <Content Include="Views\Client\VoiceCustomerIndex.cshtml" />
    <Content Include="Views\Product\VoiceIndex.cshtml" />
    <Content Include="Views\Product\VoiceAddProduct.cshtml" />
    <Content Include="Views\Client\AddVoiceCustomer.cshtml" />
    <Content Include="Views\Client\UpdateVoiceCustomer.cshtml" />
    <Content Include="Views\VoiceTemplate\Index.cshtml" />
    <Content Include="Views\VoiceDetails\Index.cshtml" />
    <Content Include="Views\VoiceDetails\VoiceTask.cshtml" />
    <Content Include="Views\Channel\QueueReportMove.cshtml" />
    <Content Include="Views\Client\VoiceCustomerChannelIndex.cshtml" />
    <Content Include="Views\Client\AddVoiceChannelCustomer.cshtml" />
    <Content Include="Views\Client\UpdateVoiceChannelCustomer.cshtml" />
    <Content Include="Views\Company\ImportCompany.cshtml" />
    <Content Include="Views\Product\OptionIndex.cshtml" />
    <Content Include="Views\Amount\Index1.cshtml" />
    <Content Include="Views\SignReport\Index.cshtml" />
    <Content Include="Views\SignReport\AddSignReport.cshtml" />
    <Content Include="Views\SignReport\UpdateSignReport.cshtml" />
    <Content Include="Views\MsgMo\AddMo.cshtml" />
    <Content Include="Views\SignReport\SaveCaiYinTemplate.cshtml" />
    <Content Include="Views\SignReport\AddBatchSignReport.cshtml" />
    <Content Include="Views\SignReport\SaveLianTongTemplate.cshtml" />
    <Content Include="Views\Home\LookLog.cshtml" />
    <Content Include="Views\BlackMsisdn\AddExtBlack.cshtml" />
    <Content Include="Views\BlackMsisdn\AddProvinceBlack.cshtml" />
    <Content Include="Views\BlackMsisdn\ExtIndex.cshtml" />
    <Content Include="Views\BlackMsisdn\ProvinceIndex.cshtml" />
    <Content Include="Views\MsisdnConvert\Index.cshtml" />
    <Content Include="Views\MsisdnBack\Index.cshtml" />
    <Content Include="Views\SendStatistics\ChannelStatisticsDay.cshtml" />
    <Content Include="Views\MsgDetails\OldChannelDetails.cshtml" />
    <Content Include="Views\MsisdnBack\GoodMsisdnRuleIndex.cshtml" />
    <Content Include="Views\MsisdnBack\AddGoodMsisdnRule.cshtml" />
    <Content Include="Views\MsisdnBack\UpdateGoodMsisdnRule.cshtml" />
    <Content Include="Views\MsgAudit\BatchUpdateTextDynamic.cshtml" />
    <Content Include="Views\SignRecords\CustomIndex.cshtml" />
    <Content Include="Views\SignRecords\AddCustomSign.cshtml" />
    <Content Include="Views\SignRecords\UpdateCustomSign.cshtml" />
    <Content Include="Views\SignRecords\BatchDeleteCustomSign.cshtml" />
    <Content Include="Views\SignRecords\BatchCustomDelete.cshtml" />
    <Content Include="Views\SendStatistics\UserStatusStatistics.cshtml" />
    <Content Include="Views\MsisdnBack\AddBack.cshtml" />
    <Content Include="Views\RestSendRule\Index.cshtml" />
    <Content Include="Views\RestSendRule\Update.cshtml" />
    <Content Include="Views\RestSendRule\Add.cshtml" />
    <Content Include="Views\SignReport\SaveCaiYinTemplate - 复制%282%29.cshtml" />
    <Content Include="Views\SignReport\DeleteLianTongTemplate.cshtml" />
    <Content Include="Views\IPWhite\Index.cshtml" />
    <Content Include="Views\IPWhite\AddIpWhite.cshtml" />
    <Content Include="Views\IPWhite\UpdateIPWhite.cshtml" />
    <Content Include="Views\MsgDetails\ReportDetails.cshtml" />
    <Content Include="Views\SensitiveWord\BatchDeleteSensitiveWord.cshtml" />
    <Content Include="Views\MsgDetails\ChannelReportExportField.cshtml" />
    <Content Include="Views\RealTimeStatistics\ChannelView.cshtml" />
    <Content Include="Views\RealTimeStatistics\Index.cshtml" />
    <Content Include="Views\RealTimeStatistics\TimeView.cshtml" />
    <Content Include="Views\RealTimeStatistics\AllStatistics.cshtml" />
    <Content Include="Views\TextTemplatePinci\AddTemplatePinci.cshtml" />
    <Content Include="Views\TextTemplatePinci\Index.cshtml" />
    <Content Include="Views\TextTemplatePinci\UpdateTemplatePinci.cshtml" />
    <Content Include="Views\ChannelTextTemplate\AddTemplate.cshtml" />
    <Content Include="Views\ChannelTextTemplate\Index.cshtml" />
    <Content Include="Views\ChannelTextTemplate\UpdateTemplate.cshtml" />
    <Content Include="Views\ChannelSpeedPolicy\Create.cshtml" />
    <Content Include="Views\ChannelSpeedPolicy\Edit.cshtml" />
    <Content Include="Views\ChannelSpeedPolicy\Index.cshtml" />
    <Content Include="Views\ChannelSpeedPolicy\_PolicyForm.cshtml" />
    <Content Include="Views\TextSignExtNumber\AddTextSignExtNumber.cshtml" />
    <Content Include="Views\TextSignExtNumber\BatchDeleteTextSignExtNumber.cshtml" />
    <Content Include="Views\TextSignExtNumber\ImportTextSignExtNumber.cshtml" />
    <Content Include="Views\TextSignExtNumber\Index.cshtml" />
    <Content Include="Views\TextSignExtNumber\UpdateTextSignExtNumber.cshtml" />
    <Content Include="Views\MobileSignReport\AddMobileSignReport.cshtml" />
    <Content Include="Views\MobileSignReport\BatchDeleteMobileSignReport.cshtml" />
    <Content Include="Views\MobileSignReport\ImportMobileSignReport.cshtml" />
    <Content Include="Views\MobileSignReport\Index.cshtml" />
    <Content Include="Views\MobileSignReport\UpdateMobileSignReport.cshtml" />
    <Content Include="Views\RestSendRule\ConfigUsers.cshtml" />
    <Content Include="Views\ContentPreparationLink\AddContent.cshtml" />
    <Content Include="Views\ContentPreparationLink\Index.cshtml" />
    <Content Include="Views\ContentPreparationLink\UpdateContentPreparation.cshtml" />
    <Content Include="Views\ContentPreparationLink\BatchImport.cshtml" />
    <None Include="Web References\BillingService\BillingService.wsdl" />
    <None Include="Web References\BillingService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\DataService\DataService.disco" />
    <None Include="Web References\BillingService\BillingService.disco" />
    <Content Include="Web References\InternalService\IndivSendResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <None Include="Web References\InternalService\InternalService.disco" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout1.cshtml" />
    <Content Include="Views\Account\LoginTest.cshtml" />
    <Content Include="Views\Account\Login.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\_NaviPartial.cshtml" />
    <Content Include="Views\Shared\_LoginPartial.cshtml" />
    <Content Include="Views\Staff\Index.cshtml" />
    <Content Include="Views\Staff\StaffDetail.cshtml" />
    <Content Include="Views\Staff\Staff.cshtml" />
    <Content Include="Views\Staff\Department.cshtml" />
    <Content Include="Views\Permission\Index.cshtml" />
    <Content Include="Views\Permission\RoleEdit.cshtml" />
    <Content Include="Views\Channel\Index.cshtml" />
    <Content Include="Views\Channel\AddChannel.cshtml" />
    <Content Include="Scripts\MyScript\Jquery.Channel.js" />
    <Content Include="Views\Channel\UpDateChannel.cshtml" />
    <Content Include="Views\Channel\SendTest.cshtml" />
    <Content Include="Views\SensitiveWord\Index.cshtml" />
    <Content Include="Views\SensitiveWord\AddSensitiveWord.cshtml" />
    <Content Include="Views\BlackMsisdn\Index.cshtml" />
    <Content Include="Views\BlackMsisdn\AddBlack.cshtml" />
    <Content Include="Views\WhiteMsisdn\Index.cshtml" />
    <Content Include="Views\WhiteMsisdn\AddWhite.cshtml" />
    <Content Include="Views\SystemProperty\Index.cshtml" />
    <Content Include="Views\SystemProperty\AddSystem.cshtml" />
    <Content Include="Views\SystemProperty\UpdateSystem.cshtml" />
    <Content Include="Views\Client\DirectCustomerIndex.cshtml" />
    <Content Include="Views\Client\UpdateCustomer.cshtml" />
    <Content Include="Views\Shared\_FooterPager.cshtml" />
    <Content Include="Views\MsgTask\Index.cshtml" />
    <Content Include="Views\MsgDetails\Index.cshtml" />
    <Content Include="Views\MobileSeciton\Index.cshtml" />
    <Content Include="Views\MobileSeciton\AddMobile.cshtml" />
    <Content Include="Views\OperMobileSection\Index.cshtml" />
    <Content Include="Views\MsgAudit\Index.cshtml" />
    <Content Include="Views\MsgMo\Index.cshtml" />
    <Content Include="Views\Amount\Index.cshtml" />
    <Content Include="Views\MsgPlan\Index.cshtml" />
    <Content Include="Views\Product\SmsIndex.cshtml" />
    <Content Include="Views\Product\SmsAddProduct.cshtml" />
    <Content Include="Views\Product\SmsUpdateProduct.cshtml" />
    <Content Include="Views\ContentPreparation\Index.cshtml" />
    <Content Include="Views\ContentPreparation\AddContent.cshtml" />
    <Content Include="Views\ContentPreparation\UpdateContentPreparation.cshtml" />
    <Content Include="Views\Client\IndirectCustomerIndex.cshtml" />
    <Content Include="Views\Permission\ClientList.cshtml" />
    <Content Include="Views\Permission\RoleClientEdit.cshtml" />
    <Content Include="Views\MsgDetails\ClientDetails.cshtml" />
    <Content Include="Views\MsgAudit\UpDateContent.cshtml" />
    <Content Include="Views\MsgAudit\UpdateChannel.cshtml" />
    <Content Include="Views\MsgPlan\UpdatePlanChannel.cshtml" />
    <Content Include="Views\Client\Recharge.cshtml" />
    <Content Include="Views\SignRecords\Index1.cshtml" />
    <None Include="Web References\DataService\DataService.wsdl" />
    <None Include="Web References\DataService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\InternalService\InternalService.wsdl" />
    <Content Include="Web References\InternalService\NResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <None Include="Web References\InternalService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="Web References\InternalService\SendResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\InternalService\SaveMmsResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="移动签名报备模版.xlsx" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="MmsElement\1546924029778010779\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-1.10.2.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\YLHT.GP.Common\YLHT.GP.Common.csproj">
      <Project>{C214764C-4282-4169-9B6D-E0C6C9C9326E}</Project>
      <Name>YLHT.GP.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\YLHT.GP.Models\YLHT.GP.Models.csproj">
      <Project>{46c40391-0d0e-4108-a77c-a88737532615}</Project>
      <Name>YLHT.GP.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\YLHT_GP_Business\YLHT_GP_Business.csproj">
      <Project>{edef710d-16b0-43b3-9e5a-fdcdfd16937a}</Project>
      <Name>YLHT_GP_Business</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resource.resx" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://127.0.0.1:5002/BillingService.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\BillingService\</RelPath>
      <UpdateFromURL>http://127.0.0.1:5002/BillingService.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>YLHT_GP_Manager_BillingService_BillingService</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://127.0.0.1:5002/DataService.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\DataService\</RelPath>
      <UpdateFromURL>http://127.0.0.1:5002/DataService.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>YLHT_GP_Manager_DataService_DataService</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://127.0.0.1:5001/InternalService.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\InternalService\</RelPath>
      <UpdateFromURL>http://127.0.0.1:5001/InternalService.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>YLHT_GP_Manager_InternalService_InternalService</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Mixed Platforms|AnyCPU'">
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Mixed Platforms|x86'">
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>1881</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://127.0.0.1:8088</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.7\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.7\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>