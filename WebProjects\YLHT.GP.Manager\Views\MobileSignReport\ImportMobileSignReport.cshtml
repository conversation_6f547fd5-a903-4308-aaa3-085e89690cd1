@{
    ViewBag.Title = "导入移动签名报备";
    Layout = null;
    Html.RenderAction("GetChannelList", "Base");
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>导入移动签名报备</title>

    <!-- 基础CSS库 -->
    <link href="~/Scripts/MobileSign/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/Scripts/MobileSign/css/font-awesome.min.css" rel="stylesheet">

    <!-- 插件CSS -->
    <link href="~/Scripts/MobileSign/css/fileinput.min.css" rel="stylesheet">

    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }

        .panel {
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .panel-title {
            font-weight: bold;
        }

        .btn {
            margin-right: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .alert {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">导入移动签名报备</h3>
                    </div>
                    <div class="panel-body">
                        <div class="alert alert-info" role="alert">
                            <p><strong>导入说明：</strong></p>
                            <p>1. 请下载导入模板，按照模板格式填写数据后上传。</p>
                            <p>2. 支持的文件格式：xls、xlsx。</p>
                            <p>3. 文件大小不能超过5MB。</p>
                            <p>4. 一次最多导入1000条记录。</p>
                            <p>5. 必填字段：日期、类型、客户名称、签名类型、签名、签名材料、身份证照片。</p>
                            <p>6. <b>身份证材料支持多个图片：身份证照片(反正面)、身份证材料1-5，共6个列可以存放身份证相关图片。</b></p>
                            <p>7. <b>签名材料和身份证相关图片必须使用Excel公式格式：=DISPIMG("图片ID",1)</b></p>
                            <p>8. <b>当前系统只存储图片ID引用，不保存实际图片文件。如需实际图片功能，请联系管理员配置。</b></p>
                        </div>

                        <!-- 批量删除功能区域 -->
                        <div class="panel panel-warning" style="margin-bottom: 20px;">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-trash"></i> 批量删除功能
                                </h4>
                            </div>
                            <div class="panel-body">
                                <div class="alert alert-warning" role="alert">
                                    <p><strong>批量删除说明：</strong></p>
                                    <p>1. 支持按通道批量删除移动签名报备记录。</p>
                                    <p>2. 可选择删除指定日期范围内的记录。</p>
                                    <p>3. <strong>删除操作不可恢复，请谨慎操作！</strong></p>
                                </div>

                                <form id="form_batch_delete">
                                    @Html.AntiForgeryToken()
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="delete_channelId">选择通道</label>
                                                @Html.DropDownList("DeleteChannelId", ViewBag.Channels as List<SelectListItem>, "请选择通道", new { @class = "form-control", @id = "delete_channelId" })
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="start_date">开始日期（可选）</label>
                                                <input type="date" class="form-control" id="start_date" name="start_date">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="end_date">结束日期（可选）</label>
                                                <input type="date" class="form-control" id="end_date" name="end_date">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label>&nbsp;</label>
                                                <div>
                                                    <button type="button" class="btn btn-danger btn-block" id="btn_batch_delete">
                                                        <i class="fa fa-trash"></i> 批量删除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <form id="form_import" enctype="multipart/form-data">
                            <div class="form-group">
                                <a href="/MobileSignReport/DownloadTemplate" class="btn btn-success">
                                    <i class="fa fa-download"></i> 下载导入模板
                                </a>
                            </div>
                            <div class="form-group">
                                <label for="txt_channelId">通道</label>
                                @Html.DropDownList("ChannelId", ViewBag.Channels as List<SelectListItem>, new { @class = "form-control", @id = "txt_channelId" })
                            </div>
                            <div class="form-group">
                                <input id="import_file" name="file" type="file" class="file" data-show-preview="false">
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-primary" id="btn_submit">
                                    <i class="fa fa-upload"></i> 导入
                                </button>
                                <button type="button" class="btn btn-default" id="btn_back">
                                    <i class="fa fa-arrow-left"></i> 返回
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 基础JS库 -->
    <script src="~/Scripts/MobileSign/js/jquery.min.js"></script>
    <script src="~/Scripts/MobileSign/js/bootstrap.min.js"></script>

    <!-- 插件JS -->
    <script src="~/Scripts/MobileSign/js/fileinput.min.js"></script>
    <script src="~/Scripts/MobileSign/js/zh.min.js"></script>
    <script src="~/Scripts/MobileSign/js/jquery.validate.min.js"></script>
    <script src="~/Scripts/MobileSign/js/localization/messages_zh.min.js"></script>
    <link href="~/CSS/select2.css" rel="stylesheet" />
    <script src="~/Scripts/select2.js"></script>
    <script>
        $(function () {
            // 页面加载时清除任何可能的文件缓存
            try {
                console.log('页面加载：开始清除文件缓存');
                var fileInputs = $('input[type="file"]');
                fileInputs.each(function() {
                    this.value = '';
                });
                console.log('页面加载：文件缓存已清除');
            } catch (e) {
                console.warn('页面加载：清除文件缓存失败', e);
            }

            $("#txt_channelId").select2();
            // 初始化文件上传控件
            $("#import_file").fileinput({
                language: 'zh',
                allowedFileExtensions: ['xls', 'xlsx'],
                showUpload: false,
                showCaption: true,
                showCancel: true,
                showRemove: true,
                showPreview: false,
                maxFileSize: 5120, // 5MB
                msgSizeTooLarge: '文件 "{name}" ({size} KB) 超过了允许的最大上传大小 {maxSize} KB。',
                msgInvalidFileExtension: '文件 "{name}" 不是允许的文件格式。只支持 "{extensions}" 格式的文件。'
            });

            // 提交表单
            $("#btn_submit").click(function () {
                if (!$("#form_import").valid()) {
                    return;
                }

                var channelId = $("#txt_channelId").val();
                if (!channelId || channelId == "-1") {
                    alert("请选择通道");
                    return;
                }

                var fileInput = $('#import_file')[0];

                // 详细检查文件选择状态
                console.log('[前端] 文件输入检查：');
                console.log('  fileInput:', fileInput);
                console.log('  fileInput.files:', fileInput.files);
                console.log('  fileInput.files.length:', fileInput.files ? fileInput.files.length : 'undefined');
                console.log('  fileInput.value:', fileInput.value);

                if (!fileInput.files || fileInput.files.length === 0) {
                    alert("请选择文件");
                    return;
                }

                var selectedFile = fileInput.files[0];
                console.log('  选中的文件：');
                console.log('    名称：', selectedFile.name);
                console.log('    大小：', selectedFile.size);
                console.log('    类型：', selectedFile.type);
                console.log('    最后修改：', new Date(selectedFile.lastModified).toLocaleString());

                // 生成唯一的导入ID用于前端跟踪
                var importId = 'import_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                console.log('[前端' + importId + '] 开始导入，通道ID：' + channelId + '，文件：' + fileInput.files[0].name);

                // 创建全新的FormData，避免缓存问题
                var formData = new FormData();

                // 添加时间戳避免缓存
                formData.append('timestamp', Date.now());
                formData.append('importId', importId);

                // 确保文件正确添加到FormData
                try {
                    formData.append('file', selectedFile, selectedFile.name);
                    console.log('[前端' + importId + '] 文件已添加到FormData：', selectedFile.name);
                } catch (e) {
                    console.error('[前端' + importId + '] 添加文件到FormData失败：', e);
                    alert('文件添加失败：' + e.message);
                    return;
                }

                formData.append('channelId', channelId);

                // 验证FormData内容
                console.log('[前端' + importId + '] FormData内容：');
                for (var pair of formData.entries()) {
                    if (pair[1] instanceof File) {
                        console.log('  ' + pair[0] + ': File(' + pair[1].name + ', ' + pair[1].size + ' bytes)');
                    } else {
                        console.log('  ' + pair[0] + ': ' + pair[1]);
                    }
                }

                // 禁用提交按钮防止重复点击
                $("#btn_submit").prop('disabled', true).text('导入中...');

                $.ajax({
                    url: '/MobileSignReport/ImportMobileSignReport',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    cache: false, // 禁用缓存
                    timeout: 300000, // 5分钟超时
                    success: function (data) {
                        console.log('[前端' + importId + '] 导入完成，结果：', data);
                        if (data.code === 0) {
                            alert("导入成功：" + data.msg);
                            window.location.href = "/MobileSignReport/Index";
                        } else {
                            alert("导入失败：" + data.msg);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('[前端' + importId + '] 导入出错：', status, error);
                        var errorMsg = "导入失败：";
                        if (status === 'timeout') {
                            errorMsg += "请求超时，请检查文件大小或网络连接";
                        } else if (xhr.responseText) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                errorMsg += response.msg || response.Message || "服务器错误";
                            } catch (e) {
                                errorMsg += "服务器错误，请稍后重试";
                            }
                        } else {
                            errorMsg += "网络错误，请稍后重试";
                        }
                        alert(errorMsg);
                    },
                    complete: function() {
                        // 恢复提交按钮
                        $("#btn_submit").prop('disabled', false).text('导入');

                        // 强制清除文件缓存，避免文件对象失效问题
                        try {
                            console.log('[前端' + importId + '] 开始清除文件缓存');

                            // 方法1：使用fileinput插件的清除方法
                            $('#import_file').fileinput('clear');

                            // 方法2：直接清空原生input的value
                            var nativeInput = $('#import_file')[0];
                            if (nativeInput) {
                                nativeInput.value = '';
                            }

                            // 方法3：强制刷新fileinput插件
                            $('#import_file').fileinput('refresh');

                            console.log('[前端' + importId + '] 文件缓存已清除');

                            // 显示提示信息
                            //alert('上传完成！文件缓存已清除，请重新选择文件进行下次上传。');

                        } catch (e) {
                            console.error('[前端' + importId + '] 清除文件缓存失败：', e);
                            // 备用方案：刷新页面
                            if (confirm('文件缓存清除失败，是否刷新页面？')) {
                                window.location.reload();
                            }
                        }
                    }
                });
            });

            // 返回按钮
            $("#btn_back").click(function () {
                window.location.href = "/MobileSignReport/Index";
            });

            // 批量删除按钮点击事件
            $('#btn_batch_delete').click(function () {
                var channelId = $('#delete_channelId').val();
                var startDate = $('#start_date').val();
                var endDate = $('#end_date').val();

                if (!channelId) {
                    alert('请选择要删除的通道');
                    return;
                }

                // 构建确认消息
                var confirmMsg = '确定要删除通道 "' + $('#delete_channelId option:selected').text() + '" 下的';
                if (startDate && endDate) {
                    confirmMsg += startDate + ' 至 ' + endDate + ' 期间的';
                } else if (startDate) {
                    confirmMsg += startDate + ' 之后的';
                } else if (endDate) {
                    confirmMsg += endDate + ' 之前的';
                } else {
                    confirmMsg += '所有';
                }
                confirmMsg += '移动签名报备记录吗？\n\n此操作不可恢复，请谨慎操作！';

                if (!confirm(confirmMsg)) {
                    return;
                }

                // 显示加载提示
                var loadingMsg = '正在删除，请稍候...';

                // 发送AJAX请求
                $.ajax({
                    url: '@Url.Action("BatchDeleteByCondition", "MobileSignReport")',
                    type: 'POST',
                    data: {
                        channelId: channelId,
                        startDate: startDate,
                        endDate: endDate,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    beforeSend: function() {
                        $('#btn_batch_delete').prop('disabled', true).text('删除中...');
                    },
                    success: function (result) {
                        $('#btn_batch_delete').prop('disabled', false).html('<i class="fa fa-trash"></i> 批量删除');

                        if (result.code === 0) {
                            alert('批量删除成功！' + result.msg);
                            // 清空表单
                            $('#form_batch_delete')[0].reset();
                        } else {
                            alert('批量删除失败：' + result.msg);
                        }
                    },
                    error: function (xhr, status, error) {
                        $('#btn_batch_delete').prop('disabled', false).html('<i class="fa fa-trash"></i> 批量删除');
                        console.error('批量删除请求失败:', status, error);
                        alert('批量删除请求失败，请检查网络连接或联系管理员');
                    }
                });
            });

            // 表单验证
            $("#form_import").validate({
                rules: {
                    ChannelId: {
                        required: true
                    }
                },
                messages: {
                    ChannelId: {
                        required: "请选择通道"
                    }
                }
            });
        });
    </script>
</body>
</html> 