<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>文件内容读取调试</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .file-info {
            background-color: #e8f4fd;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 文件内容读取调试工具</h1>
        
        <div class="debug-section">
            <h3>📁 文件选择和信息</h3>
            <div class="form-group">
                <label for="debug_file">选择Excel文件：</label>
                <input type="file" id="debug_file" class="form-control" accept=".xls,.xlsx">
            </div>
            
            <div id="file_info" class="file-info" style="display:none;">
                <h4>文件信息：</h4>
                <div id="file_details"></div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🧪 测试控制</h3>
            <div class="form-group">
                <label for="channel_select">选择通道：</label>
                <select id="channel_select" class="form-control">
                    <option value="1">通道1</option>
                    <option value="2">通道2</option>
                    <option value="3">通道3</option>
                </select>
            </div>
            
            <div class="btn-group">
                <button id="btn_test_upload" class="btn btn-primary">测试上传</button>
                <button id="btn_clear_log" class="btn btn-default">清除日志</button>
            </div>
            
            <div class="alert alert-info" style="margin-top: 15px;">
                <strong>测试说明：</strong>
                <ol>
                    <li>选择一个Excel文件</li>
                    <li>第一次选择通道1，点击"测试上传"</li>
                    <li>第二次选择通道2，<strong>不重新选择文件</strong>，直接点击"测试上传"</li>
                    <li>观察服务器日志中的文件内容读取情况</li>
                </ol>
            </div>
        </div>

        <div class="debug-section">
            <h3>📊 上传结果</h3>
            <div id="upload_results" class="log-output">等待测试...\n</div>
        </div>

        <div class="debug-section">
            <h3>🔍 关键检查点</h3>
            <div class="alert alert-warning">
                <h4>在服务器日志中查找以下信息：</h4>
                <ul>
                    <li><strong>文件流状态</strong>：位置、长度、可读性</li>
                    <li><strong>文件字节大小</strong>：实际读取的字节数</li>
                    <li><strong>文件头信息</strong>：Excel文件的二进制头</li>
                    <li><strong>工作表内容</strong>：前几行的实际数据</li>
                    <li><strong>第一列内容</strong>：是否为空或NULL</li>
                </ul>
            </div>
            
            <div class="alert alert-success">
                <h4>预期的正常日志：</h4>
                <pre>[导入abc12345] 文件信息：名称=test.xlsx, 大小=12345, 类型=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
[导入abc12345] 流状态：位置=0, 长度=12345, 可读=True, 可定位=True
[导入abc12345] 文件字节读取完成，实际大小：12345
[导入abc12345] 文件头：50-4B-03-04-14-00-06-00
[导入abc12345] 工作表检查：总行数=10
[导入abc12345] 第0行第0列内容：'2023/12/01'
[导入abc12345] 第1行第0列内容：'2023/12/02'</pre>
            </div>
            
            <div class="alert alert-danger">
                <h4>异常情况的日志：</h4>
                <pre>[导入abc12345] 流状态：位置=12345, 长度=12345, 可读=True, 可定位=True
[导入abc12345] 文件字节读取完成，实际大小：0
[导入abc12345] 第0行第0列内容：'NULL'</pre>
                <p><strong>这表明文件流已经被消耗，位置在末尾，读取到空内容。</strong></p>
            </div>
        </div>
    </div>

    <script>
        var uploadCount = 0;
        
        // 文件选择事件
        $('#debug_file').change(function() {
            var file = this.files[0];
            if (file) {
                var fileInfo = `
                    <strong>文件名：</strong> ${file.name}<br>
                    <strong>文件大小：</strong> ${file.size} 字节<br>
                    <strong>文件类型：</strong> ${file.type}<br>
                    <strong>最后修改：</strong> ${new Date(file.lastModified).toLocaleString()}
                `;
                $('#file_details').html(fileInfo);
                $('#file_info').show();
                
                addLog('✅ 文件已选择：' + file.name + ' (' + file.size + ' 字节)');
            } else {
                $('#file_info').hide();
            }
        });
        
        // 测试上传
        $('#btn_test_upload').click(function() {
            var fileInput = document.getElementById('debug_file');
            if (!fileInput.files || fileInput.files.length === 0) {
                addLog('❌ 错误：没有选择文件', 'error');
                return;
            }
            
            uploadCount++;
            var channelId = $('#channel_select').val();
            var file = fileInput.files[0];
            
            addLog('🚀 开始第' + uploadCount + '次上传测试，通道' + channelId);
            addLog('📁 使用文件：' + file.name + ' (' + file.size + ' 字节)');
            
            var formData = new FormData();
            formData.append('file', file);
            formData.append('channelId', channelId);
            formData.append('debugTest', 'true');
            formData.append('uploadCount', uploadCount);
            
            $.ajax({
                url: '/MobileSignReport/ImportMobileSignReport',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                cache: false,
                timeout: 30000,
                success: function (data) {
                    if (data.code === 0) {
                        addLog('✅ 第' + uploadCount + '次测试成功：' + data.msg, 'success');
                    } else {
                        addLog('❌ 第' + uploadCount + '次测试失败：' + data.msg, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    addLog('❌ 第' + uploadCount + '次测试出错：' + status + ' - ' + error, 'error');
                    if (xhr.responseText) {
                        addLog('响应内容：' + xhr.responseText.substring(0, 200), 'error');
                    }
                },
                complete: function() {
                    addLog('📋 第' + uploadCount + '次测试完成，请检查服务器日志中的详细信息\n');
                }
            });
        });
        
        // 清除日志
        $('#btn_clear_log').click(function() {
            $('#upload_results').html('日志已清除...\n');
            uploadCount = 0;
        });
        
        function addLog(message, type = 'info') {
            var timestamp = new Date().toLocaleTimeString();
            var className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            var logEntry = '[' + timestamp + '] ' + message + '\n';
            
            if (className) {
                logEntry = '<span class="' + className + '">' + logEntry + '</span>';
            }
            
            $('#upload_results').append(logEntry);
            $('#upload_results').scrollTop($('#upload_results')[0].scrollHeight);
        }
    </script>
</body>
</html>
