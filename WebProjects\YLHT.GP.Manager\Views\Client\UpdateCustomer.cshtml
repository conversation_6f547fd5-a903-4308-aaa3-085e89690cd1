﻿@using YLHT.GP.Common
@model YLHT.GP.Models.ClientModel
@{ ViewBag.Title = "修改客户";}
<style>
    .numberbox {
        width: 80px
    }

    .sh-container {
        width: 100%
    }

    .sh-container {
        position: absolute;
        transform: translate(-50%, -50%);
        left: 110%;
        top: 32%;
        width: 100%;
        max-width: 1024px;
    }
</style>
<article class="page-container" id="app">
    @using (Html.BeginForm("UpdateCustomer", "Client", FormMethod.Post, new { id = "form-client-add", @class = "form form-horizontal" }))
    {
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">基本信息</li>
                <li>CMPP</li>
                @*<li>HTTP</li>*@
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>账号：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="" v-model="username" placeholder="" id="UserName" autocomplete="off" name="UserName">
                            <input type="text" class="input-text" style="display:none" value="@(Model.UserId == 0 ? 0 : Model.UserId)" id="" name="UserId" />
                            <input type="text" class="input-text" style="display:none" value="@(Model.UserName == null ? "" : Model.UserName)" id="oldUserName" />
                            <input type="text" name="ParentUserId" style="display:none" value="@(Model.ParentUserId == 0 ? 0 : Model.ParentUserId)" />
                            <input type="text" style="display:none" name="Status" value="@Model.Status" />
                            <label id="namelable"></label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">平台密码：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="password" class="input-text" value="" placeholder="**************" id="PASSWORD" name="PASSWORD">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">接口密码：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="password" class="input-text" value="" placeholder="***************" id="TOKEN" name="TOKEN">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>手机号码：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.Mobile == null ? "" : Model.Mobile)" placeholder="" id="Mobile" name="Mobile">
                            <label style="color:red">@*注：登陆时验证手机号*@</label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>客户名称：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.CustomerName == null ? "" : Model.CustomerName)" placeholder="" id="CUSTOMERNAME" name="CUSTOMERNAME">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>联系人：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.Contact == null ? "" : Model.Contact)" placeholder="" id="CONTACT" name="CONTACT">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>联系电话：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.Phone == null ? "" : Model.Phone)" placeholder="" id="PHONE" name="PHONE">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">联系地址：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.Address == null ? "" : Model.Address)" placeholder="" id="ADDRESS" name="ADDRESS">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">联系QQ：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.QQ == null ? "" : Model.QQ)" placeholder="" id="QQ" name="QQ">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">联系邮箱：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@(Model.Email == null ? "" : Model.Email)" placeholder="" id="EMAIL" name="EMAIL">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">归属管理员：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("AdminId", ((List<SelectListItem>)ViewBag.alist), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">归属公司：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("CompanyId", ((List<SelectListItem>)ViewBag.CompanyList), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">归属业务员：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @*@Html.DropDownList("SalesId", ((List<SelectListItem>)ViewBag.slist), new { style = "width:100%" })*@
                            <select id="SalesId" name="SalesId" style="width:80%"></select>
                            <label><a href="javascript:void(0);" onclick="replacesales('@Model.SalesId')">刷新业务员</a></label>&nbsp;<label><a href="javascript:;" onclick="layer_show('添加业务员','/SalesMan/AddSalesMan?flag=1','600','400')">添加业务员</a></label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">归属类别：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("CateoryId", ((List<SelectListItem>)ViewBag.CateoryList), new { style = "width:80%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">所属标签：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("LableId", ((List<SelectListItem>)ViewBag.LableList), new { style = "width:80%" })
                        </div>
                    </div>
                    <div style="display:none" class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">扣费方式：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            <div class="radio-box">
                                <input name="PAYTYPE" type="radio" id="PayType-1" value="0" checked>
                                <label for="PayType-1">业务类型</label>
                            </div>
                            @*@Html.DropDownList("PAYTYPE", ((List<SelectListItem>)ViewBag.PayTypes))*@
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">付费方式：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            <div class="radio-box">
                                @if (Model.FeeType == 0)
                                {
                                    <input name="FEETYPE" type="radio" id="TdType-1" value="0" checked>
                                    <label for="TdType-1">预&nbsp;付&nbsp;费</label>
                                }
                                else
                                {
                                    <input name="FEETYPE" type="radio" id="TdType-1" value="0">
                                    <label for="TdType-1">预&nbsp;付&nbsp;费</label>
                                }
                            </div>
                            <div class="radio-box">
                                @if (Model.FeeType == 1)
                                {
                                    <input type="radio" id="TdType-2" name="FEETYPE" value="1" checked>
                                    <label for="TdType-2">后&nbsp;付&nbsp;费</label>
                                    /**/
                                }
                                else
                                { <input type="radio" id="TdType-2" name="FEETYPE" value="1">
                                    <label for="TdType-2">后&nbsp;付&nbsp;费</label>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">计费方式：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            <div class="radio-box">
                                @if (Model.ChargeType == 0)
                                {
                                    <input name="CHARGETYPE" type="radio" id="OpType-1" value="0" checked>
                                    <label for="OpType-1">提交计费</label>}
                                else
                                { <input name="CHARGETYPE" type="radio" id="OpType-1" value="0">
                                    <label for="OpType-1">提交计费</label>
                                }
                            </div>
                            <div class="radio-box">
                                @if (Model.ChargeType == 1)
                                {
                                    <input type="radio" id="OpType-2" name="CHARGETYPE" value="1" checked>

                                    <label for="OpType-2">成功计费</label>}
                                else
                                {<input type="radio" id="OpType-2" name="CHARGETYPE" value="1">

                                    <label for="OpType-2">成功计费</label>
                                }
                            </div>

                            <div class="radio-box">
                                @if (Model.ChargeType == 2)
                                {<input type="radio" id="OpType-3" name="CHARGETYPE" value="2" checked>
                                    <label for="OpType-3">成功未知</label>}
                                else
                                {<input type="radio" id="OpType-3" name="CHARGETYPE" value="2">
                                    <label for="OpType-3">成功未知</label>}
                            </div>

                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">客户类型：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            <div class="radio-box checkboxwidth">
                                <input name="ClientType" type="radio" id="ClientType-1" value="0" @(Model.ClientType == 0 ? "checked" : "")>
                                <label for="ClientType-1">营销</label>
                            </div>
                            <div class="radio-box checkboxwidth">
                                <input type="radio" id="ClientType-2" name="ClientType" value="1" @(Model.ClientType == 1 ? "checked" : "")>
                                <label for="ClientType-2">行业</label>
                            </div>
                            <div class="radio-box checkboxwidth">
                                <input type="radio" id="ClientType-3" name="ClientType" value="2" @(Model.ClientType == 2 ? "checked" : "")>
                                <label for="ClientType-3">验证码</label>
                            </div>
                            <div class="radio-box checkboxwidth">
                                <input type="radio" id="ClientType-4" name="ClientType" value="3" @(Model.ClientType == 3 ? "checked" : "")>
                                <label for="ClientType-4">接口独享队列（技术动）</label>
                            </div>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">功能选项：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            @foreach (var item in EnumMethods.GetItems(typeof(YLHT.GP.Common.ClientOption)).Where(d => d.Value > 0))
                            {
                                <div class="check-box">
                                    @Html.CheckBox(item.Name, (bool)Model.GetOption((ClientOption)item.Value), new { value = (bool)Model.GetOption((ClientOption)item.Value) }) <label for="@item.Name">@item.Text</label>
                                    @*<input type="checkbox" name="@item.Name" value="@((bool)Model.GetOption((ClientOption)item.Value))">@item.Text*@
                                    <label for="checkbox-pinglun">&nbsp;</label>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">接口选项：</label>
                        <div class="formControls col-xs-8 col-sm-9 skin-minimal">
                            @foreach (var item in EnumMethods.GetItems(typeof(YLHT.GP.Common.AccessOrigin)).Where(w => w.Value > 0))
                            {
                                <div class="check-box">
                                    @Html.CheckBox(item.Name, (bool)Model.GetOrigin((AccessOrigin)item.Value), new { value = (bool)Model.GetOrigin((AccessOrigin)item.Value) }) <label for="@item.Name">@item.Text</label>
                                    @*<input type="checkbox" name="@item.Name" value=@((bool)Model.GetOrigin((AccessOrigin)item.Value))>@item.Text*@
                                    <label for="checkbox-pinglun">&nbsp;</label>
                                </div>
                            }

                            <input type="text" style="display:none" id="ACCESSORIGIN" name="ACCESSORIGIN" value="@Model.AccessOrigin" />
                            <input type="text" style="display:none" id="OPTIONS" name="OPTIONS" value="@Model.Options" />
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">余额提交方式：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="BalanceSubmitMode" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.BalanceSubmitMode == 0 ? "selected" : "")>提交失败</option>
                                <option value="1" @(Model.BalanceSubmitMode == 1 ? "selected" : "")>进入审核</option>
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">计费字数：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="number" class="input-text" value="@Model.ShortWords" data-toggle="tooltip" title="普通短信" placeholder="普通短信" id="ShortWords" name="ShortWords"> -
                            <input type="number" class="input-text" value="@Model.LongWords" data-toggle="tooltip" title="长短信" placeholder="长短信" id="LongWords" name="LongWords">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">短信单价：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text numberbox" value="@Model.Price" data-toggle="tooltip" title="实际价格" placeholder="实际价格" id="Price" name="Price"> -
                            <input type="text" class="input-text numberbox" value="@Model.VPrice" data-toggle="tooltip" title="虚拟价格" placeholder="虚拟价格" id="VPRICE" name="VPRICE">
                            @*-
                <input type="number" class="input-text" value="@Model.Price1" placeholder="" id="Price1" name="Price1">*@
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">闪信单价：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text numberbox" value="@Model.Price2" data-toggle="tooltip" title="实际价格" placeholder="实际价格" id="Price2" name="Price2"> -
                            <input type="text" class="input-text numberbox" value="@Model.VPrice2" data-toggle="tooltip" title="虚拟价格" placeholder="虚拟价格" id="VPRICE2" name="VPRICE2">
                            @*-
                <input type="number" class="input-text" value="@Model.Price1" placeholder="" id="Price1" name="Price1">*@
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">彩信单价：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text numberbox" value="@Model.Price1" placeholder="实际价格" data-toggle="tooltip" title="实际价格" id="Price1" name="Price1"> -
                            <input type="text" class="input-text numberbox" value="@Model.VPrice1" placeholder="虚拟价格" data-toggle="tooltip" title="虚拟价格" id="VPRICE1" name="VPRICE1">
                            @*-
                <input type="number" class="input-text" value="@Model.Price1" placeholder="" id="Price1" name="Price1">*@
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">子端口号：</label>
                        <div class="formControls col-xs-8 col-sm-3">
                            <input type="text" class="input-text" placeholder="子端口号" id="CHILDEXTNUMBER" @(Model.CHILDEXTNUMBER != null && Model.CHILDEXTNUMBER != "" ? "readonly" : "") value="@Model.CHILDEXTNUMBER" name="CHILDEXTNUMBER">
                        </div>
                        <div class="formControls col-xs-8 col-sm-2"><input type="text" class="input-text" placeholder="要修改的子端口号" id="NEWCHILDEXTNUMBER" value="@Model.NEWCHILDEXTNUMBER" name="NEWCHILDEXTNUMBER"></div>
                        <div class="formControls col-xs-8 col-sm-1"><button class="btn" type="button" onclick="batchupdate()">批量修改</button></div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">优先级：</label>
                        <div class="formControls col-xs-8 col-sm-9">

                            @*@Html.DropDownList("PRIORITY", ((List<SelectListItem>)ViewBag.priorityList))*@
                            <select name="PRIORITY" id="PRIORITY" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.Priority == 0 ? "selected" : "")>最低</option>
                                <option value="1" @(Model.Priority == 1 ? "selected" : "")>较低</option>
                                <option value="2" @(Model.Priority == 2 ? "selected" : "")>低</option>
                                <option value="3" @(Model.Priority == 3 ? "selected" : "")>正常</option>
                                <option value="4" @(Model.Priority == 4 ? "selected" : "")>高于正常</option>
                                <option value="5" @(Model.Priority == 5 ? "selected" : "")>高</option>
                                <option value="6" @(Model.Priority == 6 ? "selected" : "")>较高</option>
                                <option value="7" @(Model.Priority == 7 ? "selected" : "")>最高</option>
                            </select>

                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">个性化页面：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="pageid" id="PRIORITY" class="input-text select select-box" style="width:100%">
                                <option value="0">--请选择个性化页面--</option>
                                @foreach (var item in (List<YLHT.GP.Models.PageModel>)ViewBag.Pages)
                                {
                                    <option value="@item.PageId" @(item.PageId == ViewBag.Page ? "selected" : "")>@item.PName</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">web端提交分包数量：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.SUBMITPACKAGE" placeholder="" id="SUBMITPACKAGE" name="SUBMITPACKAGE">
                            <label style="color:red">注：web端提交分包数量 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">审核数量：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.AuditNumber" placeholder="" id="AUDITNUMBER" name="AUDITNUMBER">
                            <label style="color:red">注：0免审核，1条审核，2条审核，依此类推 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">日发送限制(号码)：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.DayLimit" placeholder="" id="DayLimit" name="DayLimit">
                            <label style="color:red">注：0不限制 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">日发送限制(内容)：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.ContentDayLimit" placeholder="" id="ContentDayLimit" name="ContentDayLimit">
                            <input type="number" class="input-text" value="@Model.ContentDayLimitStartTime" placeholder="" id="ContentDayLimitStartTime" name="ContentDayLimitStartTime">-
                            <input type="number" class="input-text" value="@Model.ContentDayLimitEndTime" placeholder="" id="ContentDayLimitEndTime" name="ContentDayLimitEndTime">
                            <label style="color:red">注：0不限制 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">提交数限制（条数）：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.MaxSubmitLimit" placeholder="" id="MaxSubmitLimit" name="MaxSubmitLimit">
                            <input type="number" class="input-text" value="@Model.SubmitLimitStartTime" placeholder="" id="SubmitLimitStartTime" name="SubmitLimitStartTime">-
                            <input type="number" class="input-text" value="@Model.SubmitLimitEndTime" placeholder="" id="SubmitLimitEndTime" name="SubmitLimitEndTime">
                            <label style="color:red">注：0不限制,返回错误代码1077 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">是否开启签名白名单：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsSignMaxsubmitLimit" class="select-box">
                                <option @(Model.IsSignMaxsubmitLimit == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.IsSignMaxsubmitLimit == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                            <label style="color:red">注：是否开启签名白名单！作用于 提交数限制（条数）、日发送限制(内容) </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">是否使用单用户路由：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="UseUserRouting" class="select-box">
                                <option @(Model.UseUserRouting == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.UseUserRouting == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                            <label style="color:red">注：是否使用单用户路由，启用用后只匹配对应用户下的路由 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">单用户路由操作：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="USERROUTINGFALID" class="select-box">
                                <option @(Model.USERROUTINGFALID == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.USERROUTINGFALID == 1 ? "selected" : "") value="1">失败</option>
                                <option @(Model.USERROUTINGFALID == 2 ? "selected" : "") value="2">审核</option>
                            </select>
                            <label style="color:red">注：是否使用单用户路由，启用用后未匹配路由的失败或进审核 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">签名白名单：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <textarea class="textarea" id="SignsubmitLimit" name="SignsubmitLimit" style="width:100%">@Model.SignsubmitLimit</textarea>
                            <label style="color:red">注：格式为：签名:数量，例如：【京东】:3000，表示京东这个签名只能提交3000条，超过的失败,作用于 提交数限制（条数）、日发送限制(内容)，多个签名用英文逗号隔开（,） </label>
                        </div>
                    </div>


                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">提交内容号码限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsContentMsisdn" class="select-box">
                                <option @(Model.IsContentMsisdn == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.IsContentMsisdn == 1 ? "selected" : "") value="1">启用</option>
                                <option @(Model.IsContentMsisdn == 2 ? "selected" : "") value="2">号码+链接</option>
                                <option @(Model.IsContentMsisdn == 3 ? "selected" : "") value="3">链接</option>
                            </select>
                            <select name="ContentMsisdnIsAudit" class="select-box">
                                <option @(Model.ContentMsisdnIsAudit == 1 ? "selected" : "") value="1">进审核</option>
                                <option @(Model.ContentMsisdnIsAudit == 0 ? "selected" : "") value="0">失败</option>
                            </select>
                            <input type="number" class="input-text" value="@Model.ContentMsisdnStartTime" placeholder="" id="ContentMsisdnStartTime" name="ContentMsisdnStartTime">-
                            <input type="number" class="input-text" value="@Model.ContentMsisdnEndTime" placeholder="" id="ContentMsisdnEndTime" name="ContentMsisdnEndTime">
                            <label style="color:red">注：返回错误代码1020，0不限制，规定时间段内拦截：11位号码，固话号码，11位号码带横杠（-），固话号码带横杠（-），例如：147876604813、139-0428-5237、0571-86615020、0594-3369130、0537656203、07305380958 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">提交内容号码限制1：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="MsisdnFalidType" class="select-box">
                                <option @(Model.MsisdnFalidType == 0 ? "selected" : "") value="0">不拦截</option>
                                <option @(Model.MsisdnFalidType == 1 ? "selected" : "") value="1">全部类型拦截进审核</option>
                                <option @(Model.MsisdnFalidType == 2 ? "selected" : "") value="2">手机号码拦截进审核</option>
                                <option @(Model.MsisdnFalidType == 3 ? "selected" : "") value="3">固话号码拦截进审核</option>
                                <option @(Model.MsisdnFalidType == 4 ? "selected" : "") value="4">全部类型拦截失败</option>
                                <option @(Model.MsisdnFalidType == 5 ? "selected" : "") value="5">手机号码拦截失败</option>
                                <option @(Model.MsisdnFalidType == 6 ? "selected" : "") value="6">固话号码拦截失败</option>
                            </select>
                            <label style="color:red">注：提交内容号码限制设置时间段外的拦截，如果提交内容号码限制不启用，这个也不生效 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">追加扩展：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="AppendExtNumber" class="select-box">
                                <option @(Model.AppendExtNumber == 1 ? "selected" : "") value="1">启用</option>
                                <option @(Model.AppendExtNumber == 0 ? "selected" : "") value="0">不启用</option>
                            </select>
                            <label style="color:red">注：签名报备的情况下后面追加客户带的扩展 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">是否启用多维度号码频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select v-model="usenumberlimit" name="UseNumberLimit" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.UseNumberLimit == 0 ? "selected" : "")>启用</option>
                                <option value="1" @(Model.UseNumberLimit == 1 ? "selected" : "")>不启用</option>
                            </select>
                        </div>
                    </div>

                    <div class="row cl" v-show="showusenumberlimit">
                        <label class="form-label col-xs-4 col-sm-2">号码频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <label>@(ViewBag.limtstr!=null ? ViewBag.limtstr :"")</label>
                        </div>
                        <div class="formControls col-xs-8 col-sm-9">
                            <table class="table table-border table-bg">
                                <thead>
                                    <tr>
                                        <td>周期(规定的时间范围,必须是数字)</td>
                                        <td>单位(时间)</td>
                                        <td>数量(每个号码在周期内可发的数量,必须是数字)</td>
                                        <td>操作</td>
                                    </tr>
                                </thead>

                                <tbody id="numbers">
                                    @if (ViewBag.NumberLimits != null)
                                    {
                                        if (((string[])ViewBag.NumberLimits).Count() > 0)
                                        {
                                            int nnumber = 0;
                                            foreach (var item in (string[])ViewBag.NumberLimits)
                                            {
                                                var str = item.Split("-");
                                                <tr>
                                                    <td>
                                                        <input style="width:100%" class="input-text" type="number" value="@str[0]" name="periods@(nnumber)" />
                                                    </td>
                                                    <td>
                                                        <select id="numer" class="select select-box" name="numberoco@(nnumber)">
                                                            @foreach (var item1 in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                            {
                                                                <option value="@item1.Name" @(item1.Name == str[1] ? "selected" : "")>@item1.Text</option>
                                                            }
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input style="width:100%" class="input-text" value="@str[2]" type="number" name="counts@(nnumber)" />
                                                    </td>
                                                    <td>
                                                        <button type="button" onclick="btntr_del(this)" class="ui-button">删除</button>
                                                    </td>
                                                </tr>
                                                nnumber += 1;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td><input style="width:100%" class="input-text" type="number" name="periods0" /></td>
                                            <td>
                                                <select class="select select-box" name="numberoco0">
                                                    @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                    {
                                                        <option value="@item.Name">@item.Text</option>
                                                    }
                                                </select>
                                            </td>
                                            <td>
                                                <input style="width:100%" class="input-text" type="number" name="counts0" />
                                            </td>
                                        </tr>
                                    }

                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="4" style="text-align:center"><button type="button" class="ui-button" id="btnadd">添加</button></td>
                                    </tr>
                                </tfoot>
                            </table>
                            <input type="hidden" value="@Model.numbercountt" name="numbercountt" id="numbert" />
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">是否启用多维度地区频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select v-model="usemsisdnmaclimit" name="USEMSISDNMACLIMIT" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.USEMSISDNMACLIMIT == 0 ? "selected" : "")>不启用</option>
                                <option value="1" @(Model.USEMSISDNMACLIMIT == 1 ? "selected" : "")>启用进审核</option>
                                <option value="2" @(Model.USEMSISDNMACLIMIT == 2 ? "selected" : "")>启用拦截失败</option>
                            </select>
                        </div>
                    </div>

                    <div class="row cl" v-show="showusemsisdnmaclimit">
                        <label class="form-label col-xs-4 col-sm-2">地区频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <label>@(ViewBag.msisdnlimtstr != null ? ViewBag.msisdnlimtstr : "")</label>
                        </div>
                        <div class="formControls col-xs-8 col-sm-9">
                            <table class="table table-border table-bg">
                                <thead>
                                    <tr>
                                        <td>周期(规定的时间范围,必须是数字)</td>
                                        <td>单位(时间)</td>
                                        <td>数量(每个地区在周期内可发的数量,必须是数字)</td>
                                        <td>操作</td>
                                    </tr>
                                </thead>

                                <tbody id="msisdnlimits">
                                    @if (!string.IsNullOrEmpty(Model.MSISDNMACLIMIT))
                                    {
                                        int msisdnnumber = 0;
                                        foreach (var item in Model.MSISDNMACLIMIT.Split(';'))
                                        {
                                            if (!string.IsNullOrEmpty(item))
                                            {
                                                var str = item.Split('-');
                                                <tr>
                                                    <td>
                                                        <input style="width:100%" class="input-text" type="number" value="@str[0]" name="msisdnperiods@(msisdnnumber)" />
                                                    </td>
                                                    <td>
                                                        <select id="msisdnunit" class="select select-box" name="msisdnnumberoco@(msisdnnumber)">
                                                            @foreach (var item1 in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                            {
                                                                <option value="@item1.Name" @(item1.Name == str[1] ? "selected" : "")>@item1.Text</option>
                                                            }
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input style="width:100%" class="input-text" value="@str[2]" type="number" name="msisdncounts@(msisdnnumber)" />
                                                    </td>
                                                    <td>
                                                        <button type="button" onclick="msisdnbtntr_del(this)" class="ui-button">删除</button>
                                                    </td>
                                                </tr>
                                                msisdnnumber += 1;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td><input style="width:100%" class="input-text" type="number" name="msisdnperiods0" /></td>
                                            <td>
                                                <select class="select select-box" name="msisdnnumberoco0">
                                                    @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                    {
                                                        <option value="@item.Name">@item.Text</option>
                                                    }
                                                </select>
                                            </td>
                                            <td>
                                                <input style="width:100%" class="input-text" type="number" name="msisdncounts0" />
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="4" style="text-align:center"><button type="button" class="ui-button" id="msisdnbtnadd">添加</button></td>
                                    </tr>
                                </tfoot>
                            </table>
                            <input type="hidden" value="@(!string.IsNullOrEmpty(Model.MSISDNMACLIMIT) ? Model.MSISDNMACLIMIT.Split(';').Length : 0)" name="msisdncountt" id="msisdnnumbert" />
                        </div>
                    </div>

                    <div class="row cl" v-show="showusemsisdnmaclimit">
                        <label class="form-label col-xs-4 col-sm-2">多频次省份拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <div style="max-height:200px;overflow-y:auto;">
                                @if (ViewBag.provs != null)
                                {
                                    var p = ((Dictionary<int, string>)ViewBag.provs);
                                    foreach (var item in p.Keys)
                                    {
                                        <input type="checkbox" name="PROVINCELIMIT1" id="PROVINCELIMIT1_@item" value="@item" onclick="provlimitclick(this)" /> @p[item].ToString();
                                    }
                                }
                                <input type="hidden" value="@Model.PROVINCELIMIT" name="PROVINCELIMIT" id="PROVINCELIMITid" />
                            </div>
                            <label style="color:red">注：选择要进行频次限制的省份 </label>
                        </div>
                    </div>

                    <div class="row cl" v-show="showusemsisdnmaclimit">
                        <label class="form-label col-xs-4 col-sm-2">多频次地区拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <div style="max-height:200px;overflow-y:auto;">
                                @if (ViewBag.citys != null)
                                {
                                    var p = ((Dictionary<int, string>)ViewBag.citys);
                                    foreach (var item in p.Keys)
                                    {
                                        <input type="checkbox" name="CITYSLIMIT1" id="CITYSLIMIT1_@item" value="@item" onclick="citylimitclick(this)" /> @p[item].ToString();
                                    }
                                }
                                <input type="hidden" value="@Model.CITYSLIMIT" name="CITYSLIMIT" id="CITYSLIMITid" />
                            </div>
                            <label style="color:red">注：选择要进行频次限制的城市 </label>
                        </div>
                    </div>


                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">是否启用多维度内容频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select v-model="usecontentlimit" name="UseContentLimit" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.UseContentLimit == 0 ? "selected" : "")>不启用</option>
                                <option value="1" @(Model.UseContentLimit == 1 ? "selected" : "")>启用</option>
                                <option value="2" @(Model.UseContentLimit == 2 ? "selected" : "")>内容链接</option>
                                <option value="3" @(Model.UseContentLimit == 3 ? "selected" : "")>内容链接+内容手机号</option>
                            </select>
                        </div>
                    </div>

                    <div class="row cl" v-show="showusecontentlimit">
                        <label class="form-label col-xs-4 col-sm-2">内容频次限制：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <label>@(ViewBag.contentlimtstr != null ? ViewBag.contentlimtstr :"")</label>
                        </div>
                        <div class="formControls col-xs-8 col-sm-9">
                            <table class="table table-border table-bg">
                                <thead>
                                    <tr>
                                        <td>周期(规定的时间范围,必须是数字)</td>
                                        <td>单位(时间)</td>
                                        <td>数量(每个号码在周期内可发的数量,必须是数字)</td>
                                        <td>操作</td>
                                    </tr>
                                </thead>

                                <tbody id="contens">
                                    @if (ViewBag.ContentLimits != null)
                                    {
                                        if (((string[])ViewBag.ContentLimits).Count() > 0)
                                        {
                                            int nnumber = 0;
                                            foreach (var item in (string[])ViewBag.ContentLimits)
                                            {
                                                var str = item.Split("-");
                                                <tr>
                                                    <td>
                                                        <input style="width:100%" class="input-text" type="number" value="@str[0]" name="contentperiods@(nnumber)" />
                                                    </td>
                                                    <td>
                                                        <select id="numer" class="select select-box" name="contentnumberoco@(nnumber)">
                                                            @foreach (var item1 in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                            {
                                                                <option value="@item1.Name" @(item1.Name == str[1] ? "selected" : "")>@item1.Text</option>
                                                            }
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input style="width:100%" class="input-text" value="@str[2]" type="number" name="contentcounts@(nnumber)" />
                                                    </td>
                                                    <td>
                                                        <button type="button" onclick="contentbtntr_del(this)" class="ui-button">删除</button>
                                                    </td>
                                                </tr>
                                                nnumber += 1;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td><input style="width:100%" class="input-text" type="number" name="contentperiods0" /></td>
                                            <td>
                                                <select class="select select-box" name="contentnumberoco0">
                                                    @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit)))
                                                    {
                                                        <option value="@item.Name">@item.Text</option>
                                                    }
                                                </select>
                                            </td>
                                            <td>
                                                <input style="width:100%" class="input-text" type="number" name="contentcounts0" />
                                            </td>
                                        </tr>
                                    }

                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="4" style="text-align:center"><button type="button" class="ui-button" id="contentbtnadd">添加</button></td>
                                    </tr>
                                </tfoot>
                            </table>
                            <input type="hidden" value="@Model.contentcountt" name="contentcountt" id="contentnumbert" />
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">签名路由规则：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="SignRoutingRules" id="SignRoutingRuleId" class="select-box select input-text" style="width:100%">
                                <option value="0" @(Model.SignRoutingRules == 0 ? "selected" : "")>默认</option>
                                <option value="1" @(Model.SignRoutingRules == 1 ? "selected" : "")>价格小</option>
                                <option value="2" @(Model.SignRoutingRules == 2 ? "selected" : "")>价格大</option>
                                <option value="3" @(Model.SignRoutingRules == 3 ? "selected" : "")>流量大</option>
                                <option value="4" @(Model.SignRoutingRules == 4 ? "selected" : "")>流量小</option>
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">签名验证：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @*@Html.DropDownList("Signverify", ((List<SelectListItem>)ViewBag.Signverifys))*@

                            <select name="Signverify" v-model="Signverify" id="Signverify" class="input-text select select-box" style="width:100%">
                                <option value="1" @(Model.Signverify == 1 ? "selected" : "")>不验证</option>
                                <option value="2" @(Model.Signverify == 2 ? "selected" : "")>需要签名</option>
                                <option value="3" @(Model.Signverify == 3 ? "selected" : "")>强制签名</option>
                                <option value="4" @(Model.Signverify == 4 ? "selected" : "")>报备签名</option>
                            </select>

                        </div>
                    </div>
                    <div class="row cl" v-show="qiangsign" id="qiangsign">
                        <label class="form-label col-xs-4 col-sm-2">强制签名：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.ConstraIntSign" placeholder="" id="CONSTRAINTSIGN" name="CONSTRAINTSIGN">
                            <label style="color:red">注：签名的格式为【2-10个字符】，如果为空表示不启用此功能！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">删除客户扩展：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="DeleteUserExtNumber" class="select-box">
                                <option @(Model.DeleteUserExtNumber == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.DeleteUserExtNumber == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                            <label style="color:red">注：删除客户账号分配的扩展号，不删除客户提交的扩展 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">短信签名报备拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="ISFILINGSIGNFAILD" id="ISFILINGSIGNFAILD" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.ISFILINGSIGNFAILD == 0 ? "selected" : "")>进审核</option>
                                <option value="1" @(Model.ISFILINGSIGNFAILD == 1 ? "selected" : "")>失败</option>
                            </select>
                            <label style="color:red">注：验证客户提交的短信签名未报备的情况下是否失败，前提是启用一客一签预匹配功能才能生效，返回状态码：1064</label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">5G消息通道是否发纯文本：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="ISTEXT5G" class="select-box">
                                <option @(Model.ISTEXT5G == 0 ? "selected" : "") value="0">否</option>
                                <option @(Model.ISTEXT5G == 1 ? "selected" : "") value="1">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">彩信报备编码类型：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="SaveMmsEncodingType" class="select-box">
                                <option @(Model.SaveMmsEncodingType == 0 ? "selected" : "") value="0">系统默认编码</option>
                                <option @(Model.SaveMmsEncodingType == 1 ? "selected" : "") value="1">utf-8编码</option>
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">靓号拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="GoodMobileVerify" class="select-box">
                                <option @(Model.GoodMobileVerify == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.GoodMobileVerify == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">靓号拦截规则：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("GoodRuleId", ((List<SelectListItem>)ViewBag.GoodMsisdnRule), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">启用自动补发：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsUserRestSend" class="select-box">
                                <option @(Model.IsUserRestSend == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.IsUserRestSend == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                            <label style="color:red">注：是否开启自动补发功能 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">自动补发规则：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("RestSendId", ((List<SelectListItem>)ViewBag.RestSendRule), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">启用自定义端口签名报备：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsCustomPortSign" class="select-box">
                                <option @(Model.IsCustomPortSign == 0 ? "selected" : "") value="0">不启用</option>
                                <option @(Model.IsCustomPortSign == 1 ? "selected" : "") value="1">启用</option>
                            </select>
                            <label style="color:red">注：针对自定义端口签名扩展不能超过指定数量的情况使用 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">自定义端口签名报备未匹配操作：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsCustomPortOper" class="select-box">
                                <option @(Model.IsCustomPortOper == 0 ? "selected" : "") value="0">不操作</option>
                                <option @(Model.IsCustomPortOper == 1 ? "selected" : "") value="1">标记</option>
                                <option @(Model.IsCustomPortOper == 2 ? "selected" : "") value="2">失败</option>
                            </select>
                            <label style="color:red">注：针对自定义端口签名扩展不能超过指定数量的情况使用，失败状态码：1017 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">外部黑名单：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="BLACKVERIFY" id="BLACKVERIFY" v-model="BLACKVERIFY" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.BlackVerify == 0 ? "selected" : "")>默认</option>
                                <option value="1" @(Model.BlackVerify == 1 ? "selected" : "")>不验证</option>
                                <option value="2" @(Model.BlackVerify == 2 ? "selected" : "")>验证</option>
                            </select>
                            <label style="color:red">注：黑名单验证功能启用才生效！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">屏蔽地区：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="BlockNdcVerify" id="BlockNdcVerify" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.BlockNdcVerify == 0 ? "selected" : "")>不验证</option>
                                <option value="1" @(Model.BlockNdcVerify == 1 ? "selected" : "")>验证</option>
                            </select>
                            <label style="color:red">注：客户账号屏蔽地区，不进明细，返回1061！ </label>
                        </div>
                    </div>
                    <div class="row cl" id="pingbisheng">
                        <label class="form-label col-xs-4 col-sm-2">屏蔽省份：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @if (ViewBag.provs != null)
                            {
                                var p = ((Dictionary<int, string>)ViewBag.provs);
                                foreach (var item in p.Keys)
                                {
                                    <input type="checkbox" name="ProvinceIds1" id="ProvinceIds1idid+@item" value="@item" onclick="provclick(this)" /> @p[item].ToString();
                                }
                            }
                            <input type="hidden" value="@Model.ProvinceIds" name="ProvinceIds" id="ProvinceIds1id" />
                        </div>
                    </div>
                    <div class="row cl" id="pingbishi">
                        <label class="form-label col-xs-4 col-sm-2">屏蔽地市：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @if (ViewBag.provs != null)
                            {
                                var p = ((Dictionary<int, string>)ViewBag.citys);
                                foreach (var item in p.Keys)
                                {
                                    <input type="checkbox" name="CityIds1" id="CityIds1idid+@item" value="@item" onclick="cityclick(this)" /> @p[item].ToString();
                                }
                            }
                            <input type="hidden" value="@Model.CityIds" name="CityIds" id="CityIds1id" />
                        </div>
                    </div>
                    @*<div v-show="blackusername" class="row cl">
            <label class="form-label col-xs-4 col-sm-2">接口账号：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="text" class="input-text" value="@Model.BlackUserName" placeholder="" name="BlackUserName">
            </div>
        </div>
        <div v-show="blackuserpassword" class="row cl">
            <label class="form-label col-xs-4 col-sm-2">接口密码：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="text" class="input-text" value="@Model.BlackUserPassWord" placeholder="" name="BlackUserPassWord">
            </div>
        </div>
        <div v-show="blacklink" class="row cl">
            <label class="form-label col-xs-4 col-sm-2">接口地址：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="text" class="input-text" value="@Model.BlackLink" placeholder="" name="BlackLink">
            </div>
        </div>
        <div v-show="blackformat" class="row cl">
            <label class="form-label col-xs-4 col-sm-2">接口格式：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="text" class="input-text" value="@Model.BlackFormat" placeholder="" name="BlackFormat">
            </div>
        </div>
        <div v-show="blackquestcount" class="row cl">
            <label class="form-label col-xs-4 col-sm-2">请求数量：</label>
            <div class="formControls col-xs-8 col-sm-9">
                <input type="text" class="input-text" value="@Model.BlackQuestCount" placeholder="" name="BLACKQUESTCOUNT">
            </div>
        </div>*@
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">状态上行获取方式：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @*@Html.DropDownList("RECEIVEMODE", ((List<SelectListItem>)ViewBag.ReceiveMode))*@
                            <select name="RECEIVEMODE" id="RECEIVEMODE" v-model="receivemode" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.ReceiveMode == 0 ? "selected" : "")>不需要</option>
                                <option value="1" @(Model.ReceiveMode == 1 ? "selected" : "")>主动获取</option>
                                <option value="2" @(Model.ReceiveMode == 2 ? "selected" : "")>推送</option>
                                <option value="3" @(Model.ReceiveMode == 3 ? "selected" : "")>直连</option>
                            </select>
                            <label style="color:red">注：表示接口用户获取状态报告和上行的方式！ </label>
                        </div>
                    </div>
                    <div v-show="moaddress" class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">上行接收地址：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.PUSHMOURL" placeholder="" id="PUSHMOURL" name="PUSHMOURL">
                        </div>
                    </div>
                    <div v-show="reportaddrss" class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">状态接收地址：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.PUSHREPORTURL" placeholder="" id="PUSHREPORTURL" name="PUSHREPORTURL">
                        </div>
                    </div>
                    <div v-show="reportaddrss" class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">模板审核状态接收地址：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="text" class="input-text" value="@Model.TEMPLATEPUSHREPORTURL" placeholder="" id="TEMPLATEPUSHREPORTURL" name="TEMPLATEPUSHREPORTURL">
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">支持省份：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("SENDPROVINCEID", ((List<SelectListItem>)ViewBag.proList), new { style = "width:100%" })
                            <label style="color:red">注：客户账号支持省份，不进明细，返回1061！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">状态重推：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="RetryPush" id="RetryPush" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.RetryPush == 0 ? "selected" : "")>重推</option>
                                <option value="1" @(Model.RetryPush == 1 ? "selected" : "")>不需要</option>
                            </select>
                            <label style="color:red">注：1020状态自动重推 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">指定单网号码：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @*@Html.DropDownList("RECEIVEMODE", ((List<SelectListItem>)ViewBag.ReceiveMode))*@
                            <select name="SINGLENET" id="SINGLENET" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.SINGLENET == 0 ? "selected" : "")>不指定</option>
                                <option value="1" @(Model.SINGLENET == 1 ? "selected" : "")>移动</option>
                                <option value="2" @(Model.SINGLENET == 2 ? "selected" : "")>联通</option>
                                <option value="3" @(Model.SINGLENET == 3 ? "selected" : "")>电信</option>
                            </select>
                            <label style="color:red">注：一旦勾选了后，此账号提交的所有号码都改为指定的运营商！ </label>
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">彩信报备关键词拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="IsSaveUseKeyWordFaild" id="IsSaveUseKeyWordFaild" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.IsSaveUseKeyWordFaild == 0 ? "selected" : "")>不拦截</option>
                                <option value="1" @(Model.IsSaveUseKeyWordFaild == 1 ? "selected" : "")>拦截</option>
                            </select>
                            <label style="color:red">注：验证客户提交的彩信模板里的文本内容，返回状态码：1014</label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">彩信报备拦截关键词：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <textarea class="textarea" id="SaveKeyWordFailds" name="SaveKeyWordFailds" style="width:100%">@Model.SaveKeyWordFailds</textarea>
                            <label style="color:red">注：验证客户提交的彩信模板里的文本内容，返回状态码：1014，多个关键字用英文逗号（,）隔开</label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">提交是否拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <select name="ISSTOP" id="ISSTOP" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.ISSTOP == 0 ? "selected" : "")>不拦截</option>
                                <option value="1" @(Model.ISSTOP == 1 ? "selected" : "")>拦截</option>
                            </select>
                            <label style="color:red">注：一旦勾选了后，此账号提交的所有信息都拦截，提交不到平台！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">发送时间段拦截：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @*@Html.DropDownList("RECEIVEMODE", ((List<SelectListItem>)ViewBag.ReceiveMode))*@
                            <select name="ISCTS" id="ISCTS" class="input-text select select-box" style="width:100%">
                                <option value="0" @(Model.ISCTS == 0 ? "selected" : "")>不开启</option>
                                <option value="1" @(Model.ISCTS == 1 ? "selected" : "")>开启</option>
                            </select>
                            <label style="color:red">注：一旦勾选了后，此账号在非允许时间段提交的所有信息都拦截，提交不到平台！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">发送时间段：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="number" class="input-text" value="@Model.CTSSTARTTIME" placeholder="" id="CTSSTARTTIME" name="CTSSTARTTIME">-
                            <input type="number" class="input-text" value="@Model.CTSENDTIME" placeholder="" id="CTSENDTIME" name="CTSENDTIME">
                            <label style="color:red">注：0不限制 </label>
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">免黑关键字：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <textarea class="textarea" id="NOBLACKKEYWORD" name="NOBLACKKEYWORD" style="width:100%">@Model.NoblackKeyword</textarea>
                            <label style="color:red">注：多个关键字用英文逗号隔开（,）！ </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">短信通道组：</label>
                        <div class="formControls col-xs-8 col-sm-9">

                            @Html.DropDownList("ProductSmsId", ((List<SelectListItem>)ViewBag.SmsProductList), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">闪信通道组：</label>
                        <div class="formControls col-xs-8 col-sm-9">

                            @Html.DropDownList("ProductFmsId", ((List<SelectListItem>)ViewBag.FmsProductList), new { style = "width:100%" })
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">彩信通道组：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            @Html.DropDownList("ProductMmsId", ((List<SelectListItem>)ViewBag.MmsProductList), new { style = "width:100%" })
                        </div>
                    </div>
                </div>
                <!--Cmpp-->
                <div class="layui-tab-item">
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>账号：</label>
                        <div class="formControls col-xs-8 col-sm-5">
                            <input type="text" class="input-text" v-model="username" value="" placeholder="" id="UserName2" autocomplete="off" name="UserName1">
                            <label id="namelable"></label>
                            <label style="color:red">注：账号需6位</label>
                        </div>
                        <div class="formControls col-xs-5 col-sm-5 sh-container">
                            <div class="layui-tab">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">描述</li>
                                    <li>扩展重复用户</li>
                                    @*<li>HTTP</li>*@
                                </ul>
                                <div class="layui-tab-content">
                                    <div class="layui-tab-item layui-show">
                                        <div class="layui-row layui-col-space5">
                                            <div class="layui-col-md4">

                                                <div class="layui-card-header">描述：<button style="margin-left:185px" class="btn" type="button" id="copy">复制</button></div>
                                                <div class="layui-card-body">
                                                    <div class="textarea" id="descption" style="height:200px;border:solid 0px;width:400px">
                                                        服务地址：@ViewBag.ip<br />
                                                        端口：7890<br />
                                                        账号/企业代码：{{username}}<br />
                                                        密码/登陆密码：{{secret}}<br />
                                                        扩展/接入代码：{{extnumber}}<br />
                                                        绑定Ip:{{userhostaddress}}<br />
                                                        链接数:{{connectcount}}<br />
                                                        速度:{{rate}}<br />
                                                        线程数:{{threadcount}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div class="layui-row layui-col-space5">
                                            <div class="layui-col-md4">
                                                <div class="layui-card-header">账号：<button style="margin-left:185px" class="btn" type="button" id="copy1">复制</button></div>
                                                <div class="layui-card-body">
                                                    <div class="textarea" id="descption1" style="height:200px;border:solid 0px;width:400px">
                                                        <ul>
                                                            <li v-for="e in extnumberarr">{{e}}</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>密文：</label>
                        <div class="formControls col-xs-8 col-sm-5">
                            <input type="password" class="input-text" placeholder="***************" value="" v-model="secret" id="SECRET" name="SECRET">
                            <label style="color:red">注：直连客户使用</label>
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2" title="扩展号规则：主账号扩展号生成1、2、3、5、7，扩展5位，子账号从父级账号扩展3位，从1开始。列如：100001、100002">接入代码：</label>
                        <div class="formControls col-xs-8 col-sm-5">
                            <input type="text" class="input-text" value="" v-model="extnumber" placeholder="" id="ExtNumber" name="ExtNumber">
                            <input type="text" class="input-text" style="display:none" value="@(Model.ExtNumber == null ? "" : Model.ExtNumber)" placeholder="" id="ExtNumber1" name="ExtNumber1">
                            <label style="color:red" v-show="extnumbertips">接入代码已存在</label>
                        </div>
                    </div>

                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>链接数：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="number" class="input-text" name="CONNECTCOUNT" id="CONNECTCOUNT" v-model="connectcount" value="" />
                            <label style="color:red">注：支持的最大链接数 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>速度：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="number" class="input-text" name="RATE" id="RATE" v-model="rate" value="" />
                            <label style="color:red">注：单个链接的速度，多连接的实际速度=链接数*速度 </label>
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>线程数：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <input type="number" class="input-text" name="THREADCOUNT" id="THREADCOUNT" v-model="threadcount" value="" />
                        </div>
                    </div>
                    <div class="row cl">
                        <label class="form-label col-xs-4 col-sm-2">客户绑定IP：</label>
                        <div class="formControls col-xs-8 col-sm-9">
                            <textarea class="textarea" id="USERHOSTADDRESS" name="USERHOSTADDRESS" v-model="userhostaddress" style="width:100%">{{userhostaddress}}</textarea>
                            <label style="color:red">注：如果默认为空，表示不绑定IP,多个IP用英文逗号隔开 </label>
                        </div>
                    </div>
                </div>
                <!--Http-->
                @*<div class="layui-tab-item">
                        <div class="row cl">
                            <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>账号：</label>
                            <div class="formControls col-xs-8 col-sm-5">
                                <input type="text" class="input-text" v-model="username" value="" placeholder="" id="UserName1" autocomplete="off" name="UserName1">
                                <label id="namelable"></label>
                                <label style="color:red">注：账号需6位</label>
                            </div>
                            <div class="formControls col-xs-5 col-sm-5 sh-container">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md5">

                                        <div class="layui-card-header">描述：</div>
                                        <div class="layui-card-body">
                                            <p>
                                                用户Id：{{userid}}<br />
                                                账号：{{username}}<br />
                                                密码：{{secret}}<br />
                                                扩展：{{extnumber}}<br />
                                                绑定Ip:{{userhostaddress}}<br />
                                                客户端：*************:8080
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row cl">
                            <label class="form-label col-xs-4 col-sm-2"><span class="c-red">*</span>接口密码：</label>
                            <div class="formControls col-xs-8 col-sm-9">
                                <input type="password" class="input-text" value="" placeholder="***************" id="TOKEN" name="TOKEN">
                            </div>
                        </div>
                        <div class="row cl">
                            <label class="form-label col-xs-4 col-sm-2" title="扩展号规则：主账号扩展号生成1、2、3、5、7，扩展5位，子账号从父级账号扩展3位，从1开始。列如：100001、100002">扩展号：</label>
                            <div class="formControls col-xs-8 col-sm-9">
                                <input type="text" class="input-text" value="" v-model="extnumber" placeholder="" id="ExtNumber2" name="ExtNumber2">
                                <label style="color:red" v-show="extnumbertips">扩展码已存在</label>
                            </div>
                        </div>
                        <div class="row cl">
                            <label class="form-label col-xs-4 col-sm-2">客户绑定IP：</label>
                            <div class="formControls col-xs-8 col-sm-9">
                                <textarea class="textarea" id="USERHOSTADDRESS1" name="USERHOSTADDRESS1" v-model="userhostaddress" style="width:100%">{{userhostaddress}}</textarea>
                                <label style="color:red">注：如果默认为空，表示不绑定IP,多个IP用英文逗号隔开 </label>
                            </div>
                        </div>
                    </div>*@
                @*<form class="form form-horizontal" id="form-client-add">*@


                @if (ViewBag.look == null)
                {
                    <div class="row cl">
                        <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-2">
                            <button onClick="" class="btn btn-primary radius" type="submit"><i class="Hui-iconfont">&#xe632;</i> 保存并提交</button>
                            @*<button onClick="article_save();" class="btn btn-secondary radius" type="button"><i class="Hui-iconfont">&#xe632;</i> 保存草稿</button>*@
                            <button onClick="layer_close()" class="btn btn-default radius" type="button">&nbsp;&nbsp;取消&nbsp;&nbsp;</button>
                        </div>
                    </div>
                }

            </div>

        </div>
    }
</article>


<!--请在下方写此页面业务相关的脚本-->
<script src="~/Scripts/jquery.validation/1.14.0/jquery.validate.js"></script>
<script src="~/Scripts/jquery.validation/1.14.0/validate-methods.js"></script>
<script src="~/Scripts/jquery.validation/1.14.0/messages_zh.js"></script>
<script type="text/javascript" src="~/Scripts/MyScript/Jquery.Customer.js"></script>
<script src="~/Scripts/Client/UpdateCustomer.js"></script>
<script src="~/Content/layui/layui.js"></script>
<script src="~/Content/vue.js"></script>
<script>


    layui.use('element', function () {

    });

    var vm = new Vue({
        el: '#app',
        data: () =>{
            return {
                username: '@(Model.UserName == null ? "" : Model.UserName)',
                password: '',
                secret:'@(Model.Secret == null ? "" : Model.Secret)',
                extnumber: '@(Model.ExtNumber == null ? "" : Model.ExtNumber)',
                extnumberold: '@(Model.ExtNumber == null ? "" : Model.ExtNumber)',
                extnumbertips: false,
                connectcount: '@Model.ConnectCount',
                userhostaddress: '@(Model.UserHostAddress == null ? "" : Model.UserHostAddress)',
                threadcount: '@Model.ThreadCount',
                rate: '@Model.Rate',
                receivemode:'@Model.ReceiveMode',
                moaddress: false,
                reportaddrss: false,
                BLACKVERIFY: '@Model.BlackVerify',
                Signverify: '@Model.Signverify',
                qiangsign: false,
                blackformat: false,
                blacklink: false,
                blackuserpassword: false,
                blackusername: false,
                blackquestcount: false,
                usenumberlimit: '@Model.UseNumberLimit',
                showusenumberlimit: false,
                usecontentlimit: '@Model.UseContentLimit',
                showusecontentlimit: false,
                extnumberarr: [],
                usemsisdnmaclimit: '@Model.USEMSISDNMACLIMIT',
                showusemsisdnmaclimit: false,
            }
        },
        watch: {
            extnumber: (n, o) => {
                var self = vm;
                if (n != self.extnumberold) {
                    $.ajax({
                        type: 'POST',
                        url: '/Client/ExtNumberBool',
                        data: { ext: n },
                        dataType: 'json',
                        success: function (result) {
                            if (result.message == "1") {//存在
                                self.extnumbertips = true;
                                $.ajax({
                                    type: 'post',
                                    url: '/Client/GetExtNumberClient',
                                    data: { ext: n },
                                    dataType: 'json',
                                    success: function (d) {
                                        console.log(d);
                                        self.extnumberarr = d;
                                    }
                                });
                            } else {
                                self.extnumbertips = false;
                                self.extnumberarr = [];
                            }
                        },
                        error: function (data) {
                            console.log(data.message);
                        }
                    });
                } else {
                    self.extnumbertips = false;
                }

            },
            receivemode: (n) => {
                var self = vm;
                if (n == 2) {
                    self.moaddress = true;
                    self.reportaddrss = true;
                } else {
                    self.moaddress = false;
                    self.reportaddrss = false;
                }
            },
            Signverify: (c) => {
                var self = vm;
                if (c == 3) {
                    self.qiangsign = true;
                } else {
                    self.qiangsign = false;
                }
            },
            BLACKVERIFY: (z) => {
                var self = vm;
                if (z == 2) {
                    self.blackformat = true;
                    self.blacklink = true;
                    self.blackusername = true;
                    self.blackuserpassword = true;
                    self.blackquestcount = true;
                } else {
                    self.blackformat = false;
                    self.blacklink = false;
                    self.blackusername = false;
                    self.blackuserpassword = false;
                    self.blackquestcount = false;
                }
            },
            usenumberlimit: (x) => {
                var self = vm;
                if (x == 0) {
                    self.showusenumberlimit = true;
                } else {
                    self.showusenumberlimit = false;
                }
            },
            usecontentlimit: (x) => {
                var self = vm;
                if (x >0 ) {
                    self.showusecontentlimit = true;
                } else {
                    self.showusecontentlimit = false;
                }
            },
            usemsisdnmaclimit: (x) => {
                var self = vm;
                if (x > 0) {
                    self.showusemsisdnmaclimit = true;
                } else {
                    self.showusemsisdnmaclimit = false;
                }
            },
        }
        , created: function () {

            if (this.receivemode==2) {
                this.moaddress = true;
                this.reportaddrss = true;
            }
            if (this.BLACKVERIFY==2) {
                this.blackformat = true;
                this.blacklink = true;
                this.blackusername = true;
                this.blackuserpassword = true;
                this.blackquestcount = true;
            }
            if (this.Signverify == 3) {
                this.qiangsign = true;
            }
            if (this.usenumberlimit == 0) {
                this.showusenumberlimit = true;
            }
            if (this.usecontentlimit >0) {
                this.showusecontentlimit = true;
            }
            if (this.usemsisdnmaclimit > 0) {
                this.showusemsisdnmaclimit = true;
            }
        }

    });
    $(function () {
        $("#GoodRuleId").select2();
        $("#RestSendId").select2();
        console.log("扩展号规则：\n\t扩展号生成1、2、3、5、7，列如：100001、100002，主扩展5位，子账号从父级账号扩展3位，从1开始。");
        replacesales('@Model.SalesId');
        $("#SENDPROVINCEID").select2();
        var bv = $("#BlockNdcVerify").val();
        if (bv == 0) {
            $("#pingbisheng").hide();
            $("#pingbishi").hide();
        }

        if ($("#CityIds1id").val()!="") {
            citys = $("#CityIds1id").val().split(',');
        }
        if ($("#ProvinceIds1id").val()!="") {
            provs = $("#ProvinceIds1id").val().split(',');
        }


        provs.forEach(function (value, index) {
            if (value != undefined && value != "") {
                document.getElementById("ProvinceIds1idid+" + value).setAttribute("checked", "checked");
            }
        });

        citys.forEach(function (value, index) {
            if (value != undefined && value != "") {
                document.getElementById("CityIds1idid+" + value).setAttribute("checked", "checked");
            }
        });


        // 初始化地区频次限制计数
        msisdnnumberoco = parseInt($("#msisdnnumbert").val() || "0");

        // 添加省份和城市多频次限制的初始化
        if ($("#PROVINCELIMITid").val() != "") {
            provlimits = $("#PROVINCELIMITid").val().split(',');
        }
        if ($("#CITYSLIMITid").val() != "") {
            citylimits = $("#CITYSLIMITid").val().split(',');
        }

        // 初始化省份选中状态
        provlimits.forEach(function (value, index) {
            if (value != undefined && value != "") {
                var element = document.getElementById("PROVINCELIMIT1_" + value);
                if (element) {
                    element.checked = true;
                }
            }
        });

        // 初始化城市选中状态
        citylimits.forEach(function (value, index) {
            if (value != undefined && value != "") {
                var element = document.getElementById("CITYSLIMIT1_" + value);
                if (element) {
                    element.checked = true;
                }
            }
        });


    })
    //定义函数
    window.Clipboard = (function (window, document, navigator) {
        var textArea,
            copy;

        // 判断是不是ios端
        function isOS() {
            return navigator.userAgent.match(/ipad|iphone/i);
        }
        //创建文本元素
        function createTextArea(text) {
            textArea = document.createElement('textArea');
            textArea.value = text;
            document.body.appendChild(textArea);
        }
        //选择内容
        function selectText() {
            var range,
                selection;

            if (isOS()) {
                range = document.createRange();
                range.selectNodeContents(textArea);
                selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                textArea.setSelectionRange(0, 999999);
            } else {
                textArea.select();
            }
        }

        //复制到剪贴板
        function copyToClipboard() {
            try {
                if (document.execCommand("Copy")) {
                    layer.alert("复制成功！");
                } else {
                    layer.alert("复制失败！请手动复制！");
                }
            } catch (err) {
                layer.alert("复制错误！请手动复制！")
            }
            document.body.removeChild(textArea);
        }

        copy = function (text) {
            createTextArea(text);
            selectText();
            copyToClipboard();
        };

        return {
            copy: copy
        };
    })(window, document, navigator);

    $("#BlockNdcVerify").on('change', function () {
        if ($(this).val() == "0") {
            $("#pingbisheng").hide();
            $("#pingbishi").hide();
        } else {
            $("#pingbisheng").show();
            $("#pingbishi").show();
        }
    });

    //使用函数
    $("#copy").on("click", function () {
        var val = $("#descption").text().replace(/\s+/g, "\n");;
        Clipboard.copy(val);
    });
    //使用函数
    $("#copy1").on("click", function () {
        var val = $("#descption1").text().replace(/\s+/g, "\n");;
        Clipboard.copy(val);
    });
    var provs = [];
    function provclick(obj) {
        var valv = obj.value;
        if (valv != null && obj.checked) {
            if (!provs.includes(valv)) {
                provs.push(valv);
            }
        } else {
            var index = provs.indexOf(valv);
            provs.splice(index, 1);
        }
        $("#ProvinceIds1id").val(provs.join(","));
    }
    var citys = [];
    function cityclick(obj) {
        var valv = obj.value;
        if (valv != null && obj.checked) {
            if (!citys.includes(valv)) {
                citys.push(valv);
            }
        } else {
            var index = citys.indexOf(valv);
            citys.splice(index,1);
        }
        $("#CityIds1id").val(citys.join(","));
    }
    var dict = [];

    var numberoco = @Model.numbercountt;
    $("#btnadd").on("click", function () {
        var html = "";
        html += '<select name="numberoco' + numberoco +'" class="select select-box">';

        @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit))) {

            @:html += "<option value='@item.Name'>@item.Text</option>";
        }

        html += "</select>";
        $("#numbers").append('<tr><td><input style="width:100%" class="input-text" type="number" name="periods' + numberoco + '"/></td><td>' + html + '</td><td><input style="width:100%" class="input-text" type="number" name="counts' + numberoco +'"/></td><td><button type="button" onclick="btntr_del(this)" class="ui-button">删除</button></td></tr>')
        numberoco += 1;
        $("#numbert").val(numberoco);

        for (var i = 0; i < $("#numbers")[0].childNodes.length; i++) {
            var childs=$("#numbers")[0].childNodes;
            //console.log(childs[0]);
        }
    })

    $("#contentbtnadd").on("click", function () {
        var html = "";
        html += '<select name="contentnumberoco' + numberoco +'" class="select select-box">';

        @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit))) {

            @:html += "<option value='@item.Name'>@item.Text</option>";
        }

        html += "</select>";
        $("#contens").append('<tr><td><input style="width:100%" class="input-text" type="number" name="contentperiods' + numberoco + '"/></td><td>' + html + '</td><td><input style="width:100%" class="input-text" type="number" name="contentcounts' + numberoco +'"/></td><td><button type="button" onclick="contentbtntr_del(this)" class="ui-button">删除</button></td></tr>')
        numberoco += 1;
        $("#contentnumbert").val(numberoco);

        for (var i = 0; i < $("#contens")[0].childNodes.length; i++) {
            var childs = $("#contens")[0].childNodes;
            //console.log(childs[0]);
        }
    })

    function btntr_del(obj) {
        $(obj).parents("tr").remove();
    }
    function contentbtntr_del(obj) {
        $(obj).parents("tr").remove();
    }
    function batchupdate() {
        var CHILDEXTNUMBER = $("#CHILDEXTNUMBER").val();
        var NewCHILDEXTNUMBER = $("#NEWCHILDEXTNUMBER").val();
        if (CHILDEXTNUMBER != "" && NewCHILDEXTNUMBER != "") {
            layer.confirm("确定要将" + CHILDEXTNUMBER + "批量替换成：" + NewCHILDEXTNUMBER + " 吗？", function (index) {
                $.ajax({
                    type: 'post',
                    url: '/Client/BatchUpdateClientChildExtNumber',
                    data: { ChildnNmber: NewCHILDEXTNUMBER, OldChildnNmber: CHILDEXTNUMBER },
                    dataType: 'json',
                    success: function (d) {
                        console.log(d);
                        if (d == 1) {
                            layer.alert("批量修改成功！");
                        } else {
                            layer.alert("批量修改失败！");
                        }
                    }
                });
            });
        }
        else {
            layer.alert("请填写要修改的子扩展号！");
        }
    }
    var msisdnnumberoco = 0;
$("#msisdnbtnadd").on("click", function () {
    var html = "";
    html += '<select name="msisdnnumberoco' + msisdnnumberoco +'" class="select select-box">';

    @foreach (var item in EnumMethods.GetItems(typeof(NumberLimitUnit))) {
        @:html += "<option value='@item.Name'>@item.Text</option>";
    }

    html += "</select>";
    $("#msisdnlimits").append('<tr><td><input style="width:100%" class="input-text" type="number" name="msisdnperiods' + msisdnnumberoco + '"/></td><td>' + html + '</td><td><input style="width:100%" class="input-text" type="number" name="msisdncounts' + msisdnnumberoco +'"/></td><td><button type="button" onclick="msisdnbtntr_del(this)" class="ui-button">删除</button></td></tr>')
    msisdnnumberoco += 1;
    $("#msisdnnumbert").val(msisdnnumberoco);
});

function msisdnbtntr_del(obj) {
    $(obj).parents("tr").remove();
}

var provlimits = [];
function provlimitclick(obj) {
    var valv = obj.value;
    if (valv != null && obj.checked) {
        if (!provlimits.includes(valv)) {
            provlimits.push(valv);
        }
    } else {
        var index = provlimits.indexOf(valv);
        if (index !== -1) {
            provlimits.splice(index, 1);
        }
    }
    $("#PROVINCELIMITid").val(provlimits.join(","));
}

var citylimits = [];
function citylimitclick(obj) {
    var valv = obj.value;
    if (valv != null && obj.checked) {
        if (!citylimits.includes(valv)) {
            citylimits.push(valv);
        }
    } else {
        var index = citylimits.indexOf(valv);
        if (index !== -1) {
            citylimits.splice(index, 1);
        }
    }
    $("#CITYSLIMITid").val(citylimits.join(","));
}

    function addnumber() {

    }
    function addnumber1() {
        console.log(1);
    }
</script>