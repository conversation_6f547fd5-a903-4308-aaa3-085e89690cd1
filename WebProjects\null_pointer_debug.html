<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>空指针错误调试指南</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-example {
            background-color: #f8f8f8;
            padding: 10px;
            border-left: 4px solid #d9534f;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 空指针错误调试指南</h1>
        
        <div class="debug-section">
            <h3>🎯 问题确认</h3>
            <div class="alert alert-danger">
                <strong>发现：</strong>第二次上传同一文件时出现空指针错误，说明文件流为null或空。
            </div>
        </div>

        <div class="debug-section">
            <h3>✅ 已添加的调试代码</h3>
            <h4>服务器端检查：</h4>
            <ul>
                <li>检查 Request.Files.Count</li>
                <li>检查 file 对象是否为null</li>
                <li>检查 file.ContentLength</li>
                <li>检查 file.InputStream 是否为null</li>
                <li>详细的日志输出</li>
            </ul>
            
            <h4>前端检查：</h4>
            <ul>
                <li>检查 fileInput.files 是否存在</li>
                <li>检查选中文件的详细信息</li>
                <li>验证 FormData 内容</li>
                <li>不自动清除文件选择</li>
            </ul>
        </div>

        <div class="debug-section">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>打开浏览器开发者工具</strong>（F12）</li>
                <li><strong>进入导入页面</strong></li>
                <li><strong>第一次上传</strong>：
                    <ul>
                        <li>选择通道1</li>
                        <li>选择Excel文件</li>
                        <li>点击导入</li>
                        <li>观察控制台日志和服务器响应</li>
                    </ul>
                </li>
                <li><strong>第二次上传</strong>：
                    <ul>
                        <li>选择通道2</li>
                        <li><strong>不要重新选择文件</strong></li>
                        <li>直接点击导入</li>
                        <li>观察是否出现空指针错误</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="debug-section">
            <h3>📊 预期的日志输出</h3>
            
            <h4>浏览器控制台应该显示：</h4>
            <div class="log-example">[前端] 文件输入检查：
  fileInput: <input type="file" ...>
  fileInput.files: FileList {0: File, length: 1}
  fileInput.files.length: 1
  fileInput.value: C:\fakepath\test.xlsx
  选中的文件：
    名称： test.xlsx
    大小： 12345
    类型： application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    最后修改： 2024/1/1 上午10:00:00
[前端import_abc123] 文件已添加到FormData： test.xlsx
[前端import_abc123] FormData内容：
  timestamp: 1704067200000
  importId: import_abc123
  file: File(test.xlsx, 12345 bytes)
  channelId: 2</div>

            <h4>服务器日志应该显示：</h4>
            <div class="log-example">[导入abc12345] Request.Files.Count: 1
[导入abc12345] 文件对象检查:
  file == null: False
  file.ContentLength: 12345
  file.FileName: 'test.xlsx'
  file.ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  file.InputStream == null: False</div>
        </div>

        <div class="debug-section">
            <h3>🚨 如果仍然出现空指针错误</h3>
            
            <h4>可能的原因：</h4>
            <ol>
                <li><strong>浏览器文件缓存</strong>：浏览器认为是重复文件，没有重新上传</li>
                <li><strong>ASP.NET文件处理</strong>：服务器端文件处理机制有问题</li>
                <li><strong>IIS配置</strong>：文件上传大小限制或超时设置</li>
                <li><strong>内存不足</strong>：服务器内存不足导致文件处理失败</li>
            </ol>
            
            <h4>需要检查的信息：</h4>
            <ul>
                <li>浏览器控制台的完整日志</li>
                <li>服务器日志中的详细错误信息</li>
                <li>Network标签中的请求详情</li>
                <li>文件大小和格式</li>
            </ul>
        </div>

        <div class="debug-section">
            <h3>🔧 临时解决方案</h3>
            <div class="alert alert-warning">
                <h4>如果问题持续存在，可以尝试：</h4>
                <ol>
                    <li><strong>刷新页面</strong>：每次上传前刷新页面</li>
                    <li><strong>重新选择文件</strong>：每次都重新选择文件，不要重复使用</li>
                    <li><strong>清除浏览器缓存</strong>：Ctrl+Shift+Delete</li>
                    <li><strong>使用不同浏览器</strong>：测试是否是浏览器特定问题</li>
                    <li><strong>重启IIS</strong>：重启应用程序池</li>
                </ol>
            </div>
        </div>

        <div class="debug-section">
            <h3>📝 需要提供的信息</h3>
            <div class="panel panel-default">
                <div class="panel-body">
                    <h4>如果问题仍然存在，请提供：</h4>
                    <ol>
                        <li><strong>浏览器控制台日志</strong>（完整的Console输出）</li>
                        <li><strong>Network标签信息</strong>：
                            <ul>
                                <li>请求头信息</li>
                                <li>请求体大小</li>
                                <li>响应状态码</li>
                                <li>响应内容</li>
                            </ul>
                        </li>
                        <li><strong>服务器日志</strong>（包含[导入xxxxxxxx]的所有日志）</li>
                        <li><strong>具体的错误消息</strong>（完整的异常堆栈）</li>
                        <li><strong>测试环境信息</strong>：
                            <ul>
                                <li>浏览器类型和版本</li>
                                <li>文件大小和格式</li>
                                <li>操作系统</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
