using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace YLHT.GP.Models
{
    /// <summary>
    /// 移动签名报备模型
    /// </summary>
    public class MobileSignReportModel
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        [Display(Name = "日期")]
        public DateTime? ReportDate { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        [Display(Name = "类型")]
        public string Type { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Display(Name = "客户名称")]
        public string AccountName { get; set; }

        /// <summary>
        /// 签名类型:企业简称/APP/商标
        /// </summary>
        [Display(Name = "签名类型")]
        public string SignType { get; set; }

        /// <summary>
        /// 签名
        /// </summary>
        [Display(Name = "签名")]
        [Required(ErrorMessage = "签名不能为空")]
        public string Sign { get; set; }

        /// <summary>
        /// 签名企业名称
        /// </summary>
        [Display(Name = "签名企业名称")]
        public string SignCompanyName { get; set; }

        /// <summary>
        /// 统一社会信用代码
        /// </summary>
        [Display(Name = "统一社会信用代码")]
        public string UnifiedSocialCreditCode { get; set; }

        /// <summary>
        /// 法人
        /// </summary>
        [Display(Name = "法人")]
        public string LegalPerson { get; set; }

        /// <summary>
        /// 法人/经办人姓名
        /// </summary>
        [Display(Name = "法人/经办人姓名")]
        public string LegalPersonName { get; set; }

        /// <summary>
        /// 法人/经办人身份证号
        /// </summary>
        [Display(Name = "法人/经办人身份证号")]
        public string LegalPersonIdCard { get; set; }

        /// <summary>
        /// 法人/经办人手机号
        /// </summary>
        [Display(Name = "法人/经办人手机号")]
        public string LegalPersonPhone { get; set; }

        /// <summary>
        /// 引流链接
        /// </summary>
        [Display(Name = "引流链接")]
        public string DrainageLink { get; set; }

        /// <summary>
        /// 引流号码1
        /// </summary>
        [Display(Name = "引流号码1")]
        public string DrainageNumber1 { get; set; }

        /// <summary>
        /// 引流号码2
        /// </summary>
        [Display(Name = "引流号码2")]
        public string DrainageNumber2 { get; set; }

        /// <summary>
        /// 签名材料
        /// </summary>
        [Display(Name = "签名材料")]
        public string SignMaterials { get; set; }

        /// <summary>
        /// 身份证照片
        /// </summary>
        [Display(Name = "身份证照片")]
        public string IdCardPhotos { get; set; }

        /// <summary>
        /// 扩展号
        /// </summary>
        [Display(Name = "扩展号")]
        public string ExtNumber { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        [Display(Name = "内容")]
        public string Content { get; set; }

        /// <summary>
        /// 通道ID
        /// </summary>
        [Display(Name = "通道")]
        public int ChannelId { get; set; }

        /// <summary>
        /// 通道名称
        /// </summary>
        [Display(Name = "通道名称")]
        public string ChannelName { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Display(Name = "用户")]
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Display(Name = "用户名")]
        public string UserName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Display(Name = "状态")]
        public int Status { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        [Display(Name = "添加时间")]
        public DateTime? AddTime { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        [Display(Name = "管理员")]
        public int AdminId { get; set; }

        /// <summary>
        /// 管理员名称
        /// </summary>
        [Display(Name = "管理员名称")]
        public string AdminName { get; set; }

        /// <summary>
        /// 签名状态
        /// </summary>
        [Display(Name = "签名状态")]
        public int SignStatus { get; set; }

        /// <summary>
        /// 批量移动签名报备模型
        /// </summary>
        public List<MobileSignReportModel> MobileSignReportModels { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }
        
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }
        
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }
        
        /// <summary>
        /// 客户端标识
        /// </summary>
        public int IsClient { get; set; }
        
        /// <summary>
        /// 文件目录
        /// </summary>
        public string FileDir { get; set; }
    }
} 